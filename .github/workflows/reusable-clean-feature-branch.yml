name: Reusable Cleanup Feature Branch Resources

on:
  workflow_call:
    inputs:
      branch_prefix:
        required: true
        type: string
      base_branch:
        required: true
        type: string
    secrets:
      GH_TOKEN:
        required: true
      AWS_REGION:
        required: true
      ASSUME_ROLE_ARN:
        required: true
      TFE_TOKEN:
        required: true
      ECR_REPOSITORY:
        required: true
      S3_BUCKET_NAME:
        required: true
      MONGODB_PROJECT_ID:
        required: true
      MONGODB_PUBLIC_KEY:
        required: true
      MONGODB_PRIVATE_KEY:
        required: true
      GYDA_KID:
        required: true
      WC_SOCIAL_DATA_API_KEY:
        required: true
      MONGODB_URI:
        required: true
      GRAPHQL_S31_KEY:
        required: true

env:
  TERRAFORM_VERSION: 1.8.2
  AWS_DEFAULT_REGION: ${{ secrets.AWS_REGION }}
  TF_TOKEN_app_terraform_io: ${{ secrets.TFE_TOKEN }}
  ECR_REPOSITORY: ${{ secrets.ECR_REPOSITORY }}
  S3_BUCKET_NAME: ${{ secrets.S3_BUCKET_NAME }}
  MONG<PERSON>B_PROJECT_ID: ${{ secrets.MONGODB_PROJECT_ID }}
  MONGODB_PUBLIC_KEY: ${{ secrets.MONGODB_PUBLIC_KEY }}
  MONGODB_PRIVATE_KEY: ${{ secrets.MONGODB_PRIVATE_KEY }}
  GYDA_KID: ${{ secrets.GYDA_KID }}
  WC_SOCIAL_DATA_API_KEY: ${{ secrets.WC_SOCIAL_DATA_API_KEY }}
  MONGODB_URI: ${{ secrets.MONGODB_URI }}
  GRAPHQL_S31_KEY: ${{ secrets.GRAPHQL_S31_KEY }}

permissions:
  id-token: write
  contents: read
  pull-requests: write

jobs:
  cleanup-resources:
    runs-on: [self-hosted, linux, arm64, prod]
    if: startsWith(github.head_ref, inputs.branch_prefix) && github.base_ref == inputs.base_branch
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GH_TOKEN }}

      - name: Set branch name
        run: |
          echo "BRANCH_NAME=${GITHUB_HEAD_REF}" >> $GITHUB_ENV
          echo "WORKSPACE=${GITHUB_HEAD_REF}" >> $GITHUB_ENV

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.ASSUME_ROLE_ARN }}
          aws-region: ${{ env.AWS_DEFAULT_REGION }}
          role-session-name: GitHubActionsOIDC

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3.1.2
        with:
          terraform_version: ${{ env.TERRAFORM_VERSION }}

      - name: Terraform Init
        run: |
          terraform -chdir=infrastructure/services init -reconfigure

      - name: Select Terraform Workspace
        run: |
          terraform -chdir=infrastructure/services workspace select "$WORKSPACE"

      - name: Terraform Destroy
        run: |
          terraform -chdir=infrastructure/services destroy -auto-approve \
            -var="mongodb_project_id=${MONGODB_PROJECT_ID}" \
            -var="mongodbatlas_public_key=${MONGODB_PUBLIC_KEY}" \
            -var="mongodbatlas_private_key=${MONGODB_PRIVATE_KEY}" \
            -var="gyda_kid=${GYDA_KID}" \
            -var="WC_SOCIAL_DATA_API_Key=${WC_SOCIAL_DATA_API_KEY}" \
            -var="mongodb_uri=${MONGODB_URI}" \
            -var="graphql_s31_key=${GRAPHQL_S31_KEY}"
        continue-on-error: true

      - name: Delete Terraform Workspace
        run: |
          terraform -chdir=infrastructure/services workspace select default
          terraform -chdir=infrastructure/services workspace delete -force "$WORKSPACE"
        continue-on-error: true

      - name: Clean up S3 artifacts
        run: |
          aws s3 rm s3://${S3_BUCKET_NAME}/ --recursive --exclude "*" --include "${BRANCH_NAME}*"
        continue-on-error: true

      - name: Update or Add Cleanup Status Comment
        run: |
          set -x  # Enable debug mode
          
          if [[ ${{ github.event.pull_request.merged }} == 'true' ]]; then
            echo "Pull request was merged. Skipping comment addition."
            exit 0
          fi
          
          PR_NUMBER=${{ github.event.pull_request.number }}
          echo "PR_NUMBER: $PR_NUMBER"
          
          NEW_COMMENT="🧹 Feature branch resources have been cleaned up and removed."
          
          echo "Fetching latest comment..."
          LATEST_COMMENT=$(gh pr view $PR_NUMBER --json comments --jq '.comments[-1].body' 2>&1)
          FETCH_EXIT_CODE=$?
          echo "Latest comment fetch exit code: $FETCH_EXIT_CODE"
          echo "Latest comment: $LATEST_COMMENT"
          
          if [ $FETCH_EXIT_CODE -ne 0 ]; then
            echo "Error fetching latest comment. Full output:"
            gh pr view $PR_NUMBER --json comments
            exit 1
          fi
          echo "Fetching latest comment..."
          LATEST_COMMENT=$(gh pr view $PR_NUMBER --json comments --jq '.comments[-1].body' 2>&1)
          FETCH_EXIT_CODE=$?
          echo "Latest comment fetch exit code: $FETCH_EXIT_CODE"
          echo "Latest comment: $LATEST_COMMENT"
          
          if [ $FETCH_EXIT_CODE -ne 0 ]; then
            echo "Error fetching latest comment. Full output:"
            gh pr view $PR_NUMBER --json comments
            exit 1
          fi
          
          if [ "$LATEST_COMMENT" != "$NEW_COMMENT" ]; then
            echo "Adding new comment..."
            COMMENT_RESULT=$(gh pr comment $PR_NUMBER --body "$NEW_COMMENT" 2>&1)
            COMMENT_EXIT_CODE=$?
            echo "Comment addition exit code: $COMMENT_EXIT_CODE"
            echo "Comment addition result: $COMMENT_RESULT"
            
            if [ $COMMENT_EXIT_CODE -ne 0 ]; then
              echo "Error adding comment. Full output:"
              echo "$COMMENT_RESULT"
              exit 1
            fi
            
            echo "Added new comment with cleanup information."
          else
            echo "Latest comment is already up to date with cleanup information. Skipping."
          fi
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
