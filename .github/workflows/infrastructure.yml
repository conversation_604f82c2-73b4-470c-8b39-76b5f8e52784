name: Call Reusable Infrastructure Workflow

on:
  push:
    branches:
      - main

permissions:
  id-token: write
  contents: read
  pull-requests: write

jobs:
  call-reusable-workflow:
    if: github.event.pull_request.merged == true || github.event_name == 'push'
    uses: ./.github/workflows/reusable-infrastructure.yml
    with:
      infrastructure-directory: |
        infrastructure/**
      terraform-version: 1.8.2
    secrets:
      TFE_TOKEN: ${{ secrets.TFE_TOKEN }}
      GH_TOKEN: ${{ secrets.GH_TOKEN }}
      AWS_REGION: ${{ secrets.AWS_REGION }}
      ASSUME_ROLE_ARN: ${{ secrets.ASSUME_ROLE_ARN }}
      MONGODB_PROJECT_ID: ${{ secrets.MONGODB_PROJECT_ID }}
      MONGODB_PUBLIC_KEY: ${{ secrets.MONGODB_PUBLIC_KEY }}
      MONGODB_PRIVATE_KEY: ${{ secrets.MONGODB_PRIVATE_KEY }}
      GY<PERSON>_KID: ${{ secrets.GYDA_KID }}
      WC_SOCIAL_DATA_API_KEY: ${{ secrets.WC_SOCIAL_DATA_API_KEY }}
      MONGODB_URI: ${{ secrets.MONGODB_URI }}
      GRAPHQL_S31_KEY: ${{ secrets.GRAPHQL_S31_KEY }}
