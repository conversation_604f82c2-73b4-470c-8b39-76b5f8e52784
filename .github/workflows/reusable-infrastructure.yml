name: Reusable Setup Infrastructure

on:
  workflow_call:
    inputs:
      infrastructure-directory:
        required: true
        type: string
      terraform-version:
        required: false
        type: string
    secrets:
      TFE_TOKEN:
        required: true
      GH_TOKEN:
        required: true
      AWS_REGION:
        required: true
      ASSUME_ROLE_ARN:
        required: true
      MONGODB_PROJECT_ID:
        required: true
      MONGODB_PUBLIC_KEY:
        required: true
      MONGODB_PRIVATE_KEY:
        required: true
      GYDA_KID:
        required: true
      WC_SOCIAL_DATA_API_KEY:
        required: true
      MONGODB_URI:
        required: true
      GRAPHQL_S31_KEY:
        required: true

jobs:
  terraform:
    runs-on: [self-hosted, linux, arm64, prod]
    steps:
      - name: Clone
        id: clone
        uses: actions/checkout@v4

      - name: Changed Files
        id: changed-files
        uses: tj-actions/changed-files@v46
        with:
          dir_names: true
          files: "${{ inputs.infrastructure-directory }}"

      - name: AWS Credential Setup
        id: setup-aws
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ${{ secrets.AWS_REGION }}
          role-to-assume: ${{secrets.ASSUME_ROLE_ARN}}
          role-session-name: GitHubActionsOIDC

      - name: Terraform Setup
        id: terraform-setup
        uses: hashicorp/setup-terraform@v3.1.2
        with:
          terraform_version: "${{ inputs.terraform-version }}"

      - name: Terraform Run
        id: terraform-run
        continue-on-error: true
        env:
          TF_TOKEN_app_terraform_io: "${{ secrets.TFE_TOKEN }}"
          MONGODB_PROJECT_ID: "${{ secrets.MONGODB_PROJECT_ID }}"
          MONGODB_PUBLIC_KEY: "${{ secrets.MONGODB_PUBLIC_KEY }}"
          MONGODB_PRIVATE_KEY: "${{ secrets.MONGODB_PRIVATE_KEY }}"
          GYDA_KID: "${{ secrets.GYDA_KID }}"
          WC_SOCIAL_DATA_API_KEY: "${{ secrets.WC_SOCIAL_DATA_API_KEY }}"
          MONGODB_URI: "${{ secrets.MONGODB_URI }}"
          GRAPHQL_S31_KEY: "${{ secrets.GRAPHQL_S31_KEY }}"
        run: |
          for dir in ${{ steps.changed-files.outputs.all_changed_files }}; do
            if [ ${{ github.event_name }} == "pull_request" ]; then
              echo "PLANNING ---------> $dir" | tee -a terraform.out
              terraform -chdir=$dir init -no-color | tee -a terraform.out
              terraform -chdir=$dir plan -no-color \
                -var="WC_SOCIAL_DATA_API_Key=${{ secrets.WC_SOCIAL_DATA_API_KEY }}" \
                -var="graphql_s31_key=${{ secrets.GRAPHQL_S31_KEY }}" \
                -var="mongodb_project_id=${{ secrets.MONGODB_PROJECT_ID }}" \
                -var="mongodbatlas_public_key=${MONGODB_PUBLIC_KEY}" \
                -var="mongodbatlas_private_key=${MONGODB_PRIVATE_KEY}" \
                -var="gyda_kid=${{ secrets.GYDA_KID }}" \
                -var="mongodb_uri=${{ secrets.MONGODB_URI }}" | tee -a terraform.out
              echo "PLAN_OUT<<EOF" >> $GITHUB_OUTPUT
              grep -v '::debug::' terraform.out >> $GITHUB_OUTPUT
              echo "EOF" >> $GITHUB_OUTPUT
            elif [ ${{ github.event_name }} == "push" ] && [ ${{ github.ref_name }} == "main" ]; then
              echo "APPLYING ---------> $dir"
              terraform -chdir=$dir init -no-color
              terraform -chdir=$dir apply -no-color -auto-approve \
                -var="WC_SOCIAL_DATA_API_Key=${{ secrets.WC_SOCIAL_DATA_API_KEY }}" \
                -var="graphql_s31_key=${{ secrets.GRAPHQL_S31_KEY }}" \
                -var="mongodb_project_id=${{ secrets.MONGODB_PROJECT_ID }}" \
                -var="mongodbatlas_public_key=${{ secrets.MONGODB_PUBLIC_KEY }}" \
                -var="mongodbatlas_private_key=${{ secrets.MONGODB_PRIVATE_KEY }}" \
                -var="gyda_kid=${{ secrets.GYDA_KID }}" \
                -var="mongodb_uri=${{ secrets.MONGODB_URI }}"
            fi
          done

      - name: PR Comment
        id: github-comment
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        env:
          PLAN: "terraform plan\n${{ join(steps.terraform-run.outputs.*, '\n') }}"
        with:
          github-token: ${{ secrets.GH_TOKEN }}
          script: |
            // 1. Retrieve existing bot comments for the PR
            const { data: comments } = await github.rest.issues.listComments({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
            })
            const botComment = comments.find(comment => {
              return comment.user.type === 'Bot' && comment.body.includes('Terraform Plan')
            })

            // 2. Prepare format of the comment
            const output = `#### Terraform Plan \`${{ steps.terraform-run.outcome }}\`

            <details><summary>Show Plan Output</summary>

            \`\`\`\n
            ${{ steps.terraform-run.outputs.PLAN_OUT }}
            \`\`\`

            </details>

            *Pusher: @${{ github.actor }}, Action: \`${{ github.event_name }}\`, Working Directory: \`${{ env.tf_actions_working_dir }}\`, Workflow: \`${{ github.workflow }}\`*`;

            // 3. If we have a comment, update it, otherwise create a new one
            if (botComment) {
              github.rest.issues.updateComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: botComment.id,
                body: output
              })
            } else {
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: output
              })
            }