name: ESLint

on:
  pull_request:
    types: [opened, synchronize, reopened]

jobs:
  eslint:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.12.2' # Specify the Node.js version you want to use

    - name: Configure npm to use private registry
      run: echo "//registry.npmjs.org/:_authToken=${{ secrets.NPM_TOKEN }}" > ~/.npmrc

    - name: Install dependencies
      run: npm install

    - name: Run ESLint
      run: npm run lint

    - name: Run Prettier
      run: npm run format-check