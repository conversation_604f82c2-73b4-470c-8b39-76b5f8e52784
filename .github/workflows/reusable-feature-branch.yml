name: Reusable Feature Branch PR Workflow

on:
  workflow_call:
    inputs:
      branch_prefix:
        required: true
        type: string
      base_branch:
        required: true
        type: string
      excluded_actor:
        required: true
        type: string
    secrets:
      GH_TOKEN:
        required: true
      AWS_REGION:
        required: true
      ASSUME_ROLE_ARN:
        required: true
      TFE_TOKEN:
        required: true
      ECR_REPOSITORY:
        required: true
      S3_BUCKET_NAME:
        required: true
      NPM_TOKEN:
        required: true
      MONGODB_PROJECT_ID:
        required: true
      MONGODB_PUBLIC_KEY:
        required: true
      MONGODB_PRIVATE_KEY:
        required: true
      GYDA_KID:
        required: true
      WC_SOCIAL_DATA_API_KEY:
        required: true
      MONGODB_URI:
        required: true
      GRAPHQL_S31_KEY:
        required: true

permissions:
  id-token: write
  contents: read
  pull-requests: write

env:
  NODE_VERSION: 20
  TERRAFORM_VERSION: 1.8.2
  CI: true
  HUSKY: 0
  ECR_REPOSITORY: ${{ secrets.ECR_REPOSITORY }}
  AWS_DEFAULT_REGION: ${{ secrets.AWS_REGION }}
  S3_BUCKET_NAME: ${{ secrets.S3_BUCKET_NAME }}
  TF_TOKEN_app_terraform_io: ${{ secrets.TFE_TOKEN }}
  
jobs:
  feature-branch-deploy:
    runs-on: [self-hosted, linux, arm64, prod]
    if: |
      startsWith(github.head_ref, inputs.branch_prefix) &&
      github.base_ref == inputs.base_branch &&
      github.actor != inputs.excluded_actor
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GH_TOKEN }}

      - name: Fetch and checkout PR branch
        run: |
          git fetch origin ${{ github.head_ref }}:${{ github.head_ref }}
          git checkout ${{ github.head_ref }}
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          registry-url: "https://registry.npmjs.org/"
          scope: "@meltwater"
          always-auth: true

      - name: Configure npm for publishing
        run: echo "//registry.npmjs.org/:_authToken=${{ secrets.NPM_TOKEN }}" > .npmrc

      - name: Install dependencies
        run: |
          npm ci
      
      - name: Build packages
        run: |
          npm run build

      - name: Setup Git for Lerna
        run: |
          git config --global user.name "GitHub Action"
          git config --global user.email "<EMAIL>"

      - name: Generate version
        run: |
          TIMESTAMP=$(date +%Y%m%d%H%M%S)
          npm run version:prerelease -- --preid engage-$TIMESTAMP

      - name: Publish Packages
        run: npm run publish:packages

      - name: Clear Cache for Published Packages
        run: npm run cleanCache:sinceTag


      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.ASSUME_ROLE_ARN }}
          aws-region: ${{ env.AWS_DEFAULT_REGION }}
          role-session-name: GitHubActionsOIDC


      - name: Detect Lambda Functions
        id: detect_lambdas
        run: |
          BRANCH_NAME="${GITHUB_HEAD_REF##*/}"
          echo "Branch Name: $BRANCH_NAME"
          
          # Find Lambda definition files directly under infrastructure/services/
          LAMBDA_FILES=$(find infrastructure/services -maxdepth 1 -type f -name "*lambda.tf")
          echo "Detected Lambda definition files:"
          echo "$LAMBDA_FILES"
          
          # Initialize an empty array for filtered Lambda functions
          FILTERED_LAMBDAS=()
          
          # Process each Lambda file
          while IFS= read -r lambda_file; do
            # Skip empty lines
            if [ -z "$lambda_file" ]; then
              continue
            fi
            
            echo "Processing Lambda file: $lambda_file"
            
            # Extract the Lambda name from the file name (remove directory and extension)
            lambda_name=$(basename "$lambda_file" .tf)
            echo "Lambda name: $lambda_name"
            
            # Adjust the condition to skip the Warpzone Lambda in feature branches
            if [[ "$BRANCH_NAME" == ADS-* && "$lambda_name" == "warpzone-processor-lambda" ]]; then
              echo "Skipping Warpzone Lambda in feature branch: $lambda_name"
            else
              FILTERED_LAMBDAS+=("$lambda_name")
            fi
          done <<< "$LAMBDA_FILES"
          
          # Convert the array to a compact JSON array
          LAMBDAS_JSON=$(printf '%s\n' "${FILTERED_LAMBDAS[@]}" | jq -R . | jq -s -c .)
          echo "Filtered Lambdas:"
          echo "$LAMBDAS_JSON"
          
          # Set the output variable
          echo "lambdas=$LAMBDAS_JSON" >> "$GITHUB_OUTPUT"

      - name: Update Lambdas
        run: |
          LAMBDAS='${{ steps.detect_lambdas.outputs.lambdas }}'
          echo "LAMBDAS: $LAMBDAS"
          
          for lambda_name in $(echo "$LAMBDAS" | jq -r '.[]'); do
            echo "Processing Lambda: $lambda_name"
            # Build and deploy the Lambda
            # Add your commands here, using $lambda_name
          done

      - name: Get AWS account ID
        id: get-aws-account
        run: |
          AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
          echo "aws_account_id=${AWS_ACCOUNT_ID}" >> $GITHUB_OUTPUT

      - name: Build Lambda packages
        run: |
          # Find all directories in the lambda folder
          LAMBDA_DIRS=$(find lambda -maxdepth 1 -mindepth 1 -type d)
          
          for lambda_dir in $LAMBDA_DIRS; do
            lambda_name=$(basename $lambda_dir)
            echo "Building package for $lambda_name"
            
            # Run the build:package script from the root, continue on error
            npm run build:package --workspace=lambda/${lambda_name} || true
            
            echo "Finished building $lambda_name"
          done
        continue-on-error: true
          
  
      - name: Build and Push Docker Images
        run: |
          LAMBDAS='${{ steps.detect_lambdas.outputs.lambdas }}'
          BRANCH_NAME="${GITHUB_HEAD_REF##*/}"
          AWS_REGION='${{ secrets.AWS_REGION }}'
          AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)

          echo "LAMBDAS: $LAMBDAS"

          # Log in to ECR
          aws ecr get-login-password --region "$AWS_REGION" | \
            docker login --username AWS \
            --password-stdin \
            "$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com"

          # Mapping of Lambda Names to ECR Repository Names
          declare -A ECR_REPO_NAMES
          ECR_REPO_NAMES["authorizer-lambda"]="authorizer-lambda"
          ECR_REPO_NAMES["engage-ads-lambda"]="engage-ads"

          # Mapping of Lambda Names to Build Context Directories
          declare -A LAMBDA_DIRS
          LAMBDA_DIRS["authorizer-lambda"]="engage-ads-authorizer"
          LAMBDA_DIRS["engage-ads-lambda"]="engage-ads-graphql"

          # Mapping of Lambda Names to Image Tags
          declare -A IMAGE_TAGS
          IMAGE_TAGS["authorizer-lambda"]="engage-ads-authorizer"
          IMAGE_TAGS["engage-ads-lambda"]="engage-ads-graphql"

          for lambda_name in $(echo "$LAMBDAS" | jq -r '.[]'); do
            echo "Processing Lambda: $lambda_name"

            # Skip 'warpzone-processor-lambda' in feature branches
            if [[ "$BRANCH_NAME" == ADS-* && "$lambda_name" == "warpzone-processor-lambda" ]]; then
              echo "Skipping 'warpzone-processor-lambda' in feature branch."
              continue
            fi

            ECR_REPO="${ECR_REPO_NAMES[$lambda_name]}"
            BUILD_CONTEXT="./lambda/${LAMBDA_DIRS[$lambda_name]}"
            IMAGE_BASE_TAG="${IMAGE_TAGS[$lambda_name]}"

            if [ -z "$ECR_REPO" ] || [ -z "$BUILD_CONTEXT" ] || [ -z "$IMAGE_BASE_TAG" ]; then
              echo "Error: Missing ECR repository, build context, or image tag mapping for lambda '$lambda_name'"
              continue
            fi

            IMAGE_TAG="${IMAGE_BASE_TAG}-${BRANCH_NAME}-latest"
            IMAGE_URI="$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPO:$IMAGE_TAG"

            # Check if the build context directory exists
            if [ ! -d "$BUILD_CONTEXT" ]; then
              echo "Error: Build context directory '$BUILD_CONTEXT' does not exist."
              continue
            fi

            echo "Building Docker image for $lambda_name from context $BUILD_CONTEXT"
            docker build -t "$IMAGE_URI" "$BUILD_CONTEXT"

            echo "Pushing Docker image to ECR: $IMAGE_URI"
            docker push "$IMAGE_URI"
          done

      - name: Set branch name
        run: |
          echo "BRANCH_NAME=${GITHUB_HEAD_REF}" >> $GITHUB_ENV
          echo "WORKSPACE=${GITHUB_HEAD_REF}" >> $GITHUB_ENV


      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3.1.2
        with:
          terraform_version: ${{ env.TERRAFORM_VERSION }}

      - name: Terraform Init
        run: |
          terraform -chdir=infrastructure/services init -reconfigure

      - name: Setup Terraform Workspace
        run: |
          cd infrastructure/services
          terraform workspace select -or-create ${{ github.head_ref }}
        
      - name: Generate Terraform Plan Targets
        id: plan_targets
        run: |
          cd infrastructure/services
          
          BRANCH_NAME="${GITHUB_HEAD_REF##*/}"
          echo "Current branch name: $BRANCH_NAME"
          
          # Find all Lambda modules
          LAMBDA_MODULES=$(grep -r -h 'module ".*_lambda"' . | sed -E 's/module "(.*)".*$/\1/')
          echo "Lambda modules found:"
          echo "$LAMBDA_MODULES"
          
          # Find all API Gateway resources with their names
          API_RESOURCES=$(grep -r -h 'resource "aws_apigatewayv2_' . | sed -E 's/resource "([^"]*)" "([^"]*)".*/\1.\2/')
          echo "API Gateway resources found:"
          echo "$API_RESOURCES"
          
          # Initialize TARGETS
          TARGETS=""
          
          # Add Lambda modules (excluding warpzone for feature branches)
          for module in $LAMBDA_MODULES; do
            if [[ "$module" == *"warpzone"* ]] || [[ "$module" == *"this_lambda"* ]]; then
              echo "Skipping Warpzone Lambda module: $module"
              continue
            fi
            TARGETS="$TARGETS -target=module.$module"
          done
          
          # Add API Gateway resources
          for resource in $API_RESOURCES; do
            TARGETS="$TARGETS -target=$resource"
          done
          
          # Add non-Warpzone resources
          find . -type f ! -name "*warpzone*.tf" -exec grep -h 'resource "' {} \; | 
          grep -v 'aws_kms\|aws_sqs\|aws_sns' |
          sed -E 's/resource "([^"]*)" "([^"]*)".*/\1.\2/' |
          while read -r resource; do
            if [[ "$resource" != *"warpzone"* ]]; then
              TARGETS="$TARGETS -target=$resource"
            fi
          done
          
          echo "Skipping all Warpzone resources in feature branch"
          
          # Remove any duplicate targets
          TARGETS=$(echo $TARGETS | tr ' ' '\n' | sort -u | tr '\n' ' ')
          
          echo "Final targets:"
          echo "$TARGETS"
          
          echo "targets=$TARGETS" >> $GITHUB_OUTPUT

      - name: Terraform Plan
        run: |
          cd infrastructure/services
          terraform plan -refresh=false ${{ steps.plan_targets.outputs.targets }} \
            -var="create_mongodb_resources=false" \
            -var="mongodb_project_id=${{ secrets.MONGODB_PROJECT_ID }}" \
            -var="mongodbatlas_public_key=${{ secrets.MONGODB_PUBLIC_KEY }}" \
            -var="mongodbatlas_private_key=${{ secrets.MONGODB_PRIVATE_KEY }}" \
            -var="gyda_kid=${{ secrets.GYDA_KID }}" \
            -var="WC_SOCIAL_DATA_API_Key=${{ secrets.WC_SOCIAL_DATA_API_KEY }}" \
            -var="mongodb_uri=${{ secrets.MONGODB_URI }}" \
            -var="graphql_s31_key=${{ secrets.GRAPHQL_S31_KEY }}" \
            -out=tfplan
    
      - name: Terraform Apply
        run: |
          cd infrastructure/services
          terraform apply -auto-approve tfplan

      - name: Update Lambda Functions
        run: |
          set -x  # Enable execution trace
          # Removed 'set -e' to prevent premature exit

          LAMBDAS='${{ steps.detect_lambdas.outputs.lambdas }}'
          echo "LAMBDAS: $LAMBDAS"

          BRANCH_NAME="${GITHUB_HEAD_REF##*/}"
          AWS_REGION='${{ secrets.AWS_REGION }}'
          AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)

          # Mapping of Lambda Names to ECR Repository Names (same as build step)
          declare -A ECR_REPO_NAMES
          ECR_REPO_NAMES["authorizer-lambda"]="authorizer-lambda"
          ECR_REPO_NAMES["engage-ads-lambda"]="engage-ads"

          # Mapping of Lambda Names to Lambda Function Names
          declare -A LAMBDA_FUNCTION_NAMES
          LAMBDA_FUNCTION_NAMES["authorizer-lambda"]="$(echo "${BRANCH_NAME}-authorizer")"
          LAMBDA_FUNCTION_NAMES["engage-ads-lambda"]="$(echo "${BRANCH_NAME}-engage-ads-lambda")"

          # Mapping of Lambda Names to Image Tags (same as build step)
          declare -A IMAGE_TAGS
          IMAGE_TAGS["authorizer-lambda"]="engage-ads-authorizer"
          IMAGE_TAGS["engage-ads-lambda"]="engage-ads-graphql"

          for lambda_name in $(echo "$LAMBDAS" | jq -r '.[]'); do
            echo "Processing Lambda: $lambda_name"

            # Skip 'warpzone-processor-lambda' in feature branches
            if [[ "$BRANCH_NAME" == ADS-* && "$lambda_name" == "warpzone-processor-lambda" ]]; then
              echo "Skipping 'warpzone-processor-lambda' in feature branch."
              continue
            fi

            ECR_REPO="${ECR_REPO_NAMES[$lambda_name]}"
            FUNCTION_NAME="${LAMBDA_FUNCTION_NAMES[$lambda_name]}"
            IMAGE_BASE_TAG="${IMAGE_TAGS[$lambda_name]}"

            if [ -z "$ECR_REPO" ] || [ -z "$FUNCTION_NAME" ] || [ -z "$IMAGE_BASE_TAG" ]; then
              echo "Error: Missing ECR repository, function name, or image tag mapping for lambda '$lambda_name'"
              continue
            fi

            # Use same image tag format as build step
            IMAGE_TAG="${IMAGE_BASE_TAG}-${BRANCH_NAME}-latest"
            IMAGE_URI="$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPO:$IMAGE_TAG"

            echo "FUNCTION_NAME: $FUNCTION_NAME"
            echo "IMAGE_URI: $IMAGE_URI"
            echo "AWS_REGION: $AWS_REGION"

            # Check for empty variables
            if [ -z "$FUNCTION_NAME" ] || [ -z "$IMAGE_URI" ] || [ -z "$AWS_REGION" ]; then
              echo "Error: One or more required variables are empty."
              continue
            fi

            echo "Updating Lambda $FUNCTION_NAME with image $IMAGE_URI"

            # Wrap in subshell to capture errors
            (
              set +e  # Disable immediate exit in subshell
              UPDATE_OUTPUT=$(aws lambda update-function-code \
                --function-name "$FUNCTION_NAME" \
                --image-uri "$IMAGE_URI" \
                --region "$AWS_REGION" \
                2>&1)
              UPDATE_EXIT_CODE=$?

              if [ $UPDATE_EXIT_CODE -ne 0 ]; then
                echo "Error updating Lambda function $FUNCTION_NAME"
                echo "AWS CLI Output:"
                echo "$UPDATE_OUTPUT"
                continue
              else
                echo "Successfully updated Lambda function $FUNCTION_NAME"
              fi
            )
          done

      - name: Copy Layer Code to S3 Bucket
        run: npm run publish:s3:sinceTag

      - name: Generate Deployment Summary
        id: deployment_summary
        run: |
          set -e  # Exit immediately if a command exits with a non-zero status
          SUMMARY=""
          LAMBDAS='${{ steps.detect_lambdas.outputs.lambdas }}'
          BRANCH_NAME="${{ github.head_ref }}"
          
          echo "Debug: LAMBDAS = $LAMBDAS"
          echo "Debug: BRANCH_NAME = $BRANCH_NAME"
          
          # Get all API Gateways with tags
          API_GATEWAYS=$(aws apigatewayv2 get-apis --query 'Items[*].[ApiId,Name,Tags]' --output json)
          echo "Debug: API_GATEWAYS = $API_GATEWAYS"
          
          # Get all custom domain names
          CUSTOM_DOMAINS=$(aws apigatewayv2 get-domain-names --query 'Items[*].DomainName' --output json)
          echo "Debug: CUSTOM_DOMAINS = $CUSTOM_DOMAINS"
          
          SUMMARY="Lambda Functions:\n\n"
          
          for lambda in $(echo $LAMBDAS | jq -r '.[]'); do
            lambda_name=$(basename $lambda .tf | sed 's/_lambda//')
            FUNCTION_NAME="${BRANCH_NAME}-${lambda_name}"
            
            echo "Debug: Processing lambda: $FUNCTION_NAME"
            SUMMARY+="Lambda Function: ${FUNCTION_NAME}\n"
            
            # Find the API Gateway for this lambda using the tag
            API_ID=$(echo $API_GATEWAYS | jq -r ".[] | select(.[2].LambdaFunction == \"$FUNCTION_NAME\") | .[0]")
            API_NAME=$(echo $API_GATEWAYS | jq -r ".[] | select(.[2].LambdaFunction == \"$FUNCTION_NAME\") | .[1]")
            
            echo "Debug: API_ID = $API_ID, API_NAME = $API_NAME"
            
            if [ -n "$API_ID" ] && [ "$API_ID" != "null" ]; then
              API_URL=$(aws apigatewayv2 get-api --api-id $API_ID --query 'ApiEndpoint' --output text)
              STAGE_NAME=$(aws apigatewayv2 get-stages --api-id $API_ID --query 'Items[0].StageName' --output text)
              RAW_URL="${API_URL}/${STAGE_NAME}"
              
              echo "Debug: API_URL = $API_URL, STAGE_NAME = $STAGE_NAME, RAW_URL = $RAW_URL"
              SUMMARY+="API Gateway: ${API_NAME} (${RAW_URL})\n"
              
              # Check custom domain mappings
              for DOMAIN_NAME in $(echo $CUSTOM_DOMAINS | jq -r '.[]'); do
                echo "Debug: Checking domain: $DOMAIN_NAME"
                API_MAPPINGS=$(aws apigatewayv2 get-api-mappings --domain-name $DOMAIN_NAME --output json)
                echo "Debug: API_MAPPINGS for $DOMAIN_NAME = $API_MAPPINGS"
                
                BRANCH_MAPPINGS=$(echo "$API_MAPPINGS" | jq -r ".Items[] | select(.ApiId == \"$API_ID\" and (.ApiMappingKey | startswith(\"feature/${BRANCH_NAME}\")))")
                echo "Debug: BRANCH_MAPPINGS = $BRANCH_MAPPINGS"
                
                if [ -n "$BRANCH_MAPPINGS" ]; then
                  while read -r MAPPING_KEY; do
                    echo "Debug: MAPPING_KEY = $MAPPING_KEY"
                    if [ -n "$MAPPING_KEY" ] && [ "$MAPPING_KEY" != "null" ]; then
                      FULL_URL="https://${DOMAIN_NAME}/${MAPPING_KEY}"
                      echo "Debug: FULL_URL = $FULL_URL"
                      SUMMARY+="Custom Domain: ${FULL_URL}\n"
                      echo "Debug: Added Custom Domain to SUMMARY"
                    fi
                  done < <(echo "$BRANCH_MAPPINGS" | jq -r '.ApiMappingKey')
                fi
              done
              
              # Get and add routes information
              ROUTES=$(aws apigatewayv2 get-routes --api-id $API_ID --output json)
              echo "Debug: ROUTES = $ROUTES"
              if [ -n "$ROUTES" ] && [ "$(echo "$ROUTES" | jq '.Items | length')" -gt 0 ]; then
                SUMMARY+="Routes:\n"
                while read -r route; do
                  # Extract only the method and path
                  ROUTE_INFO=$(echo "$route" | awk '{print $1, $2}')
                  SUMMARY+="  ${ROUTE_INFO}\n"
                  echo "Debug: Added Route to SUMMARY: $ROUTE_INFO"
                done < <(echo "$ROUTES" | jq -r '.Items[] | "\(.RouteKey)"')
              else
                SUMMARY+="Routes: None found\n"
              fi
            else
              SUMMARY+="API Gateway: Not found (deployment may be in progress)\n"
            fi
            
            SUMMARY+="\n"
          done
          
          # Remove any trailing newlines
          SUMMARY=$(echo "$SUMMARY" | sed -e :a -e '/^\n*$/{$d;N;ba' -e '}')
          
          echo "summary<<EOF" >> $GITHUB_OUTPUT
          echo -e "$SUMMARY" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
          
          echo "Debug: Final SUMMARY = $SUMMARY"
        continue-on-error: true

      - name: Add or Update API Gateway Endpoint Comment
        if: always()
        run: |
          PR_NUMBER=${{ github.event.pull_request.number }}
          
          NEW_COMMENT="✅ Feature branch deployment summary:

          ${{ steps.deployment_summary.outputs.summary }}"
          
          gh pr comment $PR_NUMBER --body "$NEW_COMMENT"
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          BRANCH_NAME: ${{ env.BRANCH_NAME }}
