name: Create Feature Branch Resources

on:
  pull_request:
    types: [opened, synchronize]

jobs:
  test-feature-branch:
    uses: ./.github/workflows/reusable-feature-branch.yml
    with:
      branch_prefix: 'ADS-'
      base_branch: 'main'
      excluded_actor: 'engage-ads-meltwater'
    secrets:
      GH_TOKEN: ${{ secrets.GH_TOKEN }}
      AWS_REGION: ${{ secrets.AWS_REGION }}
      ASSUME_ROLE_ARN: ${{ secrets.ASSUME_ROLE_ARN }}
      TFE_TOKEN: ${{ secrets.TFE_TOKEN }}
      ECR_REPOSITORY: ${{ secrets.ECR_REPOSITORY }}
      S3_BUCKET_NAME: ${{ secrets.S3_BUCKET_NAME }}
      NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
      MONGODB_PROJECT_ID: ${{ secrets.MONGODB_PROJECT_ID }}
      MONGODB_PUBLIC_KEY: ${{ secrets.MONGODB_PUBLIC_KEY }}
      MONGODB_PRIVATE_KEY: ${{ secrets.MONGODB_PRIVATE_KEY }}
      GYDA_KID: ${{ secrets.GYDA_KID }}
      WC_SOCIAL_DATA_API_KEY: ${{ secrets.WC_SOCIAL_DATA_API_KEY }}
      MONGODB_URI: ${{ secrets.MONGODB_URI }}
      GRAPHQL_S31_KEY: ${{ secrets.GRAPHQL_S31_KEY }}
