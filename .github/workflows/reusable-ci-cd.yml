name: Reusable Build and Deploy Workflow

on:
  workflow_call:
    inputs:
      node-version:
        required: true
        type: string
      beta-release-name:
        required: true
        type: string
      enable-ecr:
        required: false
        type: boolean
        default: true
      enable-s3:
        required: false
        type: boolean
        default: true
      enable-lambda:
        required: false
        type: boolean
        default: true
      should-version-and-publish:
        required: false
        type: boolean
        default: false
    secrets:
      npm-token:
        required: true
      gh-token:
        required: true
      aws-region:
        required: true
      ecr-repository:
        required: true
      s3-bucket-name:
        required: true
      assume-role-arn:
        required: true

jobs:
  run-tests:
    runs-on: [self-hosted, linux, arm64, prod]
    steps:
      - uses: actions/checkout@v4
      - name: setup-node-js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ inputs.node-version }}
      - name: Configure npm for publishing
        run: echo "//registry.npmjs.org/:_authToken=${{ secrets.npm-token }}" > .npmrc
      - name: install-dependencies
        run: | 
          npm ci
          npm run build
        env:
          CI: true
          HUSKY: 0
      - name: Run Tests
        run: npm run test
        env:
          CI: true
          HUSKY: 0

  version-and-publish:
    runs-on: [self-hosted, linux, arm64, prod]
    needs: run-tests
    if: ${{ inputs.should-version-and-publish }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          persist-credentials: false
      - name: setup-node-js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ inputs.node-version }}
          registry-url: "https://registry.npmjs.org/"
          scope: "@meltwater"
          always-auth: true
      - name: Configure npm for publishing
        run: echo "//registry.npmjs.org/:_authToken=${{ secrets.npm-token }}" > .npmrc
      - name: install-dependencies-and-build
        run: |
          npm ci
          npm run build
        env:
          CI: true
          HUSKY: 0
      - name: Configure git for Lerna
        run: |
          git config --global user.name "github-actions"
          git config --global user.email "<EMAIL>"
          git config --global credential.helper 'store --file ~/.my-credentials'
          echo "https://x-access-token:${{ secrets.gh-token }}@github.com" > ~/.my-credentials

      - name: Save Tags
        id: save_tag
        run: |
          TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "v0.0.0")
          echo "Tag before Publish: $TAG"
          echo "TAG=$TAG" >> $GITHUB_ENV
          echo "BRANCH=${GITHUB_HEAD_REF:-${GITHUB_REF#refs/heads/}}" >> $GITHUB_ENV
      - name: Check and Clean Existing Tags
        run: |
          echo "Checking for potential tag conflicts..."
          # Get current package versions that would be tagged
          PACKAGES=$(npx lerna list --json 2>/dev/null || echo "[]")
          echo "Current packages: $PACKAGES"

          # Check if we're in a state where tags might conflict
          if [[ ${{ github.event_name }} == 'push' && "${{ github.ref }}" == 'refs/heads/main' ]]; then
            echo "Main branch push detected - checking for existing release tags"
            # For main branch, we might need to handle existing release tags
            git fetch --tags

            # Check if there are any staged changes that might cause version conflicts
            if git diff --cached --quiet; then
              echo "No staged changes detected"
            else
              echo "Staged changes detected, resetting to clean state"
              git reset --hard HEAD
            fi
          fi
      - name: Generate version
        id: generate_version
        run: |
          set -e
          echo "Starting version generation..."

          if [[ ${{ github.event_name }} == 'pull_request' || (${{ github.event_name }} == 'push' && "${{ github.ref }}" == 'refs/heads/${{ inputs.beta-release-name }}'*) ]]; then
            echo "Generating prerelease version..."
            npm run version:prerelease || {
              echo "Prerelease versioning failed, attempting recovery..."
              # Try to recover by checking what went wrong
              git status
              git tag --list | tail -10
              exit 1
            }
          elif [[ ${{ github.event_name }} == 'push' && "${{ github.ref }}" == 'refs/heads/main' ]]; then
            echo "Generating release version..."
            npm run version:release || {
              echo "Release versioning failed, attempting recovery..."
              # Check if it's a tag conflict by looking for any package tags
              if git tag --list | grep -E "@[0-9]+\.[0-9]+\.[0-9]+$"; then
                echo "Detected existing release tags, attempting to increment beyond existing versions..."

                # The issue is that Lerna has already updated package.json files but failed at tagging
                # We need to reset the state and force a proper version increment
                echo "Resetting any partial changes from failed versioning..."
                git reset --hard HEAD
                git clean -fd

                echo "Attempting forced patch version increment..."
                # Force all packages to increment patch version, regardless of change detection
                npx lerna version patch --yes --force-publish || {
                  echo "Forced patch increment failed, trying minor increment..."
                  # Reset again and try minor increment
                  git reset --hard HEAD
                  git clean -fd
                  npx lerna version minor --yes --force-publish || {
                    echo "All automatic version increments failed, manual intervention required"
                    echo "The issue is that Lerna is trying to create a tag that already exists."
                    echo "You may need to manually increment package versions or delete conflicting tags."
                    git status
                    git tag --list | tail -10
                    exit 1
                  }
                }
              else
                echo "Non-tag related versioning error"
                git status
                exit 1
              fi
            }
          fi

          echo "Version generation completed successfully"
      - name: Check for Changes
        id: check_changes
        run: |
          # Check for both staged and unstaged changes
          GIT_DIFF_STAGED=$(git diff --staged)
          GIT_DIFF_UNSTAGED=$(git diff)

          if [ -z "$GIT_DIFF_STAGED" ] && [ -z "$GIT_DIFF_UNSTAGED" ]; then
            echo "No changes to commit."
            echo "changes_exist=false" >> $GITHUB_OUTPUT
          else
            echo "Changes detected."
            echo "changes_exist=true" >> $GITHUB_OUTPUT

            # Stage any unstaged changes from version bump
            if [ -n "$GIT_DIFF_UNSTAGED" ]; then
              echo "Staging unstaged changes from version bump..."
              git add .
            fi
          fi
      - name: Update Tag Reference
        run: |
          # Update the TAG environment variable with the latest tag after versioning
          NEW_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "$TAG")
          echo "Updated tag reference from $TAG to $NEW_TAG"
          echo "TAG=$NEW_TAG" >> $GITHUB_ENV
      - name: Commit changes
        if: steps.check_changes.outputs.changes_exist == 'true'
        run: |
          # Ensure we have something to commit
          if ! git diff --staged --quiet; then
            git commit -m 'chore: update package versions and package-lock.json [skip ci]'

            # Push with retry mechanism for tag conflicts
            for i in {1..3}; do
              if git push origin HEAD:${GITHUB_REF#refs/heads/} --follow-tags; then
                echo "Successfully pushed changes and tags"
                break
              else
                echo "Push attempt $i failed, retrying in 5 seconds..."
                sleep 5
                if [ $i -eq 3 ]; then
                  echo "Failed to push after 3 attempts"
                  exit 1
                fi
              fi
            done
          else
            echo "No staged changes to commit after version bump"
          fi
        env:
          GITHUB_TOKEN: ${{ secrets.gh-token }}
      - name: Publish Packages
        run: npm run publish:packages
        env:
          NODE_AUTH_TOKEN: ${{ secrets.npm-token }}
          HUSKY: 0
      - name: Clear Cache for Published Packages
        env:
          NODE_AUTH_TOKEN: ${{ secrets.npm-token }}
        run: npm run cleanCache:sinceTag

      - name: Configure AWS Credentials
        if: ${{ inputs.enable-ecr }}
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{secrets.assume-role-arn}}
          aws-region: ${{ secrets.aws-region }}
          role-session-name: GitHubActionsOIDC

      - name: Login to Amazon ECR
        if: ${{ inputs.enable-ecr }}
        run: |
          aws ecr get-login-password --region ${{ secrets.aws-region }} | docker login --username AWS --password-stdin ${{ secrets.ecr-repository }}

      - name: Package for Lambda Deployment
        run: npm run build:package:sinceTag

      - name: Publish image to ECR
        if: ${{ inputs.enable-ecr }}
        run: npm run publish:ecr:sinceTag
        env:
          ECR_REPOSITORY: ${{ secrets.ecr-repository }}
          AWS_DEFAULT_REGION: ${{ secrets.aws-region }}

      - name: Deploy Lambda
        if: ${{ inputs.enable-lambda && inputs.enable-ecr }}
        run: npx lerna run deploy:lambda --stream --since $TAG
        env:
          ECR_REPOSITORY: ${{ secrets.ecr-repository }}

      - name: Copy Layer Code to S3 Bucket
        if: ${{ inputs.enable-s3 }}
        run: npm run publish:s3:sinceTag
        env:
          S3_BUCKET_NAME: ${{ secrets.s3-bucket-name }}