name: CI/CD Workflow
on:
  push:
    branches:
      - main

permissions:
  id-token: write
  contents: read
  pull-requests: write

jobs:
  build:
    if: github.event.pull_request.merged == true || github.event_name == 'push'
    uses: ./.github/workflows/reusable-ci-cd.yml
    with:
      node-version: 20
      beta-release-name: 'ADS-'
      enable-ecr: true
      enable-s3: true
      enable-lambda: true
      should-version-and-publish: ${{ github.ref == 'refs/heads/main' || startsWith(github.ref, 'refs/heads/ADS-') }}
    secrets:
      npm-token: ${{ secrets.NPM_TOKEN }}
      gh-token: ${{ secrets.GH_TOKEN }}
      aws-region: ${{ secrets.AWS_REGION }}
      ecr-repository: ${{ secrets.ECR_REPOSITORY }}
      s3-bucket-name: ${{ secrets.S3_BUCKET_NAME }}
      assume-role-arn: ${{ secrets.ASSUME_ROLE_ARN }}
