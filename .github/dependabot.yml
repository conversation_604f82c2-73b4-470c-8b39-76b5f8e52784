version: 2
updates:
  - package-ecosystem: "npm"
    registries:
      - npm-npmjs
    directory: "/"
    schedule:
      interval: "daily"
      time: "08:00"
      timezone: "America/New_York"
    commit-message:
      prefix: "chore"
      include: "scope"
    versioning-strategy: increase
    groups:
      meltwater-dependencies:
        patterns:
          - "@meltwater/*"
      other-dependencies:
        patterns:
          - "*"
        exclude-patterns:
          - "@meltwater/*"
    allow:
      - dependency-name: "*"

  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "daily"
      time: "08:00"
      timezone: "America/New_York"
    commit-message:
      prefix: "chore"
      include: "scope"
    open-pull-requests-limit: 5
    groups:
      github-actions:
        patterns:
          - "*"

registries:
  npm-npmjs:
    type: npm-registry
    url: https://registry.npmjs.org
    token: ${{secrets.NPM_TOKEN}}
