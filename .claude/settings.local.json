{"permissions": {"allow": ["<PERSON><PERSON>(cat:*)", "Bash(cd:*)", "Bash(git checkout:*)", "Bash(node:*)", "Bash(npm run build && npm run test && npm run lint && npm run format-check)", "Bash(npm run build:*)", "Bash(npm run format-check:*)", "Bash(npm run lint:*)", "Bash(npm run test:lambda:getFacebookAdCampaigns:*)", "Bash(npm run:*)", "Bash(npx lerna run build:*)", "<PERSON><PERSON>(npx prettier:*)", "Bash(npx ts-node:*)"], "deny": []}}