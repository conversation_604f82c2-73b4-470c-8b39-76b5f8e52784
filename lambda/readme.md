# Steps for creating new lambda in terraform in this repo

1. Create a new branch from main for creating new ecr in terraform.
2. Go to _infrastructure/service_ folder and update publish-ecr.tf file with the new lambda ECR repository name.

   ```terraform
      module "ecr_name" {
        source = "../modules/ecr"

        name                      = "lambda-name"
        function_name             = "lambda-name"
        region                    = var.aws_region
        staging_aws_account_id    = var.staging_aws_account_id
        production_aws_account_id = var.production_aws_account_id
        }
   ```

3. Create a new PR and merge it to the main branch.
4. Create a new branch from main for creating the new lambda in terraform after merging the above PR.
   - Do not open PR just yet.
5. Go to _infrastructure/service_ folder again and this time create a new terraform file for the new lambda.
6. Run the following command and go through setup instructions
   ```bash
   backend-generator-cli terraform
   ```
7. After the above script outputs a terraform code, copy paste that code to this new terraform file you created.
8. Make any necessary corrections or adjustments to the terraform code.
9. Run the following command and go through setup instructions
   ```bash
   npm run generate:lambda
   ```
   - This script will create a new lambda folder and files in the **lambda** folder.
10. Go to the newly created lambda folder and build the package.
    ```bash
    cd lambda/lambda-name
    npm run build:package
    ```
11. build and push the docker image to the ECR repository.
    ```bash
    $ECR_REPOSITORY = "Newly created ECR repository"
    aws ecr get-login-password --region eu-west-1 | docker login --username AWS --password-stdin $ECR_REPOSITORY
    docker build --platform linux/arm64 -t $ECR_NAME .
    docker tag $ECR_NAME:latest $ECR_REPOSITORY/$ECR_NAME:latest
    docker push --all-tags $ECR_REPOSITORY/$ECR_NAME
    ```
12. Confirm on ECR if the latest tag was created successfully.
13. Git commit only the terraform file and create a new PR and merge it to the main branch.
14. Create a new branch from main for creating the new lambda after merging the above PR.
15. Now commit the lambda folder and create a new PR and merge it to the main branch.

Now you are successfully created a new lambda in terraform in this repo.
