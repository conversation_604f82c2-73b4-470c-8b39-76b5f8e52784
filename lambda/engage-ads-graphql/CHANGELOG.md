# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

k

## [1.8.1-ENGAGE.1](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.8.1-ENGAGE.1) (2024-11-04)

### Features

- adding support for returning scheduleEndTime for boosted ad ([daa1021](https://github.com/meltwater/engage-ads-backend/commit/daa102117175c8f483ed45c184e61866b69c64f3))

## [1.8.1-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.8.1-ENGAGE.0) (2024-11-04)

### Features

- adding support for checking ad boost status ([a56d35d](https://github.com/meltwater/engage-ads-backend/commit/a56d35df66f50539072508278e03b823e2abd82c))

# [1.8.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.8.0) (2024-11-03)

**Note:** Version bump only for package engage-ads-graphql

## [1.7.7-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.7.7-ENGAGE.0) (2024-10-29)

**Note:** Version bump only for package engage-ads-graphql

## [1.7.5](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.7.5) (2024-10-16)

**Note:** Version bump only for package engage-ads-graphql

## [1.7.5-ENGAGE.1](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.7.5-ENGAGE.1) (2024-10-15)

### Features

- adding static methods for mongoose models ([7b5cf74](https://github.com/meltwater/engage-ads-backend/commit/7b5cf743fffd842ae5bfbc45e3385aec514c72cb))

## [1.7.5-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.7.5-ENGAGE.0) (2024-10-15)

### Bug Fixes

- addressing linting concerns ([87c1df9](https://github.com/meltwater/engage-ads-backend/commit/87c1df91fa5c677b020ef8d2200cc76f43893ce0))
- version update ([5304676](https://github.com/meltwater/engage-ads-backend/commit/5304676d299d906345119645b4f3641d2f473dee))

### Features

- integrating database package with mutation ([77de0db](https://github.com/meltwater/engage-ads-backend/commit/77de0db69d04ae905e2ba1627b89552d6e63f0e3))
- replacing console logs with logger instance ([0148030](https://github.com/meltwater/engage-ads-backend/commit/014803040dfc5a430f0711433874d919a2ee8c0d))

## [1.7.4](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.7.4) (2024-10-14)

**Note:** Version bump only for package engage-ads-graphql

## [1.7.4-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.7.4-ENGAGE.0) (2024-10-14)

**Note:** Version bump only for package engage-ads-graphql

# [1.7.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.7.0) (2024-10-07)

### Features

- adding support for social account ID for root level ([#287](https://github.com/meltwater/engage-ads-backend/issues/287)) ([1436575](https://github.com/meltwater/engage-ads-backend/commit/143657504cdb08af31a4134c1b5cf9633e6bf116))

## [1.6.3-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.6.3-ENGAGE.0) (2024-10-04)

### Bug Fixes

- updating version ([278f3f5](https://github.com/meltwater/engage-ads-backend/commit/278f3f596adf76dbbca7f9c643dd01ba99d73d35))

### Features

- adding support for social account ID for root level ([5a88704](https://github.com/meltwater/engage-ads-backend/commit/5a887045266c42778d2c273006adc488a7de2073))

# [1.6.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.6.0) (2024-10-03)

### Features

- **regions:** ADS-164 Integrate with tiktok regions API ([#282](https://github.com/meltwater/engage-ads-backend/issues/282)) ([49d864b](https://github.com/meltwater/engage-ads-backend/commit/49d864b005cc6475466f0050ff74d751b9628b27))

## [1.5.2-ENGAGE.1](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.5.2-ENGAGE.1) (2024-10-03)

**Note:** Version bump only for package engage-ads-graphql

## [1.5.2-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.5.2-ENGAGE.0) (2024-10-03)

**Note:** Version bump only for package engage-ads-graphql

# [1.5.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.5.0) (2024-09-30)

### Features

- adding origin link for graphql lambda ([#284](https://github.com/meltwater/engage-ads-backend/issues/284)) ([38b747d](https://github.com/meltwater/engage-ads-backend/commit/38b747d0554fa27f83377dc0f3fb245d4e9fdc68))

# [1.4.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.4.0) (2024-09-30)

### Features

- adding support for generating token and passing the same to rel… ([#281](https://github.com/meltwater/engage-ads-backend/issues/281)) ([8e60836](https://github.com/meltwater/engage-ads-backend/commit/8e60836ef0358facc58a4cdd0b31b45b272f7600))

## [1.3.1-ENGAGE.2](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.3.1-ENGAGE.2) (2024-09-12)

### Bug Fixes

- updating graphql_s31_url environment variable value ([2f43d0b](https://github.com/meltwater/engage-ads-backend/commit/2f43d0ba301a9a78b4855cfd41067647726a4b69))

## [1.3.1-ENGAGE.1](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.3.1-ENGAGE.1) (2024-09-12)

### Bug Fixes

- updating boost ad event for lambda tester script ([ae2f62b](https://github.com/meltwater/engage-ads-backend/commit/ae2f62bc5bfd920110b156566b02f562e023248f))

## [1.3.1-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.3.1-ENGAGE.0) (2024-09-10)

### Features

- adding support for generating token and passing the same to relevant repository and mutation ([6839470](https://github.com/meltwater/engage-ads-backend/commit/68394701a5c83d348f17709fdc63687c910ec5dc))

# [1.3.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.3.0) (2024-09-09)

### Features

- **boosting:** ADS-153 update endpoints urls and payload for boosting tiktok ([#280](https://github.com/meltwater/engage-ads-backend/issues/280)) ([c9e8575](https://github.com/meltwater/engage-ads-backend/commit/c9e85755d81575f73f236739ab8680decc442904))

## [1.2.4-ENGAGE.1](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.2.4-ENGAGE.1) (2024-09-06)

### Bug Fixes

- apply formatting ([e6a624f](https://github.com/meltwater/engage-ads-backend/commit/e6a624fc42992b460e24bd912e19d5979fc5e48f))

## [1.2.4-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.2.4-ENGAGE.0) (2024-09-06)

**Note:** Version bump only for package engage-ads-graphql

## [1.2.3](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.2.3) (2024-09-04)

### Bug Fixes

- Ads refactoring lambda plugin ([#279](https://github.com/meltwater/engage-ads-backend/issues/279)) ([c4ea0c5](https://github.com/meltwater/engage-ads-backend/commit/c4ea0c5115143036c4f1f96be8e762aeb5932a1a))

## [1.2.3-ENGAGE.5](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.2.3-ENGAGE.5) (2024-09-03)

### Bug Fixes

- awaiting and sending response ([468bede](https://github.com/meltwater/engage-ads-backend/commit/468bedeed7fc6f9f61d9c1c35ccfeebbd49e0e26))
- sending back response ([07d931f](https://github.com/meltwater/engage-ads-backend/commit/07d931f76313d52f9605a5f7993d66581e79a543))
- updating version ([1bdc0dc](https://github.com/meltwater/engage-ads-backend/commit/1bdc0dc7b5458830d4323b33bec03a1ce2ae7b35))

## [1.2.3-ENGAGE.2](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.2.3-ENGAGE.2) (2024-09-03)

### Bug Fixes

- refactoring lambda and removing usage reporting plugin and relevant configuration ([ae3af42](https://github.com/meltwater/engage-ads-backend/commit/ae3af428abf402105ffa2239620bed2512974a89))
- replacing type any with valid type for ([4f2bb0d](https://github.com/meltwater/engage-ads-backend/commit/4f2bb0de01458a5b05155e04c91cc74693b72948))

## [1.2.3-ENGAGE.1](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.2.3-ENGAGE.1) (2024-09-03)

**Note:** Version bump only for package engage-ads-graphql

## [1.2.3-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.2.3-ENGAGE.0) (2024-09-03)

### Bug Fixes

- integrating executeHandler for sending reports ([908d272](https://github.com/meltwater/engage-ads-backend/commit/908d272d013f459cdf7f99f468e4cc073e85e61b))

## [1.2.2](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.2.2) (2024-09-01)

**Note:** Version bump only for package engage-ads-graphql

## [1.2.1](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.2.1) (2024-08-29)

**Note:** Version bump only for package engage-ads-graphql

## [1.2.1-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.2.1-ENGAGE.0) (2024-08-29)

### Bug Fixes

- linting concerns ([1f877dc](https://github.com/meltwater/engage-ads-backend/commit/1f877dca6a9ff2aedc13de91b0fb1821cb7d4df4))
- Update engageAdsChannelInsightsService.ts to use profileIds instead of profileId ([5602c41](https://github.com/meltwater/engage-ads-backend/commit/5602c41fe5601743c46150b45f2e18859eb29efc))
- Update profileIds parameter in engageAdsChannelInsightsService ([dd0df1a](https://github.com/meltwater/engage-ads-backend/commit/dd0df1a16dd66c87e190fb762761ba5c097e667d))

### Features

- Add support for multiple profileIds in engage-ads-graphql-sdk ([8ff33b7](https://github.com/meltwater/engage-ads-backend/commit/8ff33b7e260e4875e250dad6e63925473686ea6a))
- Add support for multiple profileIds in engageAdsChannelInsights query ([167a6d3](https://github.com/meltwater/engage-ads-backend/commit/167a6d37cdf97ace6a71a2a511974694293d23ec))

# [1.2.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.2.0) (2024-08-28)

### Features

- adding support for filtering posts by profile ID ([#274](https://github.com/meltwater/engage-ads-backend/issues/274)) ([09ed22b](https://github.com/meltwater/engage-ads-backend/commit/09ed22bc039b85cf7c15d03b3401d153123804b0))

## [1.1.8-ENGAGE.1](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.1.8-ENGAGE.1) (2024-08-28)

**Note:** Version bump only for package engage-ads-graphql

## [1.1.8-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.1.8-ENGAGE.0) (2024-08-23)

### Bug Fixes

- updating version ([0159534](https://github.com/meltwater/engage-ads-backend/commit/01595342e58a2a9f07a66a1bb4d24bbbbc9c3d52))

## [1.1.5](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.1.5) (2024-08-22)

### Reverts

- Revert "fix: Add more detailed logging" ([ab2a141](https://github.com/meltwater/engage-ads-backend/commit/ab2a141ad56a078a696114ed87aa7095d816cbf5))

## [1.1.5-ENGAGE.3](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.1.5-ENGAGE.3) (2024-08-21)

**Note:** Version bump only for package engage-ads-graphql

## [1.1.5-ENGAGE.2](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.1.5-ENGAGE.2) (2024-08-21)

**Note:** Version bump only for package engage-ads-graphql

## [1.1.5-ENGAGE.1](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.1.5-ENGAGE.1) (2024-08-21)

### Bug Fixes

- Ads-87 Applies reviews fixes and formatting ([#272](https://github.com/meltwater/engage-ads-backend/issues/272)) ([eb1f812](https://github.com/meltwater/engage-ads-backend/commit/eb1f812142e3976a8c4d119f1afe4d329fe37c49))

## [1.1.5-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.1.5-ENGAGE.0) (2024-08-20)

**Note:** Version bump only for package engage-ads-graphql

## [1.1.4](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.1.4) (2024-08-16)

**Note:** Version bump only for package engage-ads-graphql

## [1.1.4-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.1.4-ENGAGE.0) (2024-08-16)

**Note:** Version bump only for package engage-ads-graphql

## [1.1.3](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.1.3) (2024-08-16)

**Note:** Version bump only for package engage-ads-graphql

## [1.1.3-ENGAGE.7](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.1.3-ENGAGE.7) (2024-08-16)

**Note:** Version bump only for package engage-ads-graphql

## [1.1.3-ENGAGE.6](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.1.3-ENGAGE.6) (2024-08-16)

**Note:** Version bump only for package engage-ads-graphql

## [1.1.3-ENGAGE.5](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.1.3-ENGAGE.5) (2024-08-14)

### Bug Fixes

- addressing linting concerns ([79fccbe](https://github.com/meltwater/engage-ads-backend/commit/79fccbea4e9a33ac4678c161fdf753e27290b219))
- removing operationName for getTikTokAdAccounts ([fc816c2](https://github.com/meltwater/engage-ads-backend/commit/fc816c2e74ef08cf7673c480f9221cb401ecbdd1))
- Update createEvent function to use any type for body parameter ([b5ef65e](https://github.com/meltwater/engage-ads-backend/commit/b5ef65ee11be6c980f62a89f6e93fffe872e45a6))
- updating version ([51d62aa](https://github.com/meltwater/engage-ads-backend/commit/51d62aaf217a3a4cd5b54a424d2d7f928796f49f))

### Features

- integrating sdk ([b0e819e](https://github.com/meltwater/engage-ads-backend/commit/b0e819e53410c1da9d6a5895f113f6ff86101fa8))
- Remove dependency on JSON files in lambda-tester.ts ([d27775e](https://github.com/meltwater/engage-ads-backend/commit/d27775e5f6521525de5011fdcb4a6cac8af81fbf))
- removing json files for queries and mutations testing ([05174e4](https://github.com/meltwater/engage-ads-backend/commit/05174e45c489813e2d112b352bb46ef52805bb55))
- Update lambda-tester script to use engage-ads-graphql-sdk ([c14aeba](https://github.com/meltwater/engage-ads-backend/commit/c14aebac37cbfca72caebeec433ad9a264eab70f))
- Update lambda-tester to use operation type and JSON files ([ccd734e](https://github.com/meltwater/engage-ads-backend/commit/ccd734e13a0e5ceedd469263cec246630be0f4e0))
- Use a more specific type for the `body` parameter in the `createEvent` function ([ec3e0ed](https://github.com/meltwater/engage-ads-backend/commit/ec3e0ed908e12ad46837a928c706836c6f29b71d))
- Use named imports from SDK instead of importing everything ([25a7ae9](https://github.com/meltwater/engage-ads-backend/commit/25a7ae9494f6e515b2d5f5ab394f8eb5d7038ce0))
- Use package functions directly and update named imports ([d18c3c2](https://github.com/meltwater/engage-ads-backend/commit/d18c3c2cc25249a7466ad47d88eadff5bd4b5407))

## [1.1.3-ENGAGE.4](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.1.3-ENGAGE.4) (2024-08-13)

### Bug Fixes

- addressing linting concerns ([79fccbe](https://github.com/meltwater/engage-ads-backend/commit/79fccbea4e9a33ac4678c161fdf753e27290b219))
- removing operationName for getTikTokAdAccounts ([fc816c2](https://github.com/meltwater/engage-ads-backend/commit/fc816c2e74ef08cf7673c480f9221cb401ecbdd1))
- Update createEvent function to use any type for body parameter ([b5ef65e](https://github.com/meltwater/engage-ads-backend/commit/b5ef65ee11be6c980f62a89f6e93fffe872e45a6))
- updating version ([51d62aa](https://github.com/meltwater/engage-ads-backend/commit/51d62aaf217a3a4cd5b54a424d2d7f928796f49f))

### Features

- integrating sdk ([b0e819e](https://github.com/meltwater/engage-ads-backend/commit/b0e819e53410c1da9d6a5895f113f6ff86101fa8))
- Remove dependency on JSON files in lambda-tester.ts ([d27775e](https://github.com/meltwater/engage-ads-backend/commit/d27775e5f6521525de5011fdcb4a6cac8af81fbf))
- removing json files for queries and mutations testing ([05174e4](https://github.com/meltwater/engage-ads-backend/commit/05174e45c489813e2d112b352bb46ef52805bb55))
- Update lambda-tester script to use engage-ads-graphql-sdk ([c14aeba](https://github.com/meltwater/engage-ads-backend/commit/c14aebac37cbfca72caebeec433ad9a264eab70f))
- Update lambda-tester to use operation type and JSON files ([ccd734e](https://github.com/meltwater/engage-ads-backend/commit/ccd734e13a0e5ceedd469263cec246630be0f4e0))
- Use a more specific type for the `body` parameter in the `createEvent` function ([ec3e0ed](https://github.com/meltwater/engage-ads-backend/commit/ec3e0ed908e12ad46837a928c706836c6f29b71d))
- Use named imports from SDK instead of importing everything ([25a7ae9](https://github.com/meltwater/engage-ads-backend/commit/25a7ae9494f6e515b2d5f5ab394f8eb5d7038ce0))
- Use package functions directly and update named imports ([d18c3c2](https://github.com/meltwater/engage-ads-backend/commit/d18c3c2cc25249a7466ad47d88eadff5bd4b5407))

## [1.1.3-ENGAGE.3](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.1.3-ENGAGE.3) (2024-08-13)

### Bug Fixes

- addressing linting concerns ([79fccbe](https://github.com/meltwater/engage-ads-backend/commit/79fccbea4e9a33ac4678c161fdf753e27290b219))

### Features

- Use a more specific type for the `body` parameter in the `createEvent` function ([ec3e0ed](https://github.com/meltwater/engage-ads-backend/commit/ec3e0ed908e12ad46837a928c706836c6f29b71d))

## [1.1.3-ENGAGE.2](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.1.3-ENGAGE.2) (2024-08-13)

### Features

- removing json files for queries and mutations testing ([05174e4](https://github.com/meltwater/engage-ads-backend/commit/05174e45c489813e2d112b352bb46ef52805bb55))

## [1.1.3-ENGAGE.1](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.1.3-ENGAGE.1) (2024-08-13)

### Bug Fixes

- Update createEvent function to use any type for body parameter ([b5ef65e](https://github.com/meltwater/engage-ads-backend/commit/b5ef65ee11be6c980f62a89f6e93fffe872e45a6))

### Features

- integrating sdk ([b0e819e](https://github.com/meltwater/engage-ads-backend/commit/b0e819e53410c1da9d6a5895f113f6ff86101fa8))
- Remove dependency on JSON files in lambda-tester.ts ([d27775e](https://github.com/meltwater/engage-ads-backend/commit/d27775e5f6521525de5011fdcb4a6cac8af81fbf))
- Update lambda-tester script to use engage-ads-graphql-sdk ([c14aeba](https://github.com/meltwater/engage-ads-backend/commit/c14aebac37cbfca72caebeec433ad9a264eab70f))
- Update lambda-tester to use operation type and JSON files ([ccd734e](https://github.com/meltwater/engage-ads-backend/commit/ccd734e13a0e5ceedd469263cec246630be0f4e0))
- Use named imports from SDK instead of importing everything ([25a7ae9](https://github.com/meltwater/engage-ads-backend/commit/25a7ae9494f6e515b2d5f5ab394f8eb5d7038ce0))
- Use package functions directly and update named imports ([d18c3c2](https://github.com/meltwater/engage-ads-backend/commit/d18c3c2cc25249a7466ad47d88eadff5bd4b5407))

## [1.1.3-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.1.3-ENGAGE.0) (2024-08-13)

### Bug Fixes

- removing operationName for getTikTokAdAccounts ([fc816c2](https://github.com/meltwater/engage-ads-backend/commit/fc816c2e74ef08cf7673c480f9221cb401ecbdd1))
- updating version ([51d62aa](https://github.com/meltwater/engage-ads-backend/commit/51d62aaf217a3a4cd5b54a424d2d7f928796f49f))

# [1.1.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.1.0) (2024-08-10)

### Features

- adding apollo sandbox get /graphql route for staging environmen… ([#266](https://github.com/meltwater/engage-ads-backend/issues/266)) ([794a66b](https://github.com/meltwater/engage-ads-backend/commit/794a66b74cdc220cb5712872f3c6249238c8f5d8))

## [1.0.174-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.174-ENGAGE.0) (2024-08-10)

### Bug Fixes

- updating version for package ([b3bc5a4](https://github.com/meltwater/engage-ads-backend/commit/b3bc5a445775b946a195cc6ac7cd58358f3441f3))

## [1.0.171](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.171) (2024-08-09)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.172-ENGAGE.1](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.172-ENGAGE.1) (2024-08-09)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.172-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.172-ENGAGE.0) (2024-08-08)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.170](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.170) (2024-08-07)

### Bug Fixes

- passing sortby parameter ([#265](https://github.com/meltwater/engage-ads-backend/issues/265)) ([11b9abd](https://github.com/meltwater/engage-ads-backend/commit/11b9abd5324532f1a00cfc22a92e2e2e6d052e13))

## [1.0.170-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.170-ENGAGE.0) (2024-08-07)

### Bug Fixes

- addressing lint concerns ([feef7ef](https://github.com/meltwater/engage-ads-backend/commit/feef7ef8997f85b1409637147c4341863b8406db))
- passing sortby parameter ([a23e464](https://github.com/meltwater/engage-ads-backend/commit/a23e4648dd7573e3d35a72c2ac5cb7f6d5f4989b))
- updating version ([a004d1a](https://github.com/meltwater/engage-ads-backend/commit/a004d1a99f1f627da474d8e7bdefc6cf9f791c8f))
- updating version ([c258de3](https://github.com/meltwater/engage-ads-backend/commit/c258de34d5395c951514550bb2bfff1aca2e5800))

## [1.0.165](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.165) (2024-08-05)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.165-ENGAGE.8](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.165-ENGAGE.8) (2024-08-05)

### Bug Fixes

- removing logs for incoming headers ([52a3684](https://github.com/meltwater/engage-ads-backend/commit/52a3684e6f8a3079c606902da1ab140463993808))
- updating header ([9fac3a9](https://github.com/meltwater/engage-ads-backend/commit/9fac3a9afd16ecbba0ffd27b1614787491c91bd3))

## [1.0.165-ENGAGE.7](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.165-ENGAGE.7) (2024-08-05)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.165-ENGAGE.6](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.165-ENGAGE.6) (2024-08-05)

### Bug Fixes

- replacing X-TikTok-ADS-API-Key header with Authorization header ([98d46b4](https://github.com/meltwater/engage-ads-backend/commit/98d46b43922483c9dd149e5a0df528526008a834))

## [1.0.165-ENGAGE.5](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.165-ENGAGE.5) (2024-08-05)

### Bug Fixes

- removing comments ([d7a0c78](https://github.com/meltwater/engage-ads-backend/commit/d7a0c780a5f6ee415523bc31e0bf3ae2a93fabac))

## [1.0.165-ENGAGE.4](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.165-ENGAGE.4) (2024-08-05)

### Bug Fixes

- replacing console.error with logger.error ([05dc7b4](https://github.com/meltwater/engage-ads-backend/commit/05dc7b48da456763a9e7c7b2766971e4c4daf850))

## [1.0.165-ENGAGE.3](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.165-ENGAGE.3) (2024-07-31)

### Bug Fixes

- TEST_ACCESS_TOKEN as empty string for lambda tester ([79872c9](https://github.com/meltwater/engage-ads-backend/commit/79872c9839243997341054a7fbb336a612cb4715))

## [1.0.165-ENGAGE.2](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.165-ENGAGE.2) (2024-07-31)

### Features

- SECTION31_STAGING_BASE_URL environment variable for lambda ([4326846](https://github.com/meltwater/engage-ads-backend/commit/4326846aa1f988d91b43bef6567d657820fc6bb8))

## [1.0.165-ENGAGE.1](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.165-ENGAGE.1) (2024-07-30)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.165-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.165-ENGAGE.0) (2024-07-30)

### Features

- tiktok profile and account selector ([df8b14a](https://github.com/meltwater/engage-ads-backend/commit/df8b14a68f5e03d2108f7d6a717d5439a7088de7))

## [1.0.164](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.164) (2024-07-30)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.163](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.163) (2024-07-30)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.162](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.162) (2024-07-30)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.162-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.162-ENGAGE.0) (2024-07-30)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.161](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.161) (2024-07-30)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.161-ENGAGE.2](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.161-ENGAGE.2) (2024-07-30)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.161-ENGAGE.1](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.161-ENGAGE.1) (2024-07-30)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.161-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.161-ENGAGE.0) (2024-07-24)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.160](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.160) (2024-07-24)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.159](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.159) (2024-07-12)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.158](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.158) (2024-07-02)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.157](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.157) (2024-07-01)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.156](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.156) (2024-07-01)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.155](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.155) (2024-07-01)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.154](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.154) (2024-07-01)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.153](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.153) (2024-07-01)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.152](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.152) (2024-06-29)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.151](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.151) (2024-06-29)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.150](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.150) (2024-06-29)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.149](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.149) (2024-06-29)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.148](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.148) (2024-06-29)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.147](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.147) (2024-06-29)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.146](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.146) (2024-06-29)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.145](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.145) (2024-06-29)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.144](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.144) (2024-06-29)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.143](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.143) (2024-06-29)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.142](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.142) (2024-06-29)

### Bug Fixes

- adding additional delay for cold starts ([ea4552c](https://github.com/meltwater/engage-ads-backend/commit/ea4552c27cc0ab833bd3dac29782a0089f40c486))

## [1.0.141](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.141) (2024-06-29)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.140](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.140) (2024-06-29)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.139](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.139) (2024-06-29)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.138](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.138) (2024-06-29)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.137](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.137) (2024-06-29)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.136](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.136) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.135](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.135) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.134](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.134) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.133](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.133) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.132](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.132) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.131](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.131) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.130](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.130) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.129](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.129) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.128](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.128) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.127](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.127) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.126](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.126) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.125](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.125) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.124](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.124) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.123](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.123) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.122](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.122) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.121](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.121) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.120](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.120) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.119](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.119) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.118](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.118) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.117](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.117) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.116](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.116) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.115](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.115) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.114](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.114) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.113](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.113) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.112](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.112) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.111](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.111) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.110](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.110) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.109](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.109) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.108](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.108) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.107](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.107) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.106](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.106) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.105](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.105) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.104](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.104) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.103](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.103) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.102](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.102) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.101](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.101) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.100](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.100) (2024-06-27)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.99](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.99) (2024-06-25)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.98](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.98) (2024-06-25)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.97](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.97) (2024-06-25)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.96](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.96) (2024-06-25)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.95](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.95) (2024-06-25)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.94](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.94) (2024-06-25)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.93](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.93) (2024-06-25)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.92](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.92) (2024-06-25)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.91](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.91) (2024-06-25)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.90](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.90) (2024-06-25)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.89](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.89) (2024-06-25)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.88](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.88) (2024-06-24)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.87](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.87) (2024-06-21)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.86](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.86) (2024-06-20)

### Bug Fixes

- all packages and ci/cd ([b2ca618](https://github.com/meltwater/engage-ads-backend/commit/b2ca6181da184acdd6248806b576e6fe9bfb88e4))
- packages ([bc381d8](https://github.com/meltwater/engage-ads-backend/commit/bc381d8929321fa899ac3f8b4f3135f5a45b096c))

## [1.0.84](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.84) (2024-06-20)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.78](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.78) (2024-06-18)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.77](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.77) (2024-06-18)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.76](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.76) (2024-06-18)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.75](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.75) (2024-06-18)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.74](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.74) (2024-06-18)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.73](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.73) (2024-06-18)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.72](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.72) (2024-06-18)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.71](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.71) (2024-06-18)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.70](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.70) (2024-06-15)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.69](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.69) (2024-06-15)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.68](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.68) (2024-06-15)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.67](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.67) (2024-06-15)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.66](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.66) (2024-06-15)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.65](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.65) (2024-06-15)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.64](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.64) (2024-06-15)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.63](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.63) (2024-06-15)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.62](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.62) (2024-06-15)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.61](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.61) (2024-06-15)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.60](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.60) (2024-06-15)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.59](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.59) (2024-06-15)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.58](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.58) (2024-06-14)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.57](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.57) (2024-06-14)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.56](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.56) (2024-06-13)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.55](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.55) (2024-06-13)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.54](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.54) (2024-06-13)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.53](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.53) (2024-06-13)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.52](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.52) (2024-06-12)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.51](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.51) (2024-06-12)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.50](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.50) (2024-06-12)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.49](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.49) (2024-06-12)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.48](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.48) (2024-06-12)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.47](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.47) (2024-06-11)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.46](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.46) (2024-06-11)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.45](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.45) (2024-06-11)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.44](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.44) (2024-06-11)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.43](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.43) (2024-06-11)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.42](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.42) (2024-06-10)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.41](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.41) (2024-06-10)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.40](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.40) (2024-06-10)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.39](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.39) (2024-06-10)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.38](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.38) (2024-06-10)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.37](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.37) (2024-06-10)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.36](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.36) (2024-06-10)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.35](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.35) (2024-06-04)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.34](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.34) (2024-06-04)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.33](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.33) (2024-06-04)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.32](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.32) (2024-06-04)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.31](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.31) (2024-06-03)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.30](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.30) (2024-06-02)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.29](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.29) (2024-06-02)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.28](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.28) (2024-06-02)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.27](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.27) (2024-06-02)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.26](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.26) (2024-06-02)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.25](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.25) (2024-06-02)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.24](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.24) (2024-06-02)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.23](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.23) (2024-06-02)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.22](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.22) (2024-06-02)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.21](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.21) (2024-06-02)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.20](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.20) (2024-06-02)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.19](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.19) (2024-06-02)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.18](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.18) (2024-06-02)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.17](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.17) (2024-06-02)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.16](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.16) (2024-06-02)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.15](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.15) (2024-06-02)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.14](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.14) (2024-06-02)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.13](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.13) (2024-06-02)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.12](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.12) (2024-06-02)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.11](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.11) (2024-06-02)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.10](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.10) (2024-06-02)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.9](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.9) (2024-06-02)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.8](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.8) (2024-06-02)

**Note:** Version bump only for package engage-ads-graphql

## [1.0.7](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads-graphql@1.0.7) (2024-06-02)

**Note:** Version bump only for package engage-ads-graphql

## 1.0.6 (2024-06-01)

### Reverts

- Revert "refactoring codebase" (#81) ([355e5a3](https://github.com/meltwater/engage-ads-backend/commit/355e5a3d7950457cce0ca6bbde976ff8697b1040)), closes [#81](https://github.com/meltwater/engage-ads-backend/issues/81) [#80](https://github.com/meltwater/engage-ads-backend/issues/80)

## [1.0.3](https://github.com/meltwater/engage-ads-backend/compare/<EMAIL>-ads@1.0.3) (2024-05-31)

**Note:** Version bump only for package engage-ads

## 1.0.1 (2024-05-31)

### Reverts

- Revert "refactoring lambda to engage-ads from test-lambda (#57)" ([6e3e3de](https://github.com/meltwater/engage-ads-backend/commit/6e3e3de5671edae8e15fe9a8f2fba3396018ea54)), closes [#57](https://github.com/meltwater/engage-ads-backend/issues/57)
