import {
  engageAdsChannelInsights,
  tikTokAdAccounts,
  tikTokRegions,
  tikTokAdvertisersInfo,
  queryCampaigns,
  getFacebookAccountsPages,
  getFacebookAdCampaigns,
  getFacebookAdAccountMinBudgets,
  getFacebookSavedAudience,
  getLinkedInAdAccounts,
  getLinkedInAssociatedPages,
  getLinkedInAdCampaigns,
} from "../queries";
import {
  createTiktokSparkAd,
  boostFacebookPost,
  createLinkedInTextAd,
  createLinkedInImageAd,
  createLinkedInVideoAd,
  createLinkedInDocumentAd,
} from "../mutations";

export const resolvers = {
  Query: {
    engageAdsChannelInsights,
    tikTokAdAccounts,
    tikTokRegions,
    tikTokAdvertisersInfo,
    queryCampaigns,
    getFacebookAccountsPages,
    getFacebookAdCampaigns,
    getFacebookAdAccountMinBudgets,
    getFacebookSavedAudience,
    getLinkedInAdAccounts,
    getLinkedInAssociatedPages,
    getLinkedInAdCampaigns,
  },
  Mutation: {
    engageAdsCreateTiktokSparkAd: createTiktokSparkAd,
    engageAdsCreateLinkedInTextAd: createLinkedInTextAd,
    engageAdsCreateLinkedInImageAd: createLinkedInImageAd,
    engageAdsCreateLinkedInVideoAd: createLinkedInVideoAd,
    engageAdsCreateLinkedInDocumentAd: createLinkedInDocumentAd,
    boostFacebookPost: boostFacebookPost,
  },
};
