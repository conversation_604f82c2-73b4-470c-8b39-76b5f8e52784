import { <PERSON><PERSON><PERSON><PERSON><PERSON>, LambdaTracer } from "@meltwater/lambda-monitoring";
import { HttpClient } from "@meltwater/engage-ads-commons";
import { InternalServicesCommandQueryFactory, getEnvVar } from "@meltwater/grimoire-publish-services";
import { LinkedInCampaignRepository, GetLinkedInCampaignsQuery } from "@meltwater/engage-ads-linkedin-services";
import type { ExtendedGetLinkedInCampaignsParams, LinkedInCampaign } from "@meltwater/engage-ads-linkedin-services";

const logger = LambdaLogger.getInstance();
const tracer = LambdaTracer.getInstance();

const COMMON_HEADERS = { "x-client-name": "engage-ads" };

export class LinkedInAdCampaignsService {
  private internalFactory: InternalServicesCommandQueryFactory;

  constructor() {
    const authToken = getEnvVar("GRAPHQL_S31_KEY");
    const baseUrl = getEnvVar("GRAPHQL_S31_URL");

    this.internalFactory = InternalServicesCommandQueryFactory.createInstance({
      assetManagerConfig: {
        authToken: "",
        baseUrl: "",
        headers: COMMON_HEADERS,
      },
      assetManagerTVMConfig: {
        authToken: "",
        baseUrl: "",
        headers: COMMON_HEADERS,
      },
      credentialConfig: {
        authToken,
        baseUrl,
        headers: COMMON_HEADERS,
      },
      entitlementsConfig: {
        authToken: "",
        baseUrl: "",
        headers: COMMON_HEADERS,
      },
      identityConfig: {
        authToken,
        baseUrl,
        headers: COMMON_HEADERS,
      },
      eventReceiverConfig: {
        authToken: "",
        baseUrl: "",
        headers: COMMON_HEADERS,
      },
    });
  }

  @tracer.captureMethod()
  public async getLinkedInAdCampaigns(input: {
    companyId: string;
    credentialId: number;
    adAccountId: string;
    status?: string;
    type?: string;
  }): Promise<LinkedInCampaign[]> {
    LambdaTracer.addCustomAttributes({
      "meltwater.company.id": input.companyId,
      "meltwater.social.credential_id": input.credentialId,
    });

    logger.info("Fetching LinkedIn ad campaigns", { input });

    // 1. get access token from Section31
    const credential = await this.internalFactory.createSocialCredentialQuery().execute({
      companyId: input.companyId,
      credentialId: input.credentialId,
    });

    if (!credential || !credential.token) {
      throw new Error("Failed to retrieve LinkedIn access token");
    }

    const accessToken = credential.token;

    // 2. Build repository and query
    const httpClient = new HttpClient({
      baseURL: process.env.LINKEDIN_API_BASE_URL ?? "https://api.linkedin.com",
      defaultHeaders: COMMON_HEADERS,
    });

    const repository = new LinkedInCampaignRepository(httpClient);
    const query = new GetLinkedInCampaignsQuery(repository);

    const params: ExtendedGetLinkedInCampaignsParams = {
      adAccountId: input.adAccountId,
      status: input.status,
      type: input.type,
      accessToken,
    };

    return query.execute(params);
  }
}

export const linkedInAdCampaignsService = new LinkedInAdCampaignsService();
