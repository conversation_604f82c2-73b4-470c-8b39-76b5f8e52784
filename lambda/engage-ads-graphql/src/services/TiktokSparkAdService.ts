import { Lambda<PERSON>og<PERSON>, LambdaTracer } from "@meltwater/lambda-monitoring";
import type { CreateTiktokSparkAdArgs } from "../types/createTiktokSparkAdTypes";
import { InternalServicesCommandQueryFactory, getEnvVar } from "@meltwater/grimoire-publish-services";
import type { CreateTiktokSparkAdResponse } from "@meltwater/engage-ads-tiktok-services/dist/types/tiktok-spark-ads";
import { CreateSparkAdCommand } from "@meltwater/engage-ads-tiktok-services/dist/commands/create-spark-ad.command";
import { TiktokBoostAdRepository } from "@meltwater/engage-ads-tiktok-services/dist/tiktok-boost-ad-repository";
import { BoostAds, connectToDatabase } from "@meltwater/engage-ads-db-sdk";

const logger = LambdaLogger.getInstance();
const tracer = LambdaTracer.getInstance();

const COMMON_HEADERS = { "x-client-name": "engage-ads" };

export class TiktokSparkAdService {
  private internalServicesFactory: InternalServicesCommandQueryFactory;

  constructor() {
    this.internalServicesFactory = InternalServicesCommandQueryFactory.createInstance({
      identityConfig: {
        authToken: getEnvVar("GRAPHQL_S31_KEY"),
        baseUrl: getEnvVar("GRAPHQL_S31_URL"),
        headers: COMMON_HEADERS,
      },
      credentialConfig: {
        authToken: getEnvVar("GRAPHQL_S31_KEY"),
        baseUrl: getEnvVar("GRAPHQL_S31_URL"),
        headers: COMMON_HEADERS,
      },
      entitlementsConfig: {
        authToken: getEnvVar("GRAPHQL_S31_KEY"),
        baseUrl: getEnvVar("GRAPHQL_S31_URL"),
        headers: COMMON_HEADERS,
      },
    });
  }

  @tracer.captureMethod()
  public async createSparkAd(args: CreateTiktokSparkAdArgs): Promise<CreateTiktokSparkAdResponse> {
    LambdaTracer.addCustomAttributes({
      "meltwater.company.id": args.params.companyId,
      "meltwater.social.credential_id": args.params.credentialId,
      "internal.engage.ads.campaign": args.params.campaign,
      "internal.engage.ads.adgroup": args.params.adGroup,
    });

    logger.info("Creating TikTok Spark Ad", {
      "internal.engage.ads.args": JSON.stringify(args),
    });

    try {
      // Establish database connection
      await connectToDatabase();

      // Fetch the TikTok access token
      const credential = await this.internalServicesFactory.createSocialCredentialQuery().execute({
        companyId: args.params.companyId,
        credentialId: args.params.credentialId,
      });

      if (!credential || !credential.token) {
        throw new Error("Failed to retrieve TikTok access token");
      }

      const accessToken = credential.token;
      logger.info("TikTok access token retrieved", {
        "meltwater.social.credential_id": args.params.credentialId,
      });

      const repository = new TiktokBoostAdRepository(accessToken);
      const command = new CreateSparkAdCommand(repository);
      const result = await command.execute(args.params);

      logger.info("Spark Ad creation completed", {
        "internal.engage.ads.response": JSON.stringify(result),
      });

      // Store campaign data in database
      if (result.campaign && result.adGroup && result.ad) {
        const campaignData = {
          campaign_id: result.campaign.campaignId!,
          campaign_name: result.campaign.campaignName!,
          advertiser_id: result.campaign.advertiserId!,
        };

        const adGroupData = {
          adgroup_id: result.adGroup.adgroupId!,
          adgroup_name: result.adGroup.adgroupName!,
          advertiser_id: result.adGroup.advertiserId!,
          campaign_id: result.adGroup.campaignId!,
          schedule_end_time: result.adGroup.scheduleEndTime,
        };

        const adData = {
          ad_id: result.ad.adId!,
          ad_name: result.ad.adName!,
          advertiser_id: result.ad.advertiserId!,
          campaign_id: result.ad.campaignId!,
          adgroup_id: result.ad.adgroupId!,
          video_id: args.params.ad.creatives[0].videoId,
          schedule_end_time: result.adGroup.scheduleEndTime,
        };

        const { campaign } = await BoostAds.boostTiktokads(campaignData, adGroupData, adData, args.params.companyId);

        logger.info("Database records created", {
          "meltwater.campaign.id": campaign.campaign_id,
          "internal.engage.ads.response": JSON.stringify(campaign),
        });
      }

      return result;
    } catch (error) {
      logger.error("Failed to create TikTok Spark Ad", {
        "internal.engage.ads.error": error instanceof Error ? error.message : "Unknown error",
        "internal.engage.ads.args": JSON.stringify(args),
      });
      throw error;
    }
  }
}

export const tiktokSparkAdService = new TiktokSparkAdService();
