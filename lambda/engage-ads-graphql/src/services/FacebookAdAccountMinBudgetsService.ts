import { <PERSON><PERSON><PERSON>og<PERSON>, LambdaTracer } from "@meltwater/lambda-monitoring";
import axios from "axios";
import { GetFacebookAdAccountMinBudgetsQuery } from "@meltwater/engage-ads-facebook-services";
import type {
  FacebookAdAccountMinBudgets,
  GetFacebookAdAccountMinBudgetsParams,
  IFacebookAdAccountMinBudgetsRepository,
} from "@meltwater/engage-ads-facebook-services";
const logger = LambdaLogger.getInstance();
const tracer = LambdaTracer.getInstance();

export class FacebookAdAccountMinBudgetsService {
  private readonly socialPublisherApiUrl: string;

  constructor() {
    this.socialPublisherApiUrl = process.env.SOCIAL_PUBLISHER_API_URL!;
  }

  private createCustomRepository(accessToken: string): IFacebookAdAccountMinBudgetsRepository {
    return {
      getFacebookAdAccountMinBudgets: async (
        params: GetFacebookAdAccountMinBudgetsParams,
      ): Promise<FacebookAdAccountMinBudgets> => {
        const query = `
          query getFacebookAdAccountMinBudgets($credentialId: Int) {
            getFacebookAdAccountMinBudgets(credentialId: $credentialId) {
              currency
              impressionsBudget
              videoBudget
              highFreqBudget
              lowFreqBudget
            }
          }
        `;

        const variables = {
          credentialId: params.credentialId,
        };

        const headers = {
          Authorization: accessToken,
        };

        const response = await axios.post(
          this.socialPublisherApiUrl,
          {
            query,
            variables,
          },
          { headers },
        );

        logger.info("Response received from Social Publisher API", {
          status: response.status,
          statusText: response.statusText,
        });

        return (
          response.data?.data?.getFacebookAdAccountMinBudgets || {
            currency: "USD",
            impressionsBudget: 0,
            videoBudget: 0,
            highFreqBudget: 0,
            lowFreqBudget: 0,
          }
        );
      },
    };
  }

  @tracer.captureMethod({ subSegmentName: "FacebookAdAccountMinBudgetsService:getFacebookAdAccountMinBudgets" })
  public async getFacebookAdAccountMinBudgets(
    credentialId: number,
    accessToken: string,
  ): Promise<FacebookAdAccountMinBudgets> {
    try {
      const repository = this.createCustomRepository(accessToken);

      logger.info("Fetching Facebook ad account minimum budgets", {
        credentialId,
      });

      const query = new GetFacebookAdAccountMinBudgetsQuery(repository);
      const minBudgets = await query.execute({ credentialId });

      return minBudgets;
    } catch (error) {
      logger.error("Failed to get Facebook ad account minimum budgets", {
        error: error instanceof Error ? error.message : String(error),
        credentialId,
      });

      // Return default values in case of error
      return {
        currency: "USD",
        impressionsBudget: 0,
        videoBudget: 0,
        highFreqBudget: 0,
        lowFreqBudget: 0,
      };
    }
  }
}

export const facebookAdAccountMinBudgetsService = new FacebookAdAccountMinBudgetsService();
