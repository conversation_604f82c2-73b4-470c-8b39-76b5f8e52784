import { HttpClient } from "@meltwater/engage-ads-commons";
import type { GetTikTokInsightsQueryParams, TikTokInsights } from "@meltwater/engage-ads-tiktok-services";
import type { GetFacebookInsightsQueryParams, FacebookInsights } from "@meltwater/engage-ads-facebook-services";
import type { GetLinkedInInsightsQueryParams, LinkedInInsights } from "@meltwater/engage-ads-linkedin-services";
import { TikTokInsightsRepository, GetTikTokInsightsQuery } from "@meltwater/engage-ads-tiktok-services";
import { FacebookInsightsRepository, GetFacebookInsightsQuery } from "@meltwater/engage-ads-facebook-services";
import { LinkedInInsightsRepository, GetLinkedInInsightsQuery } from "@meltwater/engage-ads-linkedin-services";
import type { EngageAdsChannelInsightsArgs, Insight } from "../types";
import { Channel } from "../types";
import { <PERSON>daLogger, LambdaTracer } from "@meltwater/lambda-monitoring";
import { BoostAds, connectToDatabase } from "@meltwater/engage-ads-db-sdk";

const logger = LambdaLogger.getInstance();
const tracer = LambdaTracer.getInstance();

export class EngageAdsChannelInsightsService {
  @tracer.captureMethod()
  public async getChannelInsights(
    args: EngageAdsChannelInsightsArgs,
  ): Promise<{ insights: Insight[]; totalCount: number }> {
    LambdaTracer.addCustomAttributes({
      "internal.engage.ads.channels": args.filter.channels.join(","),
    });

    logger.info(`Processing channel insights request with args: ${JSON.stringify(args)}`);

    try {
      const channel = args.filter.channels[0]?.toLowerCase();
      let result: { insights: Insight[]; totalCount: number };

      switch (channel) {
        case "tiktok":
          result = await this.getTikTokInsights(args);
          break;
        case "facebook": {
          const fbResult = await this.getFacebookInsights(args);
          result = { insights: fbResult.insights, totalCount: fbResult.totalCount };
          break;
        }
        case "linkedin": {
          const liResult = await this.getLinkedInInsights(args);
          result = { insights: liResult.insights, totalCount: liResult.totalCount };
          break;
        }
        default:
          logger.warn("Unsupported channel type", {
            "internal.engage.ads.channel": channel,
            n: "1.0",
          });
          result = { insights: [], totalCount: 0 };
      }

      logger.info(`Channel insights retrieved. Total insights: ${result.insights.length}`);

      return result;
    } catch (error) {
      logger.error("Failed to get channel insights", {
        "internal.engage.ads.error": error instanceof Error ? error.message : "Unknown error",
        "internal.engage.ads.args": JSON.stringify(args),
      });
      throw error;
    }
  }

  @tracer.captureMethod()
  private async getTikTokInsights(
    args: EngageAdsChannelInsightsArgs,
  ): Promise<{ insights: Insight[]; totalCount: number }> {
    LambdaTracer.addCustomAttributes({
      "internal.engage.ads.channel": "tiktok",
    });

    const { startDate, endDate, size, order, startFrom, sortBy, profileIds, match, sortByMetric } = args.filter;
    const apiKey = process.env.WC_SOCIAL_DATA_API_Key;
    if (!apiKey) {
      logger.error("Missing API key", {
        "internal.engage.ads.error": "WC_SOCIAL_DATA_API_KEY environment variable is not set",
      });
      throw new Error("Required API key is missing");
    }

    const httpClient = new HttpClient({
      baseURL: process.env.WILDCARDS_STAGING_BASE_URL!,
      defaultHeaders: {
        "Accept-Language": "en-US,en;q=0.9",
        "WC-Social-Data-API-Key": apiKey,
        "Content-Type": "application/json",
      },
    });

    const matchObject = match ? { captionKeyword: match.captionKeyword } : undefined;

    const queryParams: GetTikTokInsightsQueryParams = {
      startDate,
      endDate,
      size,
      order,
      startFrom,
      sortBy,
      profileIds: profileIds,
      match: matchObject,
      sortByMetric,
    };

    const tikTokInsightsRepository = new TikTokInsightsRepository(httpClient);
    const query = new GetTikTokInsightsQuery(tikTokInsightsRepository);

    const result = await query.execute(queryParams);
    const insights = await this.mapTikTokInsightsToInsights(result.insights);

    logger.info(`TikTok insights retrieved. Total insights: ${insights.length}`);

    return { insights, totalCount: result.totalCount };
  }

  @tracer.captureMethod()
  private async getFacebookInsights(
    args: EngageAdsChannelInsightsArgs,
  ): Promise<{ insights: Insight[]; totalCount: number }> {
    LambdaTracer.addCustomAttributes({
      "internal.engage.ads.channel": "facebook",
    });

    const { startDate, endDate, size, order, startFrom, sortBy, profileIds, match, sortByMetric } = args.filter;
    const apiKey = process.env.WC_SOCIAL_DATA_API_Key;
    if (!apiKey) {
      logger.error("Missing API key", {
        "internal.engage.ads.error": "WC_SOCIAL_DATA_API_KEY environment variable is not set",
      });
      throw new Error("Required API key is missing");
    }

    const httpClient = new HttpClient({
      baseURL: process.env.WILDCARDS_STAGING_BASE_URL!,
      defaultHeaders: {
        "Accept-Language": "en-US,en;q=0.9",
        "WC-Social-Data-API-Key": apiKey,
        "Content-Type": "application/json",
      },
    });

    const matchObject = match ? { captionKeyword: match.captionKeyword } : undefined;

    const queryParams: GetFacebookInsightsQueryParams = {
      start_date: startDate,
      end_date: endDate,
      size: size,
      order: order,
      start_from: startFrom,
      profileIds: profileIds,
      match: matchObject,
      sort_by: sortBy,
      sortByMetric: sortByMetric,
    };

    const facebookInsightsRepository = new FacebookInsightsRepository(httpClient);
    const facebookInsightsQuery = new GetFacebookInsightsQuery(facebookInsightsRepository);

    const facebookInsights = await facebookInsightsQuery.execute(queryParams);

    const insights = await this.mapFacebookInsightsToInsights(facebookInsights.insights);

    logger.info(`Facebook insights retrieved. Total insights: ${insights.length}`);

    return { insights, totalCount: facebookInsights.totalCount };
  }

  private async mapTikTokInsightsToInsights(tikTokInsights: TikTokInsights[]): Promise<Insight[]> {
    try {
      await connectToDatabase();
      const videoIds = tikTokInsights.map((insight) => insight.video_id);
      const boostStatusMap = await BoostAds.checkBoostStatus(videoIds);

      return tikTokInsights.map((source: TikTokInsights) => {
        logger.debug("Processing TikTok insight", {
          "internal.engage.ads.insight": JSON.stringify(source),
        });

        const insight: Insight = {
          postId: source.video_id,
          documentId: source.document_id,
          channel: Channel.TikTok,
          companyId: source.company_id,
          permalink: source.permalink,
          profileId: source.profile_id,
          createTime: source.create_time,
          media: [
            {
              type: "thumbnail",
              url: source.thumbnail_url,
            },
          ],
          caption: source.caption,
          likes: source.likes,
          comments: source.comments,
          isBoosted: false,
          mimeType: source.mimeType,
          metrics: {
            averageWatchTime: source.average_watch_time,
            videoLength: source.video_length,
            engagements: source.engagements,
            engagementRate: source.engagement_rate,
            reach: source.reach,
            videoViews: source.video_views,
            fullVideoWatchRate: source.full_video_watch_rate,
            totalTimeWatched: source.total_time_watched,
            shares: source.shares,
            impressionSources: source.impression_sources?.map((source) => ({
              percentage: source.percentage,
              impressionSource: source.impression_source,
            })),
          },
        };

        // Map boost status to insights
        const boostStatus = boostStatusMap.get(source.video_id);
        if (boostStatus) {
          insight.isBoosted = boostStatus.isBoosted;
          insight.scheduleEndTime = boostStatus.scheduleEndTime;
        }

        logger.debug("Mapped TikTok insight", {
          "internal.engage.ads.mapped_insight": JSON.stringify(insight),
        });

        return insight;
      });
    } catch (error) {
      logger.error("Failed to map TikTok insights", {
        "internal.engage.ads.error": error instanceof Error ? error.message : "Unknown error",
      });
      return [];
    }
  }

  private async mapFacebookInsightsToInsights(facebookInsights: FacebookInsights[]): Promise<Insight[]> {
    try {
      await connectToDatabase();
      const postIds = facebookInsights.map((insight) => insight.id);
      const boostStatusMap = await BoostAds.checkBoostStatus(postIds, "facebook");

      return facebookInsights.map((facebookInsight) => {
        logger.debug("Processing Facebook insight", {
          "internal.engage.ads.insight": JSON.stringify(facebookInsight),
        });

        const insight: Insight = {
          postId: facebookInsight.id,
          documentId: facebookInsight.document_id,
          externalId: facebookInsight.externalId,
          channel: Channel.Facebook,
          companyId: facebookInsight.company_id,
          profileId: facebookInsight.page_id,
          createTime: facebookInsight.created_time,
          media: [
            {
              type: "post",
              url: facebookInsight.permalink_url,
            },
            ...(facebookInsight.thumbnail_url
              ? [
                  {
                    type: "thumbnail",
                    url: facebookInsight.thumbnail_url,
                  },
                ]
              : []),
          ],
          caption: facebookInsight.message,
          likes: facebookInsight.post_reactions_like_total,
          comments: facebookInsight.comments_count,
          isBoosted: false,
          mimeType: facebookInsight.mimeType,
          metrics: {
            averageWatchTime: 0, // Facebook doesn't provide this
            videoLength: 0,
            engagements: facebookInsight.reactions_count,
            engagementRate: facebookInsight.engagement_rate,
            reach: 0,
            videoViews: facebookInsight.post_video_views || 0,
            fullVideoWatchRate: 0,
            totalTimeWatched: 0,
            shares: facebookInsight.shares_count,
            impressionSources: [],
          },
        };

        // Map boost status to insights
        const boostStatus = boostStatusMap.get(facebookInsight.id);
        if (boostStatus) {
          insight.isBoosted = boostStatus.isBoosted;
          insight.scheduleEndTime = boostStatus.scheduleEndTime;
        }

        logger.debug("Mapped Facebook insight", {
          "internal.engage.ads.mapped_insight": JSON.stringify(insight),
        });

        return insight;
      });
    } catch (error) {
      logger.error("Failed to map Facebook insights", {
        "internal.engage.ads.error": error instanceof Error ? error.message : "Unknown error",
      });
      return [];
    }
  }

  @tracer.captureMethod()
  private async getLinkedInInsights(
    args: EngageAdsChannelInsightsArgs,
  ): Promise<{ insights: Insight[]; totalCount: number }> {
    LambdaTracer.addCustomAttributes({
      "internal.engage.ads.channel": "linkedin",
    });

    const { startDate, endDate, size, order, startFrom, sortBy, profileIds, match, sortByMetric } = args.filter;
    const apiKey = process.env.WC_SOCIAL_DATA_API_Key;
    if (!apiKey) {
      logger.error("Missing API key", {
        "internal.engage.ads.error": "WC_SOCIAL_DATA_API_KEY environment variable is not set",
      });
      throw new Error("Required API key is missing");
    }

    const httpClient = new HttpClient({
      baseURL: process.env.WILDCARDS_STAGING_BASE_URL!,
      defaultHeaders: {
        "Accept-Language": "en-US,en;q=0.9",
        "WC-Social-Data-API-Key": apiKey,
        "Content-Type": "application/json",
      },
    });

    const matchObject = match ? { captionKeyword: match.captionKeyword } : undefined;

    const queryParams: GetLinkedInInsightsQueryParams = {
      start_date: startDate,
      end_date: endDate,
      size,
      order,
      start_from: startFrom,
      sort_by: sortBy,
      profileIds,
      match: matchObject,
      sortByMetric,
    };

    const linkedInInsightsRepository = new LinkedInInsightsRepository(httpClient);
    const linkedInInsightsQuery = new GetLinkedInInsightsQuery(linkedInInsightsRepository);

    const result = await linkedInInsightsQuery.execute(queryParams);
    const insights = await this.mapLinkedInInsightsToInsights(result.insights);

    logger.info(`LinkedIn insights retrieved. Total insights: ${insights.length}`);

    return { insights, totalCount: result.totalCount };
  }

  private async mapLinkedInInsightsToInsights(linkedInInsights: LinkedInInsights[]): Promise<Insight[]> {
    try {
      await connectToDatabase();
      const postIds = linkedInInsights.map((insight) => insight.externalId);
      const boostStatusMap = await BoostAds.checkBoostStatus(postIds, "linkedin");

      return linkedInInsights.map((linkedInInsight) => {
        logger.debug("Processing LinkedIn insight", {
          "internal.engage.ads.insight": JSON.stringify(linkedInInsight),
        });

        const insight: Insight = {
          postId: linkedInInsight.externalId, // Use externalId as postId instead of id
          documentId: linkedInInsight.document_id,
          externalId: linkedInInsight.externalId,
          channel: Channel.LinkedIn,
          companyId: linkedInInsight.company_id,
          profileId: linkedInInsight.page_id || "", // Map org_id property to profileId
          createTime: linkedInInsight.created_time,
          media: [
            {
              type: linkedInInsight.mimeType?.includes("video")
                ? "video"
                : linkedInInsight.mimeType?.includes("pdf") || linkedInInsight.mimeType?.includes("document")
                  ? "document"
                  : "image",
              url: linkedInInsight.thumbnail_url || "",
            },
          ],
          caption: linkedInInsight.message,
          likes: linkedInInsight.likes_count,
          comments: linkedInInsight.comments_count,
          isBoosted: false,
          mimeType: linkedInInsight.mimeType,
          metrics: {
            averageWatchTime: 0, // LinkedIn doesn't provide this
            videoLength: 0, // LinkedIn doesn't provide this
            engagements: linkedInInsight.likes_count, // For LinkedIn, engagements is the same as likes_count
            engagementRate: linkedInInsight.engagement_rate,
            reach: linkedInInsight.post_impressions,
            videoViews: linkedInInsight.post_video_views || 0,
            fullVideoWatchRate: linkedInInsight.post_video_completion_rate || 0,
            totalTimeWatched: linkedInInsight.post_video_view_time || 0,
            shares: linkedInInsight.shares_count,
            impressionSources: [], // LinkedIn doesn't provide this
          },
          postType: linkedInInsight.post_type,
          downloadUrl: linkedInInsight.download_url,
          mediaUrn: linkedInInsight.media_urn,
        };

        // Map boost status to insights
        const boostStatus = boostStatusMap.get(linkedInInsight.externalId);
        if (boostStatus) {
          insight.isBoosted = boostStatus.isBoosted;
          insight.scheduleEndTime = boostStatus.scheduleEndTime;
        }

        logger.debug("Mapped LinkedIn insight", {
          "internal.engage.ads.mapped_insight": JSON.stringify(insight),
        });

        return insight;
      });
    } catch (error) {
      logger.error("Failed to map LinkedIn insights", {
        "internal.engage.ads.error": error instanceof Error ? error.message : "Unknown error",
      });
      return [];
    }
  }
}

export const engageAdsChannelInsightsService = new EngageAdsChannelInsightsService();
