import { <PERSON><PERSON><PERSON><PERSON><PERSON>, LambdaLogger } from "@meltwater/lambda-monitoring";
import { InternalServicesCommandQueryFactory, getEnvVar } from "@meltwater/grimoire-publish-services";
import { HttpClient } from "@meltwater/engage-ads-commons";
import { LinkedInBoostAdRepository, CreateLinkedInDocumentAdCommand } from "@meltwater/engage-ads-linkedin-services";
import type {
  LinkedInDocumentAdParams as BaseLinkedInParams,
  CreateLinkedInDocumentAdResponse,
} from "@meltwater/engage-ads-linkedin-services";
import { BoostAds, connectToDatabase } from "@meltwater/engage-ads-db-sdk";
import type { LinkedInAdCreative } from "@meltwater/engage-ads-db-sdk";

const tracer = LambdaTracer.getInstance();
const logger = LambdaLogger.getInstance();

const COMMON_HEADERS = { "x-client-name": "engage-ads" };

export class LinkedInDocumentAdService {
  private internalFactory: InternalServicesCommandQueryFactory;

  constructor() {
    const authToken = getEnvVar("GRAPHQL_S31_KEY");
    const baseUrl = getEnvVar("GRAPHQL_S31_URL");

    this.internalFactory = InternalServicesCommandQueryFactory.createInstance({
      credentialConfig: {
        authToken,
        baseUrl,
        headers: COMMON_HEADERS,
      },
      identityConfig: {
        authToken,
        baseUrl,
        headers: COMMON_HEADERS,
      },
      assetManagerConfig: { authToken: "", baseUrl: "", headers: COMMON_HEADERS },
      assetManagerTVMConfig: { authToken: "", baseUrl: "", headers: COMMON_HEADERS },
      entitlementsConfig: { authToken: "", baseUrl: "", headers: COMMON_HEADERS },
      eventReceiverConfig: { authToken: "", baseUrl: "", headers: COMMON_HEADERS },
    });
  }

  @tracer.captureMethod()
  public async createDocumentAd(params: BaseLinkedInParams): Promise<CreateLinkedInDocumentAdResponse> {
    LambdaTracer.addCustomAttributes({
      "meltwater.company.id": params.companyId,
      "meltwater.social.credential_id": params.credentialId,
    });

    logger.info("Creating LinkedIn Document Ad", { params: JSON.stringify(params) });

    // 1. Retrieve access token for credential
    const credential = await this.internalFactory.createSocialCredentialQuery().execute({
      companyId: params.companyId,
      credentialId: params.credentialId,
    });

    if (!credential || !credential.token) {
      throw new Error("Failed to retrieve LinkedIn access token - credential not found or token missing");
    }

    const accessToken = credential.token;

    // 2. Prepare HttpClient
    const httpClient = new HttpClient({
      baseURL: process.env.LINKEDIN_API_BASE_URL ?? "https://api.linkedin.com",
      defaultHeaders: {
        ...COMMON_HEADERS,
        Authorization: `Bearer ${accessToken}`,
        "X-Restli-Protocol-Version": "2.0.0",
        "Linkedin-Version": process.env.LINKEDIN_VERSION || "202504",
      },
    });

    const repository = new LinkedInBoostAdRepository(httpClient);
    const command = new CreateLinkedInDocumentAdCommand(repository);

    const result = await command.execute(params);

    // Persist in DB if campaign + creative ids are available
    if (result.campaign?.campaignId && result.creative?.creativeId) {
      try {
        await connectToDatabase();

        // 2b. Retrieve campaign details to capture schedule end time and campaign group
        let scheduleEndTime: string | undefined;
        let campaignGroup: string | undefined;
        try {
          const campaignDetailsEndpoint = `/rest/adAccounts/${params.campaign.adAccountId}/adCampaigns/${result.campaign.campaignId}`;
          const campaignDetails = await httpClient.get<{ runSchedule?: { end?: number }; campaignGroup?: string }>(
            campaignDetailsEndpoint,
          );
          const endTimestamp = campaignDetails?.runSchedule?.end;
          if (endTimestamp) {
            scheduleEndTime = new Date(endTimestamp).toISOString();
          }
          if (campaignDetails?.campaignGroup) {
            campaignGroup = campaignDetails.campaignGroup;
          }
        } catch (fetchErr) {
          logger.warn("Failed to fetch LinkedIn campaign details for document ad", {
            error: fetchErr instanceof Error ? fetchErr.message : String(fetchErr),
            campaignId: result.campaign.campaignId,
          });

          // Fallback to params if API call fails
          if (params.campaign.endTimestamp) {
            scheduleEndTime = params.campaign.endTimestamp;
          }
          if (params.campaign.campaignGroupId) {
            campaignGroup = params.campaign.campaignGroupId;
          }
        }

        const adData: LinkedInAdCreative & {
          channel: "linkedin";
          post_id?: string;
          schedule_end_time?: string;
          campaign_group?: string;
        } = {
          ad_id: result.creative.creativeId,
          ad_name: result.creative.creativeName ?? params.creative.adName ?? "",
          advertiser_id: params.advertiserId,
          campaign_id: result.campaign.campaignId,
          adgroup_id: "", // Not applicable for LinkedIn
          video_id: "", // Not applicable for LinkedIn
          channel: "linkedin",
          post_id: params.postId,
          schedule_end_time: scheduleEndTime,
          campaign_group: campaignGroup,
        };

        // Create campaign data for database
        const campaignData = {
          campaign_id: result.campaign.campaignId,
          campaign_name: result.campaign.campaignName ?? params.campaign.campaignName ?? "",
          advertiser_id: params.campaign.adAccountId,
        };

        // Create a new ad record in the database
        await BoostAds.boostLinkedInAds(campaignData, adData, params.companyId);
        logger.info("LinkedIn document ad record created in database", {
          adId: adData.ad_id,
          campaignId: adData.campaign_id,
        });
      } catch (dbError) {
        logger.error("Failed to persist LinkedIn document ad in database", {
          error: dbError instanceof Error ? dbError.message : String(dbError),
          creativeId: result.creative.creativeId,
          campaignId: result.campaign.campaignId,
        });
        // Continue with the response even if DB persistence fails
      }
    }

    return result;
  }
}

export const linkedInDocumentAdService = new LinkedInDocumentAdService();
