import { HttpClient } from "@meltwater/engage-ads-commons";
import type {
  TikTokAdAccountQueryInput,
  TikTokAdAccount,
  ExtendedTikTokAdAccountQueryInput,
} from "@meltwater/engage-ads-tiktok-services";
import { TikTokAdAccountRepository, GetTikTokAdAccountsQuery } from "@meltwater/engage-ads-tiktok-services";
import { LambdaLogger, LambdaTracer } from "@meltwater/lambda-monitoring";

const logger = LambdaLogger.getInstance();
const tracer = LambdaTracer.getInstance();

export class TikTokAdAccountService {
  @tracer.captureMethod()
  public async getTikTokAdAccounts(input: TikTokAdAccountQueryInput, accessToken: string): Promise<TikTokAdAccount[]> {
    LambdaTracer.addCustomAttributes({
      "internal.engage.ads.access_token": "present",
    });

    logger.info("Processing TikTok ad accounts request", {
      "internal.engage.ads.request": JSON.stringify(input),
    });

    try {
      const httpClient = new HttpClient({
        baseURL: process.env.SECTION31_STAGING_BASE_URL!,
        defaultHeaders: {
          "Accept-Language": "en-US,en;q=0.9",
        },
      });

      const tikTokAdAccountRepository = new TikTokAdAccountRepository(httpClient);
      const getTikTokAdAccountsQuery = new GetTikTokAdAccountsQuery(tikTokAdAccountRepository);
      const extendedInput: ExtendedTikTokAdAccountQueryInput = { ...input, accessToken };
      const result = await getTikTokAdAccountsQuery.execute(extendedInput);

      logger.info("TikTok ad accounts retrieved", {
        "internal.engage.ads.response": JSON.stringify(result),
        "internal.engage.ads.count": result.length,
      });

      return result;
    } catch (error) {
      logger.error("Failed to get TikTok ad accounts", {
        "internal.engage.ads.error": error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  }
}

export const tikTokAdAccountService = new TikTokAdAccountService();
