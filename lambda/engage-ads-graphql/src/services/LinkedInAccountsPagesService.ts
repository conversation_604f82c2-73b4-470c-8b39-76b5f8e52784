import { <PERSON><PERSON><PERSON>og<PERSON>, LambdaTracer } from "@meltwater/lambda-monitoring";
import { HttpClient } from "@meltwater/engage-ads-commons";
import { getEnvVar } from "@meltwater/grimoire-publish-services";
import type {
  ExtendedLinkedInAdAccountQueryInput,
  ExtendedAssociatedAccountsQueryInput,
} from "@meltwater/engage-ads-linkedin-services";
import {
  LinkedInAdAccountRepository,
  GetLinkedInAdAccountsQuery,
  GetLinkedInAssociatedAccountsQuery,
} from "@meltwater/engage-ads-linkedin-services";
import type { LinkedInAdAccount, LinkedInCredentialsFilter } from "../types/linkedInAccountsPagesTypes";

const logger = LambdaLogger.getInstance();
const tracer = LambdaTracer.getInstance();

export class LinkedInAccountsPagesService {
  private httpClient: HttpClient;
  private linkedInRepository: LinkedInAdAccountRepository;
  private getLinkedInAdAccountsQuery: GetLinkedInAdAccountsQuery;
  private getLinkedInAssociatedAccountsQuery: GetLinkedInAssociatedAccountsQuery;

  constructor() {
    this.httpClient = new HttpClient({
      baseURL: process.env.SECTION31_STAGING_BASE_URL!,
      defaultHeaders: {
        "Accept-Language": "en-US,en;q=0.9",
      },
    });

    this.linkedInRepository = new LinkedInAdAccountRepository(this.httpClient);
    this.getLinkedInAdAccountsQuery = new GetLinkedInAdAccountsQuery(this.linkedInRepository);
    this.getLinkedInAssociatedAccountsQuery = new GetLinkedInAssociatedAccountsQuery(this.linkedInRepository);
  }

  @tracer.captureMethod()
  public async getLinkedInAdAccounts(input: LinkedInCredentialsFilter) {
    LambdaTracer.addCustomAttributes({
      "user.id": input.userId,
    });

    // Retrieve your S31 token from env or from credentials if needed
    const s31Token = getEnvVar("GRAPHQL_S31_KEY");
    if (!s31Token) {
      throw new Error("Missing GRAPHQL_S31_KEY for S31 authorization");
    }

    try {
      // Get LinkedIn ad accounts using companyCredentialsFilteredQuery with 'linkedinads' channel
      const adAccountsInput: ExtendedLinkedInAdAccountQueryInput = {
        ...input,
        channels: ["linkedinads"], // Only get ad accounts
        statusInd: ["VALID", "INVALID", "EXPIRED"], // Include all status types
        accessToken: s31Token,
      };

      const adAccounts = await this.getLinkedInAdAccountsQuery.execute(adAccountsInput);
      logger.info(`Retrieved ${adAccounts.length} LinkedIn ad accounts`);

      return adAccounts as LinkedInAdAccount[];
    } catch (error) {
      logger.error("Error retrieving LinkedIn ad accounts", {
        "internal.engage.ads.error": error instanceof Error ? error.message : "Unknown error",
        "internal.engage.ads.request": JSON.stringify(input),
      });
      throw error;
    }
  }

  @tracer.captureMethod()
  public async getLinkedInAssociatedPages(credentialId: number, companyId: string) {
    LambdaTracer.addCustomAttributes({
      "company.id": companyId,
      "credential.id": credentialId,
    });

    // Retrieve your S31 token from env or from credentials if needed
    const s31Token = getEnvVar("GRAPHQL_S31_KEY");
    if (!s31Token) {
      throw new Error("Missing GRAPHQL_S31_KEY for S31 authorization");
    }

    try {
      const associatedAccountsInput: ExtendedAssociatedAccountsQueryInput = {
        credentialId,
        companyId,
        accessToken: s31Token,
      };

      const associatedPages = await this.getLinkedInAssociatedAccountsQuery.execute(associatedAccountsInput);
      logger.info(`Retrieved ${associatedPages.length} associated pages for ad account ${credentialId}`);

      return associatedPages;
    } catch (error) {
      logger.error("Error retrieving LinkedIn associated pages", {
        "internal.engage.ads.error": error instanceof Error ? error.message : "Unknown error",
        "credential.id": credentialId,
      });
      throw error;
    }
  }
}

export const linkedInAccountsPagesService = new LinkedInAccountsPagesService();
