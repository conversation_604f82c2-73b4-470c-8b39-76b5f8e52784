import { <PERSON><PERSON><PERSON><PERSON><PERSON>, LambdaTracer } from "@meltwater/lambda-monitoring";
import { HttpClient } from "@meltwater/engage-ads-commons";
import type { ExtendedFacebookAdAccountsPagesQueryInput } from "@meltwater/engage-ads-facebook-services";
import {
  FacebookAdAccountsPagesRepository,
  GetFacebookAdAccountsPagesQuery,
} from "@meltwater/engage-ads-facebook-services";
import { getEnvVar } from "@meltwater/grimoire-publish-services";

const logger = LambdaLogger.getInstance();
const tracer = LambdaTracer.getInstance();

export class FacebookAdAccountsPagesService {
  private httpClient: HttpClient;
  private fbRepository: FacebookAdAccountsPagesRepository;
  private getFacebookAdAccountsPagesQuery: GetFacebookAdAccountsPagesQuery;

  constructor() {
    this.httpClient = new HttpClient({
      baseURL: process.env.SECTION31_STAGING_BASE_URL!,
      defaultHeaders: {
        "Accept-Language": "en-US,en;q=0.9",
      },
    });

    this.fbRepository = new FacebookAdAccountsPagesRepository(this.httpClient);
    this.getFacebookAdAccountsPagesQuery = new GetFacebookAdAccountsPagesQuery(this.fbRepository);
  }

  @tracer.captureMethod()
  public async getFacebookAdAccountsPages(input: Omit<ExtendedFacebookAdAccountsPagesQueryInput, "accessToken">) {
    LambdaTracer.addCustomAttributes({
      "user.id": input.userId,
    });

    // Retrieve your S31 token from env or from credentials if needed
    const s31Token = getEnvVar("GRAPHQL_S31_KEY");
    if (!s31Token) {
      throw new Error("Missing GRAPHQL_S31_KEY for S31 authorization");
    }

    // Build the extended input
    const extendedInput: ExtendedFacebookAdAccountsPagesQueryInput = {
      ...input,
      accessToken: s31Token,
    };

    logger.info("Requesting FB ad accounts/pages data from S31", {
      "internal.engage.ads.request": JSON.stringify(extendedInput),
    });

    try {
      const result = await this.getFacebookAdAccountsPagesQuery.execute(extendedInput);

      logger.info("FB ad accounts/pages retrieved", {
        "internal.engage.ads.response": JSON.stringify(result),
      });

      return result;
    } catch (error) {
      logger.error("Failed to get FB ad accounts/pages", {
        "internal.engage.ads.error": error,
        "user.id": input.userId,
      });
      throw error;
    }
  }
}

export const facebookAdAccountsPagesService = new FacebookAdAccountsPagesService();
