import type { GetTiktokRegionResponse } from "@meltwater/engage-ads-tiktok-services";
import { GetTikTokRegionsQuery, TiktokBoostAdRepository } from "@meltwater/engage-ads-tiktok-services";
import { Lamb<PERSON>Logger, LambdaTracer } from "@meltwater/lambda-monitoring";
import { InternalServicesCommandQueryFactory, getEnvVar } from "@meltwater/grimoire-publish-services";
import type { GetTiktokRegionArgs } from "../types";

const logger = LambdaLogger.getInstance();
const tracer = LambdaTracer.getInstance();
const COMMON_HEADERS = { "x-client-name": "engage-ads" };

export class TiktokRegionService {
  private internalServicesFactory: ReturnType<typeof InternalServicesCommandQueryFactory.createInstance>;

  constructor() {
    const authToken = "";

    this.internalServicesFactory = InternalServicesCommandQueryFactory.createInstance({
      assetManagerConfig: {
        authToken,
        baseUrl: "",
        headers: {
          "apollographql-client-name": "engage-ads",
          "Content-Type": "application/json",
          ...COMMON_HEADERS,
        },
      },
      assetManagerTVMConfig: {
        authToken: "",
        baseUrl: "",
        headers: COMMON_HEADERS,
      },
      credentialConfig: {
        authToken: getEnvVar("GRAPHQL_S31_KEY"),
        baseUrl: getEnvVar("GRAPHQL_S31_URL"),
        headers: COMMON_HEADERS,
      },
      entitlementsConfig: {
        authToken,
        baseUrl: "",
        headers: COMMON_HEADERS,
      },
      identityConfig: {
        authToken,
        baseUrl: "",
        headers: COMMON_HEADERS,
      },
      eventReceiverConfig: {
        authToken: "",
        baseUrl: "",
        headers: COMMON_HEADERS,
      },
    });
  }
  @tracer.captureMethod()
  public async getTiktokRegion(args: GetTiktokRegionArgs): Promise<GetTiktokRegionResponse> {
    logger.info("Received request for TikTok regions with input:", JSON.stringify(args));
    try {
      const { companyId, credentialId } = args.input;

      // Fetch the TikTok access token
      const credential = await this.internalServicesFactory.createSocialCredentialQuery().execute({
        companyId,
        credentialId,
      });

      if (!credential || !credential.token) {
        throw new Error("Failed to retrieve TikTok access token");
      }

      const accessToken = credential.token;
      logger.info("Generated access token for TikTok API");

      const tiktokBoostAdRepository = new TiktokBoostAdRepository(accessToken);
      const getTikTokRegionsQuery = new GetTikTokRegionsQuery(tiktokBoostAdRepository);

      const result = await getTikTokRegionsQuery.execute(args.input);
      logger.info("Query response", { regions: result.data.length });

      return result;
    } catch (error) {
      logger.error("Error in getting tiktok region", { error });
      throw error;
    }
  }
}

export const tiktokRegionService = new TiktokRegionService();
