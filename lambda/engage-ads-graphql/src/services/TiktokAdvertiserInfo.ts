import { Lambda<PERSON>og<PERSON>, LambdaTracer } from "@meltwater/lambda-monitoring";
import type { GetTiktokAdvertisersInfoParams } from "@meltwater/engage-ads-graphql-sdk/dist/types/tiktok-advertisers-info";
import { InternalServicesCommandQueryFactory, getEnvVar } from "@meltwater/grimoire-publish-services";
import type { GetTiktokAdvertiserInfoResponse } from "@meltwater/engage-ads-tiktok-services/dist/types/tiktok-advertiser";
import { GetTikTokAdvertisersInfoQuery } from "@meltwater/engage-ads-tiktok-services/dist/queries/get-tiktok-advertisers-info.query";
import { TiktokBoostAdRepository } from "@meltwater/engage-ads-tiktok-services/dist/tiktok-boost-ad-repository";

const logger = LambdaLogger.getInstance();
const tracer = LambdaTracer.getInstance();

const COMMON_HEADERS = { "x-client-name": "engage-ads" };

export class TiktokAdvertiserInfoService {
  private internalServicesFactory: InternalServicesCommandQueryFactory;

  constructor() {
    this.internalServicesFactory = InternalServicesCommandQueryFactory.createInstance({
      identityConfig: {
        authToken: getEnvVar("GRAPHQL_S31_KEY"),
        baseUrl: getEnvVar("GRAPHQL_S31_URL"),
        headers: COMMON_HEADERS,
      },
      credentialConfig: {
        authToken: getEnvVar("GRAPHQL_S31_KEY"),
        baseUrl: getEnvVar("GRAPHQL_S31_URL"),
        headers: COMMON_HEADERS,
      },
      entitlementsConfig: {
        authToken: getEnvVar("GRAPHQL_S31_KEY"),
        baseUrl: getEnvVar("GRAPHQL_S31_URL"),
        headers: COMMON_HEADERS,
      },
    });
  }

  @tracer.captureMethod()
  public async getTikTokAdvertisersInfo(
    args: GetTiktokAdvertisersInfoParams,
  ): Promise<GetTiktokAdvertiserInfoResponse> {
    LambdaTracer.addCustomAttributes({
      "meltwater.company.id": args.companyId,
      "meltwater.social.credential_id": args.credentialId,
    });

    logger.info("Getting TikTok advertisers info", {
      "meltwater.company.id": args.companyId,
      "meltwater.social.credential_id": args.credentialId,
      "internal.engage.ads.advertiser_ids": args.advertiserIds,
    });

    try {
      // Fetch the TikTok access token
      const credential = await this.internalServicesFactory.createSocialCredentialQuery().execute({
        companyId: args.companyId,
        credentialId: args.credentialId,
      });

      if (!credential || !credential.token) {
        throw new Error("Failed to retrieve TikTok access token");
      }

      const accessToken = credential.token;
      logger.info("TikTok access token retrieved", {
        "meltwater.social.credential_id": args.credentialId,
      });

      const repository = new TiktokBoostAdRepository(accessToken);
      const query = new GetTikTokAdvertisersInfoQuery(repository);
      const result = await query.execute({ advertiserIds: args.advertiserIds });

      logger.info("TikTok advertisers retrieved", {
        "internal.engage.ads.response": JSON.stringify(result),
        "internal.engage.ads.count": result.data.length,
      });

      return result;
    } catch (error) {
      logger.error("Failed to get TikTok advertisers info", {
        "meltwater.company.id": args.companyId,
        "meltwater.social.credential_id": args.credentialId,
        "internal.engage.ads.error": error instanceof Error ? error.message : "Unknown error",
        "internal.engage.ads.advertiser_ids": args.advertiserIds,
      });
      throw error;
    }
  }
}

export const tiktokAdvertiserInfoService = new TiktokAdvertiserInfoService();
