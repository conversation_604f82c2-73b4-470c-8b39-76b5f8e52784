import { <PERSON><PERSON><PERSON>og<PERSON>, LambdaTracer } from "@meltwater/lambda-monitoring";
import axios from "axios";
import type { FacebookAdCampaign } from "../types";
import { GetFacebookAdCampaignsQuery } from "@meltwater/engage-ads-facebook-services";
import type {
  IFacebookAdCampaignRepository,
  GetFacebookAdCampaignsParams,
} from "@meltwater/engage-ads-facebook-services";

const logger = LambdaLogger.getInstance();
const tracer = LambdaTracer.getInstance();

export class FacebookAdCampaignsService {
  private readonly socialPublisherApiUrl: string;

  constructor() {
    this.socialPublisherApiUrl = process.env.SOCIAL_PUBLISHER_API_URL!;
  }

  private createCustomRepository(accessToken: string): IFacebookAdCampaignRepository {
    return {
      getFacebookAdCampaigns: async (params: GetFacebookAdCampaignsParams): Promise<FacebookAdCampaign[]> => {
        const query = `
          query GetFacebookAdCampaigns($filter: FacebookAdCampaignData) {
            getFacebookAdCampaigns(filter: $filter) {
              id
              name
              status
              bidStrategy
              buyingType
              budget
              budgetType
              objective
            }
          }
        `;

        const variables = {
          filter: {
            credentialId: params.credentialId,
          },
        };

        const headers = {
          Authorization: accessToken,
        };

        const response = await axios.post(
          this.socialPublisherApiUrl,
          {
            query,
            variables,
          },
          { headers },
        );

        logger.info("Response received from Social Publisher API", {
          status: response.status,
          statusText: response.statusText,
        });

        return response.data?.data?.getFacebookAdCampaigns || [];
      },
    };
  }

  @tracer.captureMethod({ subSegmentName: "FacebookAdCampaignsService:getFacebookAdCampaigns" })
  public async getFacebookAdCampaigns(
    params: { credentialId: number },
    accessToken: string,
  ): Promise<FacebookAdCampaign[]> {
    try {
      const repository = this.createCustomRepository(accessToken);

      const query = new GetFacebookAdCampaignsQuery(repository);

      const campaigns = await query.execute({
        credentialId: params.credentialId,
      });

      return campaigns;
    } catch (error) {
      logger.error("Failed to get Facebook ad campaigns", {
        error: error instanceof Error ? error.message : String(error),
        credentialId: params.credentialId,
      });

      return [];
    }
  }
}

export const facebookAdCampaignsService = new FacebookAdCampaignsService();
