import { Lamb<PERSON><PERSON>og<PERSON>, LambdaTracer } from "@meltwater/lambda-monitoring";
import axios from "axios";

import type { FacebookSavedAudience, GetFacebookSavedAudienceParams, IFacebookSavedAudienceRepository } from "../types";

class GetFacebookSavedAudienceQuery {
  constructor(private repository: IFacebookSavedAudienceRepository) {}

  async execute(params: GetFacebookSavedAudienceParams): Promise<FacebookSavedAudience[]> {
    return this.repository.getFacebookSavedAudience(params);
  }
}

const logger = LambdaLogger.getInstance();
const tracer = LambdaTracer.getInstance();

export class FacebookSavedAudienceService {
  private readonly socialPublisherApiUrl: string;

  constructor() {
    this.socialPublisherApiUrl = process.env.SOCIAL_PUBLISHER_API_URL!;
  }

  private createCustomRepository(accessToken: string): IFacebookSavedAudienceRepository {
    return {
      getFacebookSavedAudience: async (params: GetFacebookSavedAudienceParams): Promise<FacebookSavedAudience[]> => {
        const query = `
          query getFacebookSavedAudience($filter: FacebookSavedAudienceData) {
            getFacebookSavedAudience(filter: $filter) {
              id
              name
            }
          }
        `;

        const variables = {
          filter: {
            credentialId: params.credentialId,
          },
        };

        const headers = {
          Authorization: accessToken,
        };

        const response = await axios.post(
          this.socialPublisherApiUrl,
          {
            query,
            variables,
          },
          { headers },
        );

        logger.info("Response received from Social Publisher API", {
          status: response.status,
          statusText: response.statusText,
        });

        return response.data?.data?.getFacebookSavedAudience || [];
      },
    };
  }

  @tracer.captureMethod({ subSegmentName: "FacebookSavedAudienceService:getFacebookSavedAudience" })
  public async getFacebookSavedAudience(credentialId: number, accessToken: string): Promise<FacebookSavedAudience[]> {
    try {
      const repository = this.createCustomRepository(accessToken);

      logger.info("Fetching Facebook saved audiences", {
        credentialId,
      });

      const query = new GetFacebookSavedAudienceQuery(repository);
      const audiences = await query.execute({ credentialId });

      return audiences;
    } catch (error) {
      logger.error("Failed to get Facebook saved audiences", {
        error: error instanceof Error ? error.message : String(error),
        credentialId,
      });

      return [];
    }
  }
}

export const facebookSavedAudienceService = new FacebookSavedAudienceService();
