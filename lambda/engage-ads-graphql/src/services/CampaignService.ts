import { Lamb<PERSON><PERSON>og<PERSON>, LambdaTracer } from "@meltwater/lambda-monitoring";
import { HttpClient } from "@meltwater/engage-ads-commons";
import { GetCampaignsQuery, CampaignRepository } from "@meltwater/engage-ads-tiktok-services";

const logger = LambdaLogger.getInstance();
const tracer = LambdaTracer.getInstance();

const COMMON_HEADERS = { "x-client-name": "engage-ads" };

export class CampaignService {
  @tracer.captureMethod()
  public async getCampaignsByAdvertiserIds(advertiserIds: string[], accessToken: string) {
    logger.info("Received request for campaigns", { advertiserIds });

    const httpClient = new HttpClient({
      baseURL: process.env.SECTION31_STAGING_BASE_URL!,
      defaultHeaders: {
        ...COMMON_HEADERS,
      },
    });

    httpClient.setAuthToken(accessToken, undefined);

    const campaignRepository = new CampaignRepository(httpClient);
    const getCampaignsQuery = new GetCampaignsQuery(campaignRepository);

    return getCampaignsQuery.execute({ advertiserIds });
  }
}

export const campaignService = new CampaignService();
