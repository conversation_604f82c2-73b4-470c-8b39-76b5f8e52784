import { <PERSON><PERSON><PERSON><PERSON><PERSON>, LambdaTracer } from "@meltwater/lambda-monitoring";
import { BoostAds, WarpzoneModel, connectToDatabase } from "@meltwater/engage-ads-db-sdk";
import { addDays, format } from "date-fns";

import {
  GetGroupedMessageQuery,
  BoostFacebookPostCommand,
  FacebookBoostRepository,
  type IFacebookBoostRepository,
  type GetGroupedMessageParams,
  type GroupedMessageResult,
  type BoostFacebookPostParams,
  type BoostFacebookPostResult,
} from "@meltwater/engage-ads-facebook-services";

const logger = LambdaLogger.getInstance();
const tracer = LambdaTracer.getInstance();

export class FacebookBoostService {
  private readonly socialPublisherApiUrl: string;

  constructor() {
    this.socialPublisherApiUrl = process.env.SOCIAL_PUBLISHER_API_URL!;
    logger.info("FacebookBoostService initialized", { apiUrl: this.socialPublisherApiUrl });
  }

  private createCustomBoostRepository(accessToken: string): IFacebookBoostRepository {
    return new FacebookBoostRepository(accessToken);
  }

  @tracer.captureMethod({ subSegmentName: "FacebookBoostService:getGroupedMessage" })
  public async getGroupedMessage(nativeId: string, accessToken: string): Promise<GroupedMessageResult | null> {
    logger.info("Service: Executing GetGroupedMessageQuery", { nativeId });
    try {
      const repository = this.createCustomBoostRepository(accessToken);
      const query = new GetGroupedMessageQuery(repository);
      const params: GetGroupedMessageParams = { nativeId };
      // Execute the query; await the result or any thrown error
      const result = await query.execute(params);
      logger.info("Service: GetGroupedMessageQuery executed", { nativeId, hasResult: !!result });
      return result;
    } catch (error) {
      logger.error("Service: Error executing GetGroupedMessageQuery", {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        nativeId,
      });
      throw error;
    }
  }

  @tracer.captureMethod({ subSegmentName: "FacebookBoostService:boostFacebookPost" })
  public async boostFacebookPost(
    params: BoostFacebookPostParams,
    accessToken: string,
  ): Promise<BoostFacebookPostResult> {
    logger.info("Service: Executing BoostFacebookPostCommand", {
      messageId: params.messageId,
      adAccountCredentialId: params.adAccountCredentialId,
    });
    try {
      const repository = this.createCustomBoostRepository(accessToken);
      const command = new BoostFacebookPostCommand(repository);
      // Execute the command; await the result or any thrown error
      const result = await command.execute(params);
      logger.info("Service: BoostFacebookPostCommand executed", {
        messageId: params.messageId,
        boostId: result.boostId,
        hasErrors: result.boostingErrors && result.boostingErrors.length > 0,
      });
      return result;
    } catch (error) {
      logger.error("Service: Error executing BoostFacebookPostCommand", {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        messageId: params.messageId,
        adAccountCredentialId: params.adAccountCredentialId,
      });
      const errorMessage = `Service Error: Failed to execute boost command. ${error instanceof Error ? error.message : String(error)}`;
      return { boostingErrors: [errorMessage] };
    }
  }

  @tracer.captureMethod({ subSegmentName: "FacebookBoostService:boostFacebookPostWithDatabase" })
  public async boostFacebookPostWithDatabase(
    params: BoostFacebookPostParams & {
      nativeId: string;
      advertiserId: string;
    },
    accessToken: string,
  ): Promise<BoostFacebookPostResult> {
    logger.info("Service: Executing complete Facebook boost with database operations", {
      messageId: params.messageId,
      adAccountCredentialId: params.adAccountCredentialId,
      nativeId: params.nativeId,
    });

    try {
      // First, execute the boost command
      const boostResult = await this.boostFacebookPost(params, accessToken);

      // If boost was successful and we have a boostId, save to database
      if (boostResult.boostId && (!boostResult.boostingErrors || boostResult.boostingErrors.length === 0)) {
        try {
          await connectToDatabase();

          // Extract postId from nativeId (remove the "id:facebook.com:" prefix)
          const postIdFromNativeId = params.nativeId.replace(/^id:facebook\.com:/, "");

          // Get companyId from Warpzone
          const warpzoneDoc = await WarpzoneModel.findOne({ "document.externalId": params.nativeId }).exec();
          const companyIdFromWarpzone = warpzoneDoc?.companyId;

          if (companyIdFromWarpzone) {
            logger.info("Found companyId in Warpzone document", {
              nativeId: params.nativeId,
              companyId: companyIdFromWarpzone,
            });

            const adData = {
              ad_id: boostResult.boostId,
              post_id: postIdFromNativeId,
              audience_set_id: params.audienceId,
              channel: "facebook" as const,
              advertiser_id: params.advertiserId,
              campaign_id: params.campaignId ?? "",
              ad_name: `Facebook Boost ${boostResult.boostId}`,
              adgroup_id: "",
              video_id: "",
              schedule_end_time: params.duration
                ? format(addDays(new Date(), params.duration), "yyyy-MM-dd HH:mm:ss")
                : undefined,
            };

            await BoostAds.boostFacebookAds(adData, companyIdFromWarpzone);
            logger.info("Successfully saved Facebook boost details to database", {
              boostId: boostResult.boostId,
              companyId: companyIdFromWarpzone,
            });
          } else {
            logger.error("Could not find companyId in Warpzone document for the given nativeId", {
              nativeId: params.nativeId,
            });
            // Don't fail the entire operation, just log the error
          }
        } catch (dbError) {
          logger.error("Failed to save Facebook boost to database", {
            error: dbError instanceof Error ? dbError.message : String(dbError),
            boostId: boostResult.boostId,
            nativeId: params.nativeId,
          });
          // Don't fail the entire operation, just log the error
        }
      }

      return boostResult;
    } catch (error) {
      logger.error("Service: Error in complete Facebook boost process", {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        messageId: params.messageId,
        nativeId: params.nativeId,
      });
      const errorMessage = `Service Error: Failed to execute complete boost process. ${error instanceof Error ? error.message : String(error)}`;
      return { boostingErrors: [errorMessage] };
    }
  }
}

export const facebookBoostService = new FacebookBoostService();
