import { handler } from "../index";
import type { APIGatewayProxyEventV2, Context, APIGatewayProxyStructuredResultV2 } from "aws-lambda";
import {
  getTikTokInsights,
  getFacebookInsights,
  getTikTokAdAccounts,
  createTiktokSparkAd,
  createLinkedInTextAd,
  createLinkedInImageAd,
  createLinkedInVideoAd,
  createLinkedInDocumentAd,
  getTikTokRegions,
  getTiktokAdvertisersInfo,
  getCampaigns,
  getFacebookAccountsPages,
  getFacebookAdCampaigns,
  getFacebookSavedAudience,
  getFacebookAdAccountMinBudgets,
  boostFacebookPost,
  getLinkedInAdAccounts,
  getLinkedInAssociatedPages,
  getLinkedInInsights,
  getLinkedInAdCampaigns,
} from "@meltwater/engage-ads-graphql-sdk";
import { LambdaLogger } from "@meltwater/lambda-monitoring";

const logger = LambdaLogger.getInstance();

const TEST_ACCESS_TOKEN = process.env.TEST_ACCESS_TOKEN || "";

type SDKReturnType =
  | ReturnType<typeof getTikTokInsights>
  | ReturnType<typeof getFacebookInsights>
  | ReturnType<typeof getTikTokAdAccounts>
  | ReturnType<typeof createTiktokSparkAd>
  | ReturnType<typeof getTikTokRegions>
  | ReturnType<typeof getTiktokAdvertisersInfo>
  | ReturnType<typeof getCampaigns>
  | ReturnType<typeof getFacebookAccountsPages>
  | ReturnType<typeof getFacebookAdCampaigns>
  | ReturnType<typeof getFacebookSavedAudience>
  | ReturnType<typeof getFacebookAdAccountMinBudgets>
  | ReturnType<typeof boostFacebookPost>
  | ReturnType<typeof getLinkedInAdAccounts>
  | ReturnType<typeof getLinkedInAssociatedPages>
  | ReturnType<typeof getLinkedInInsights>
  | ReturnType<typeof getLinkedInAdCampaigns>
  | ReturnType<typeof createLinkedInTextAd>
  | ReturnType<typeof createLinkedInImageAd>
  | ReturnType<typeof createLinkedInVideoAd>
  | ReturnType<typeof createLinkedInDocumentAd>;

function createEvent(body: SDKReturnType): APIGatewayProxyEventV2 {
  return {
    version: "2.0",
    routeKey: "$default",
    rawPath: "/graphql",
    rawQueryString: "",
    headers: {
      "content-type": "application/json",
      authorization: TEST_ACCESS_TOKEN,
    },
    requestContext: {
      accountId: "************",
      apiId: "api-id",
      domainName: "id.execute-api.us-east-1.amazonaws.com",
      domainPrefix: "id",
      http: {
        method: "POST",
        path: "/graphql",
        protocol: "HTTP/1.1",
        sourceIp: "127.0.0.1",
        userAgent: "Lambda Tester",
      },
      requestId: "test-" + Date.now(),
      routeKey: "$default",
      stage: "$default",
      time: new Date().toISOString(),
      timeEpoch: Date.now(),
    },
    body: JSON.stringify(body),
    isBase64Encoded: false,
  };
}

async function runOperation(operationType: string): Promise<APIGatewayProxyStructuredResultV2> {
  logger.info("Debug: Starting runOperation function");

  let sdkResult;
  switch (operationType) {
    case "getTikTokInsights":
      sdkResult = getTikTokInsights(
        {
          filter: {
            startDate: "2024-12-30",
            endDate: "2025-01-29",
            size: 25,
            order: "desc",
            aggregationType: "sum",
            startFrom: 0,
            channels: ["tiktok"],
            sortBy: "likes",
            sortByMetric: true,
            profileIds: ["_000N1Bl-GeTecu3vr9peQMegQi2ZB4nI1Bp"],
          },
        },
        {
          postId: true,
          documentId: true,
          channel: true,
          createTime: true,
          likes: true,
          caption: true,
          isBoosted: true,
          scheduleEndTime: true,
          metrics: {
            videoViews: true,
            engagementRate: true,
          },
        },
      );
      break;
    case "getTikTokInsightsSortByVideoViews":
      sdkResult = getTikTokInsights(
        {
          filter: {
            startDate: "2024-12-30",
            endDate: "2025-01-29",
            size: 10,
            order: "asc",
            aggregationType: "sum",
            startFrom: 170,
            channels: ["tiktok"],
            sortBy: "video_views",
            sortByMetric: true,
          },
        },
        {
          postId: true,
          documentId: true,
          channel: true,
          createTime: true,
          likes: true,
          caption: true,
          isBoosted: true,
          scheduleEndTime: true,
          profileId: true,
          media: {
            type: true,
            url: true,
          },
          metrics: {
            videoViews: true,
            engagementRate: true,
          },
        },
      );
      break;

    case "getFacebookInsights":
      sdkResult = getFacebookInsights(
        {
          filter: {
            startDate: "2025-03-25",
            endDate: "2025-03-28",
            size: 100,
            order: "desc",
            aggregationType: "sum",
            startFrom: 0,
            channels: ["facebook"],
            sortBy: "created_time",
          },
        },
        {
          postId: true,
          externalId: true,
          channel: true,
          createTime: true,
          likes: true,
          caption: true,
          isBoosted: true,
          scheduleEndTime: true,
          metrics: {
            engagements: true,
            engagementRate: true,
            shares: true,
          },
        },
      );
      break;

    case "getFacebookInsightsSortedByLikes":
      sdkResult = getFacebookInsights(
        {
          filter: {
            startDate: "2025-03-18",
            endDate: "2025-03-25",
            size: 5,
            order: "desc",
            aggregationType: "sum",
            startFrom: 0,
            channels: ["facebook"],
            sortBy: "likes",
            sortByMetric: true,
          },
        },
        {
          postId: true,
          externalId: true,
          channel: true,
          createTime: true,
          likes: true,
          caption: true,
          isBoosted: true,
          scheduleEndTime: true,
          metrics: {
            engagements: true,
            engagementRate: true,
            shares: true,
          },
        },
      );
      break;

    case "getTikTokAdAccounts":
      sdkResult = getTikTokAdAccounts(
        {
          input: {
            ApplicationCompanyId: "5d1cc992767990d40422e42e",
            ChannelId: 11,
            ActiveInd: 1,
          },
        },
        {
          credentialId: true,
          targetPageName: true,
          targetPageLogoUrl: true,
          socialAccountId: true,
          associatedProfiles: {
            credentialId: true,
            targetPageName: true,
            socialAccountId: true,
            targetPageLogoUrl: true,
            identityId: true,
            identityType: true,
            identityAuthorizedBcId: true,
          },
        },
      );
      break;
    case "createTiktokSparkAdEngagement":
      sdkResult = createTiktokSparkAd(
        {
          params: {
            companyId: "5d1cc992767990d40422e42e",
            credentialId: 3496,
            campaign: {
              objectiveType: "ENGAGEMENT",
              campaignName: "test-reach-objective-type-1007",
              budget: 50,
              advertiserId: "7374065959209844752",
              budgetMode: "BUDGET_MODE_TOTAL",
            },
            adGroup: {
              advertiserId: "7374065959209844752",
              adgroupName: "adgroup-reach-test",
              scheduleStartTime: "2028-01-01 00:00:00",
              scheduleType: "SCHEDULE_START_END",
              billingEvent: "OCPM", // only option
              pacing: "PACING_MODE_SMOOTH",
              budget: 20,
              budgetMode: "BUDGET_MODE_TOTAL",
              locationIds: ["6252001"],
              placements: ["PLACEMENT_TIKTOK"],
              scheduleEndTime: "2028-01-02 00:00:00",
              optimizationGoal: "FOLLOWERS", // or PAGE_VISIT
              bidPrice: 1,
              conversionBidPrice: 0.1,
            },
            ad: {
              advertiserId: "7374065959209844752",
              creatives: [
                {
                  adName: "reach-test-ad",
                  identityType: "TT_USER",
                  identityId: "89115c71-b6f4-520d-a502-647773379f80",
                  adFormat: "SINGLE_VIDEO",
                  videoId: "7367369947888127278",
                },
              ],
            },
          },
        },
        {
          campaignId: true,
          metadata: {
            error: {
              message: true,
            },
          },
        },
      );
      break;

    case "createTiktokSparkAdTraffic":
      sdkResult = createTiktokSparkAd(
        {
          params: {
            companyId: "5d1cc992767990d40422e42e",
            credentialId: 3496,
            campaign: {
              objectiveType: "TRAFFIC",
              campaignName: "test-reach-objective-type-traffic37",
              budget: 50,
              advertiserId: "7374065959209844752",
              budgetMode: "BUDGET_MODE_TOTAL",
            },
            adGroup: {
              advertiserId: "7374065959209844752",
              adgroupName: "adgroup-reach-test",
              scheduleStartTime: "2028-01-01 00:00:00",
              scheduleType: "SCHEDULE_START_END",
              billingEvent: "CPC",
              pacing: "PACING_MODE_SMOOTH",
              budget: 20,
              budgetMode: "BUDGET_MODE_TOTAL",
              locationIds: ["6252001"],
              placements: ["PLACEMENT_TIKTOK"],
              scheduleEndTime: "2028-01-02 00:00:00",
              optimizationGoal: "CLICK", // or PAGE_VISIT
              bidPrice: 1,
              conversionBidPrice: 0.1,
              promotionType: "WEBSITE",
            },
            ad: {
              advertiserId: "7374065959209844752",
              creatives: [
                {
                  adName: "reach-test-ad",
                  identityType: "TT_USER",
                  identityId: "89115c71-b6f4-520d-a502-647773379f80",
                  adFormat: "SINGLE_VIDEO",
                  videoId: "7367369947888127278",
                  callToAction: "VIEW_NOW",
                  landingPageUrl: "https://google.com",
                },
              ],
            },
          },
        },
        {
          campaignId: true,
          metadata: {
            error: {
              message: true,
            },
          },
        },
      );
      break;
    case "createTiktokSparkAdReach":
      sdkResult = createTiktokSparkAd(
        {
          params: {
            companyId: "5d1cc992767990d40422e42e",
            credentialId: 3496,
            campaign: {
              objectiveType: "REACH",
              campaignName: "test-reach-objective-type-101",
              budget: 50,
              advertiserId: "7374065959209844752",
              budgetMode: "BUDGET_MODE_TOTAL",
            },
            adGroup: {
              advertiserId: "7374065959209844752",
              adgroupName: "adgroup-reach-test",
              frequency: 1,
              frequencySchedule: 1,
              scheduleStartTime: "2028-01-01 00:00:00",
              scheduleType: "SCHEDULE_START_END",
              billingEvent: "CPM",
              pacing: "PACING_MODE_SMOOTH",
              budget: 20,
              budgetMode: "BUDGET_MODE_TOTAL",
              locationIds: ["6252001"],
              placements: ["PLACEMENT_TIKTOK"],
              scheduleEndTime: "2028-01-02 00:00:00",
              optimizationGoal: "REACH",
              bidPrice: 1,
            },
            ad: {
              advertiserId: "7374065959209844752",
              creatives: [
                {
                  adName: "reach-test-ad",
                  identityType: "TT_USER",
                  identityId: "89115c71-b6f4-520d-a502-647773379f80",
                  adFormat: "SINGLE_VIDEO",
                  videoId: "7367369947888127278",
                },
              ],
            },
          },
        },
        {
          campaignId: true,
          metadata: {
            error: {
              name: true,
              message: true,
            },
          },
        },
      );
      break;
    case "createTiktokSparkAdVideoViews":
      sdkResult = createTiktokSparkAd(
        {
          params: {
            companyId: "5d1cc992767990d40422e42e",
            credentialId: 3496,
            campaign: {
              objectiveType: "VIDEO_VIEWS",
              campaignName: "test-155-videoid-80",
              budget: 50,
              advertiserId: "7374065959209844752",
              budgetMode: "BUDGET_MODE_TOTAL",
            },
            adGroup: {
              advertiserId: "7374065959209844752",
              adgroupName: "adgroup-test-100",
              scheduleStartTime: "2028-01-01 00:00:00",
              scheduleType: "SCHEDULE_START_END",
              billingEvent: "CPV",
              pacing: "PACING_MODE_SMOOTH",
              budget: 20,
              budgetMode: "BUDGET_MODE_TOTAL",
              locationIds: ["6252001"],
              placements: ["PLACEMENT_TIKTOK"],
              scheduleEndTime: "2028-01-02 00:00:00",
              optimizationGoal: "ENGAGED_VIEW",
              bidPrice: 1,
            },
            ad: {
              advertiserId: "7374065959209844752",
              creatives: [
                {
                  adName: "test-ad-100",
                  identityType: "TT_USER",
                  adFormat: "SINGLE_VIDEO",
                  videoId: "7367369947888127278",
                  identityId: "89115c71-b6f4-520d-a502-647773379f80",
                },
              ],
            },
          },
        },
        {
          campaignId: true,
          metadata: {
            error: {
              message: true,
            },
          },
        },
      );
      break;
    case "createTiktokSparkAdWithExistingCampaign":
      sdkResult = createTiktokSparkAd(
        {
          params: {
            companyId: "5d1cc992767990d40422e42e",
            credentialId: 3496,
            campaign: {
              campaignId: "1815719868349490",
              advertiserId: "7374065959209844752",
              campaignName: "placeholder",
              budget: 0,
              budgetMode: "BUDGET_MODE_TOTAL",
              objectiveType: "ENGAGEMENT",
            },
            adGroup: {
              advertiserId: "7374065959209844752",
              adgroupName: "existing-campaign-ad-group-3",
              scheduleStartTime: "2028-01-01 00:00:00",
              scheduleType: "SCHEDULE_START_END",
              billingEvent: "CPV",
              pacing: "PACING_MODE_SMOOTH",
              budget: 20,
              budgetMode: "BUDGET_MODE_TOTAL",
              locationIds: ["6252001"],
              placements: ["PLACEMENT_TIKTOK"],
              scheduleEndTime: "2028-01-02 00:00:00",
              optimizationGoal: "ENGAGED_VIEW",
              bidPrice: 1,
            },
            ad: {
              advertiserId: "7374065959209844752",
              creatives: [
                {
                  adName: "existing-campaign-test-ad",
                  identityType: "TT_USER",
                  identityId: "89115c71-b6f4-520d-a502-647773379f80",
                  adFormat: "SINGLE_VIDEO",
                  videoId: "7367369947888127278",
                },
              ],
            },
          },
        },
        {
          campaignId: true,
          metadata: {
            error: {
              message: true,
            },
          },
        },
      );
      break;

    case "createTiktokSparkAdVideoViewsBCAUTH":
      sdkResult = createTiktokSparkAd(
        {
          params: {
            companyId: "5d1cc992767990d40422e42e",
            credentialId: 3496,
            campaign: {
              objectiveType: "VIDEO_VIEWS",
              campaignName: "test-155-videoid-88",
              budget: 50,
              // advertiserId: "7374065959209844752",
              advertiserId: "7434239868938125328",
              budgetMode: "BUDGET_MODE_TOTAL",
            },
            adGroup: {
              // advertiserId: "7374065959209844752",
              advertiserId: "7434239868938125328",
              adgroupName: "adgroup-test-100",
              scheduleStartTime: "2028-01-01 00:00:00",
              scheduleType: "SCHEDULE_START_END",
              billingEvent: "CPV",
              pacing: "PACING_MODE_SMOOTH",
              budget: 20,
              budgetMode: "BUDGET_MODE_TOTAL",
              locationIds: ["6252001"],
              placements: ["PLACEMENT_TIKTOK"],
              scheduleEndTime: "2028-01-02 00:00:00",
              optimizationGoal: "ENGAGED_VIEW",
              bidPrice: 1,
            },
            ad: {
              // advertiserId: "7374065959209844752", //section_31_mw advertiser
              advertiserId: "7434239868938125328", // Engage Advertise Test 1
              creatives: [
                {
                  adName: "test-ad-100",
                  identityType: "BC_AUTH_TT",
                  adFormat: "SINGLE_VIDEO",
                  // videoId: "7367369947888127278",
                  videoId: "7425971744839666962", // ankit's video post
                  // https://www.tiktok.com/@ankitsrivastava/video/7425971744839666962
                  // identityId: "267716d3-ce9b-4adb-8531-f20b0aaa3265" //identity section_31_mw
                  identityId: "f03f5c4c-2a5e-5a4e-8fdd-1e925270f32d",
                  identityAuthorizedBcId: "7374060200619196432", // Section Business Center
                },
              ],
            },
          },
        },
        {
          campaignId: true,
          metadata: {
            error: {
              message: true,
            },
          },
        },
      );
      break;

    case "getTikTokRegions":
      sdkResult = getTikTokRegions(
        {
          input: {
            companyId: "5d1cc992767990d40422e42e",
            credentialId: 3496,
            advertiserId: "7374065959209844752",
            placements: ["PLACEMENT_TIKTOK"],
            objectiveType: "VIDEO_VIEWS",
            level: "TO_COUNTRY",
          },
        },
        {
          locationId: true,
          name: true,
        },
      );
      break;

    case "getTiktokAdvertisersInfo":
      sdkResult = getTiktokAdvertisersInfo(
        {
          input: {
            companyId: "5d1cc992767990d40422e42e",
            credentialId: 3496,
            advertiserIds: ["7374065959209844752"],
          },
        },
        {
          advertiserId: true,
          currency: true,
          timezone: true,
        },
      );
      break;
    case "getCampaigns":
      sdkResult = getCampaigns(
        { input: { advertiserIds: ["7447924231821901841"] } },
        { campaignId: true, campaignName: true },
      );
      break;
    case "getFacebookAccountsPages":
      sdkResult = getFacebookAccountsPages(
        {
          input: {
            applicationCompanyId: "5b8569cec6ac9ab416e2b3cd",
            userId: "664237f34460d20008a56647",
            activeInd: 1,
            statusInd: ["VALID", "INVALID", "EXPIRED"],
            channels: ["FACEBOOK", "FACEBOOKADS"],
          },
        },
        {
          channelName: true,
          targetPageName: true,
          socialAccountId: true,
          credentialId: true,
          targetPageLogoUrl: true,
        },
      );
      break;
    case "getFacebookAdCampaigns":
      sdkResult = getFacebookAdCampaigns(
        {
          filter: {
            credentialId: 371,
          },
        },
        {
          id: true,
          name: true,
          status: true,
          bidStrategy: true,
          buyingType: true,
          budget: true,
          budgetType: true,
          objective: true,
        },
      );
      break;

    case "getFacebookSavedAudience":
      sdkResult = getFacebookSavedAudience(
        {
          filter: {
            credentialId: 371,
          },
        },
        {
          id: true,
          name: true,
        },
      );
      break;

    case "getFacebookAdAccountMinBudgets":
      sdkResult = getFacebookAdAccountMinBudgets(
        {
          credentialId: 371,
        },
        {
          currency: true,
          impressionsBudget: true,
          videoBudget: true,
          highFreqBudget: true,
          lowFreqBudget: true,
        },
      );
      break;

    case "boostFacebookPost":
      sdkResult = boostFacebookPost(
        {
          input: {
            nativeId: "id:facebook.com:108756328103095_988406150151559",

            profileCredentialId: 3649,
            adAccountCredentialId: 289,

            campaignId: "120218901652440337",
            duration: 1,
            budget: 8,
            audienceId: "120218857309500337",
            advertiserId: "act_623355839067987",
          },
        },
        {
          boostId: true,
          boostingErrors: true,
        },
      );
      break;

    case "getLinkedInAdAccounts":
      sdkResult = getLinkedInAdAccounts(
        {
          input: {
            applicationCompanyId: "5b8569cec6ac9ab416e2b3cd",
            userId: "664237f34460d20008a56647",
            activeInd: 1,
          },
        },
        {
          channelName: true,
          targetPageName: true,
          socialAccountId: true,
          credentialId: true,
          targetPageLogoUrl: true,
          tokenId: true,
          tokenDetails: {
            token: true,
          },
        },
      );
      break;

    case "getLinkedInAssociatedPages":
      sdkResult = getLinkedInAssociatedPages(
        {
          query: {
            companyId: "5b8569cec6ac9ab416e2b3cd",
            credentialId: 3725,
          },
        },
        {
          channelName: true,
          targetPageName: true,
          socialAccountId: true,
          credentialId: true,
          targetPageLogoUrl: true,
        },
      );
      break;

    case "getEngageAdsChannelInsightsLinkedIn":
      sdkResult = getLinkedInInsights(
        {
          filter: {
            startDate: "2024-01-10",
            endDate: "2024-01-20",
            size: 100,
            order: "desc",
            aggregationType: "sum",
            startFrom: 0,
            channels: ["linkedin"],
            sortBy: "likes_count",
            sortByMetric: false,
            profileIds: ["urn:li:organization:********"],
          },
        },
        {
          postId: true,
          documentId: true,
          profileId: true,
          externalId: true,
          channel: true,
          createTime: true,
          likes: true,
          caption: true,
          isBoosted: true,
          scheduleEndTime: true,
          metrics: {
            engagements: true,
            engagementRate: true,
            shares: true,
            videoViews: true,
          },
        },
      );
      break;

    case "getLinkedInAdCampaigns":
      sdkResult = getLinkedInAdCampaigns(
        {
          input: {
            companyId: "5b8569cec6ac9ab416e2b3cd",
            credentialId: 3725,
            adAccountId: "*********",
            status: "ACTIVE",
            type: "TEXT_AD",
          },
        },
        {
          id: true,
          name: true,
          status: true,
          type: true,
        },
      );
      break;

    case "createLinkedInTextAd":
      sdkResult = createLinkedInTextAd(
        {
          params: {
            companyId: "5b8569cec6ac9ab416e2b3cd",
            postId: "urn:li:share:7151981244128403456",
            advertiserId: "*********",
            credentialId: 3725,
            campaign: {
              campaignId: "*********",
              adAccountId: "*********",
            },
            creative: {
              adName: "lambda-test-text-ad",
              destinationUrl: "https://meltwater.com",
              description: "This is a test ad 2",
              callToAction: "LEARN_MORE",
            },
          },
        },
        {
          campaignId: true,
          creativeId: true,
          metadata: {
            error: {
              message: true,
            },
          },
        },
      );
      break;

    case "createLinkedInImageAd":
      sdkResult = createLinkedInImageAd(
        {
          params: {
            companyId: "5b8569cec6ac9ab416e2b3cd",
            postId: "urn:li:share:7329342724669636609",
            advertiserId: "*********",
            credentialId: 100,
            campaign: {
              campaignId: "*********",
              adAccountId: "*********",
            },
            creative: {
              adName: "Test Image Ad",
            },
          },
        },
        {
          campaignId: true,
          creativeId: true,
          metadata: {
            error: {
              message: true,
            },
          },
        },
      );
      break;

    case "createLinkedInVideoAd":
      sdkResult = createLinkedInVideoAd(
        {
          params: {
            companyId: "5b8569cec6ac9ab416e2b3cd",
            postId: "urn:li:ugcPost:7329238755469312001",
            credentialId: 100,
            advertiserId: "*********",
            campaign: { campaignId: "*********", adAccountId: "*********" },
            creative: { adName: "a doc post1" },
          },
        },
        {
          campaignId: true,
          creativeId: true,
          metadata: {
            error: {
              message: true,
            },
          },
        },
      );
      break;

    case "createLinkedInDocumentAd":
      sdkResult = createLinkedInDocumentAd(
        {
          params: {
            companyId: "5b8569cec6ac9ab416e2b3cd",
            postId: "urn:li:ugcPost:7331253713119887360",
            advertiserId: "*********",
            credentialId: 100,
            campaign: {
              campaignId: "*********",
              adAccountId: "*********",
            },
            creative: {
              adName: "lambda-test-document-ad",
            },
          },
        },
        {
          campaignId: true,
          creativeId: true,
          metadata: {
            error: {
              message: true,
            },
          },
        },
      );
      break;

    default:
      throw new Error(`Unsupported operation type: ${operationType}`);
  }

  const event = createEvent(sdkResult);

  const context: Context = {
    callbackWaitsForEmptyEventLoop: true,
    functionName: "test-function",
    functionVersion: "1",
    invokedFunctionArn: "arn:aws:lambda:us-east-1:************:function:test-function",
    memoryLimitInMB: "128",
    awsRequestId: "test-request-" + Date.now(),
    logGroupName: "/aws/lambda/test-function",
    logStreamName: "2024/06/26/[$LATEST]abcdefghijklmnopqrst",
    identity: undefined,
    clientContext: undefined,
    getRemainingTimeInMillis: () => 3000,
    done: () => {},
    fail: () => {},
    succeed: () => {},
  };

  return new Promise<APIGatewayProxyStructuredResultV2>((resolve, reject) => {
    logger.info("Debug: About to call handler");
    const handlerResult = handler(event, context);

    if (handlerResult instanceof Promise) {
      logger.info("Debug: Handler returned a Promise");
      handlerResult
        .then((result) => {
          if (result) {
            resolve(result);
          } else {
            logger.info("Debug: Handler returned void, assuming callback was used");
          }
        })
        .catch(reject);
    }
  });
}

const operationType = process.argv[2];

if (!operationType) {
  console.error("Please provide operation type as an argument.");
  process.exit(1);
}

logger.info("=== Lambda Execution Logs ===");

runOperation(operationType)
  .then((result) => {
    logger.info("\n=== Lambda Response ===");
    logger.info("Debug: Lambda Response");
    logger.info(JSON.stringify(result, null, 2));
    process.exit(0);
  })
  .catch((error) => {
    console.error("Error running query:", error);
    console.log("Test failed, exiting in 100ms...");
    process.exit(1);
  });
