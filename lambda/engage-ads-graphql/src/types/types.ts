export interface ImpressionSource {
  percentage: number;
  impression_source: string;
}

export interface TikTokInsightsResponse {
  video_id: string;
  create_time: number;
  company_id: string;
  profile_id: string;
  likes: number;
  average_watch_time: number;
  video_length: number;
  engagements: number;
  engagement_rate: number;
  reach: number;
  thumbnail_url: string;
  caption: string;
  comments: number;
  video_views: number;
  full_video_watch_rate: number;
  total_time_watched: number;
  shares: number;
  impression_sources?: ImpressionSource[];
  is_boosted?: boolean;
}

export interface EngageAdsChannelInsightsArgs {
  filter: {
    startDate: string;
    endDate: string;
    size: number;
    order: string;
    aggregationType: string;
    startFrom?: number;
    channels: string[];
    match?: {
      captionKeyword?: string;
    };
    sortBy?: string;
    sortByMetric?: boolean;
    profileIds?: string[];
  };
}

export interface Insight {
  postId: string;
  documentId?: string;
  externalId?: string;
  channel: string;
  companyId: string;
  profileId: string;
  permalink?: string;
  createTime: string;
  media: Array<{
    type: string;
    url: string;
  }>;
  caption: string;
  likes: number;
  comments: number;
  metrics: {
    averageWatchTime: number;
    videoLength: number;
    engagements: number;
    engagementRate: number;
    reach: number;
    videoViews: number;
    fullVideoWatchRate: number;
    totalTimeWatched: number;
    shares: number;
    impressionSources?: Array<{
      percentage: number;
      impressionSource: string;
    }>;
  };
  isBoosted?: boolean;
  scheduleEndTime?: string;
  mimeType?: string;
  postType?: string;
  downloadUrl?: string;
  mediaUrn?: string;
}

export enum Channel {
  TikTok = "tiktok",
  Facebook = "facebook",
  LinkedIn = "linkedin",
}

export interface TikTokAdAccount {
  credentialId: number;
  targetPageName: string;
  targetPageLogoUrl: string | null;
  associatedProfiles: TikTokProfile[];
}

export interface TikTokProfile {
  credentialId: number;
  targetPageName: string;
  socialAccountId: string;
  targetPageLogoUrl: string | null;
}

export interface TikTokAdAccountQueryInput {
  ApplicationCompanyId: string;
  ChannelId: number;
  ActiveInd: number;
}

export interface CampaignQueryInput {
  advertiserIds: string[];
}

export interface FacebookAdCampaignData {
  credentialId?: number;
}

export interface FacebookAdCampaign {
  id: string;
  name: string;
  status: string;
  bidStrategy: string;
  buyingType: string;
  budget: number;
  budgetType: string;
  objective: string;
}

export interface FacebookBoostInput {
  nativeId: string;
  adAccountCredentialId: number;
  profileCredentialId: number;
  campaignId: string;
  duration: number;
  budget: number;
  audienceId: string;
  advertiserId: string;
}

export interface FacebookBoostResponse {
  boostId?: string;
  boostingErrors?: string[];
}

interface GroupedMessageProfile {
  legacyMessageId?: string;
  credentialId?: number;
  nativeId?: string;
}

interface GroupedMessageChannel {
  channel?: string;
  profiles?: GroupedMessageProfile[];
}

export interface GroupedMessageData {
  groupedMessage?: {
    channels?: GroupedMessageChannel[];
  };
}

export interface SPAPIBoostData {
  messageId: string;
  campaignId: string;
  duration: number;
  budget: number;
  audienceId: string;
  credentialId: number;
}

export interface SPAPIBoostResponse {
  boostFacebookPost?: {
    boostId?: string;
    boostingErrors?: string[];
  };
}

export interface SPAPIGroupedMessageResponse {
  data?: GroupedMessageData;
  errors?: { message: string }[];
}

// --------------------------------------------
// LinkedIn campaigns
// --------------------------------------------

export interface LinkedInCampaignQueryInput {
  companyId: string;
  credentialId: number;
  adAccountId: string;
  status?: string; // e.g., "ACTIVE"
  type?: string; // e.g., "SPONSORED_UPDATES"
}

export interface LinkedInCampaign {
  id: number;
  name: string;
  status: string;
  type?: string;
  dailyBudget?: {
    amount: string;
    currencyCode: string;
  };
  unitCost?: {
    amount: string;
    currencyCode: string;
  };
}
