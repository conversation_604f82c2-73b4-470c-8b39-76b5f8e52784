export const typeDefs = `#graphql
type ImpressionSource {
  percentage: Float!
  impressionSource: String!
}

type Metrics {
  averageWatchTime: Float
  videoLength: Float
  engagements: Float
  engagementRate: Float
  reach: Int
  videoViews: Int
  fullVideoWatchRate: Float
  totalTimeWatched: Float
  shares: Int
  impressionSources: [ImpressionSource]
  # Add other channel-specific properties here
}

type Media {
  type: String!
  url: String!
}

type EngageAdsMessage {
  postId: String!
  externalId: String
  documentId: String
  channel: String!
  companyId: String!
  profileId: String!
  createTime: String!
  permalink: String
  media: [Media!]!
  caption: String
  likes: Int
  comments: Int
  isBoosted: Boolean!
  scheduleEndTime: String
  mimeType: String
  postType: String
  downloadUrl: String
  mediaUrn: String
  metrics: Metrics
}

input EngageAdsChannelInsightsFilter {
  startDate: String!
  endDate: String!
  size: Int!
  order: String!
  aggregationType: String!
  startFrom: Int
  channels: [String!]!
  match: EngageAdsChannelInsightsMatchFilter
  sortBy: String
  sortByMetric: Boolean
  profileIds: [String!]
}

input EngageAdsChannelInsightsMatchFilter {
  captionKeyword: String
}

type EngageAdsChannelInsightsResponse {
  insights: [EngageAdsMessage!]!
  totalCount: Int!
}

input CampaignQueryInput {
  advertiserIds: [String!]!
}

type Campaign {
  campaignId: String!
  campaignName: String!
}

input FacebookCredentialsFilter {
  applicationCompanyId: String!
  userId: String!
  activeInd: Int!
  statusInd: [String!]!
  channels: [String!]!
}

type FacebookCredentialRecord {
  channelName: String!
  targetPageName: String!
  socialAccountId: String!
  credentialId: Int
  targetPageLogoUrl: String
}

type FacebookAdCampaign {
  id: String!
  name: String!
  status: String!
  bidStrategy: String
  buyingType: String!
  budget: Float!
  budgetType: String!
  objective: String!
}

input FacebookAdCampaignData {
  credentialId: Int
}

input FacebookSavedAudienceData {
  credentialId: Int
}

type FacebookSavedAudience {
  id: String!
  name: String!
}

type FacebookAdAccountMinBudgets {
  currency: String!
  impressionsBudget: Float!
  videoBudget: Float!
  highFreqBudget: Float!
  lowFreqBudget: Float!
}

type Query {
  engageAdsChannelInsights(filter: EngageAdsChannelInsightsFilter!): EngageAdsChannelInsightsResponse!
  tikTokAdAccounts(input: TikTokAdAccountQueryInput!): [TikTokAdAccount]
  tikTokRegions(input: TikTokRegionQueryInput!): TiktokRegion!
  tikTokAdvertisersInfo(input: TikTokAdvertisersInfoQueryInput!): TiktokAdvertiserInfo!
  queryCampaigns(input: CampaignQueryInput!): [Campaign!]!
  getFacebookAccountsPages(input: FacebookCredentialsFilter!): [FacebookCredentialRecord!]!
  getFacebookAdCampaigns(filter: FacebookAdCampaignData): [FacebookAdCampaign!]!
  getFacebookSavedAudience(filter: FacebookSavedAudienceData): [FacebookSavedAudience!]!
  getFacebookAdAccountMinBudgets(credentialId: Int): FacebookAdAccountMinBudgets!
  getLinkedInAdAccounts(input: LinkedInCredentialsFilter!): [LinkedInCredentialRecord!]!
  getLinkedInAssociatedPages(query: LinkedInAssociatedPagesQuery!): [LinkedInCredentialRecord!]!
  getLinkedInAdCampaigns(input: LinkedInCampaignQueryInput!): [LinkedInCampaign!]!
}

type TiktokRegion {
  data: [TiktokRegionData!]
}

type TiktokAdvertiserInfo {
  data: [TiktokAdvertiserInfoData!]
}

type TiktokAdvertiserInfoData {
  advertiserId: String!
  timezone: String!
  currency: String!
}

type TiktokRegionData {
  locationId: String!
  name: String!
  nextLevelIds: [String]!
  areaType: String!
  level: String!
  regionCode: String!
  parentId: String!
  supportBelow18: Boolean!
}

type TikTokAdAccount {
  credentialId: Int!
  targetPageName: String!
  targetPageLogoUrl: String
  socialAccountId: String!
  associatedProfiles: [TikTokProfile!]!
}

type TikTokProfile {
  credentialId: Int!
  targetPageName: String!
  socialAccountId: String!
  targetPageLogoUrl: String
  identityId: String!
  identityType: String!
  identityAuthorizedBcId: String
}

input TikTokAdAccountQueryInput {
  ApplicationCompanyId: String!
  ChannelId: Int!
  ActiveInd: Int!
}

input TikTokRegionQueryInput {
  companyId: String!
  credentialId: Int!
  advertiserId: String!
  placements: [String!]!
  objectiveType: String!
  level: String
}

input TikTokAdvertisersInfoQueryInput {
  companyId: String!
  credentialId: Int!
  advertiserIds: [String!]!
}

input EngageAdsCreateTiktokSparkAdParams {
  companyId: String!
  credentialId: Int!
  campaign: TiktokCampaignInput!
  adGroup: TiktokAdGroupInput!
  ad: TiktokAdInput!
}

input TiktokCampaignInput {
  campaignId: String
  objectiveType: String!
  campaignName: String!
  budget: Int!
  advertiserId: String!
  budgetMode: String!
}

input TiktokAdGroupInput {
  advertiserId: String!
  adgroupName: String!
  scheduleStartTime: String!
  scheduleType: String!
  billingEvent: String!
  pacing: String!
  budget: Int!
  budgetMode: String!
  locationIds: [String!]!
  placements: [String!]!
  scheduleEndTime: String
  optimizationGoal: String!
  bidPrice: Int!
  frequency: Int
  frequencySchedule: Int
  conversionBidPrice: Float
  promotionType: String
}

input TiktokAdInput {
  advertiserId: String!
  creatives: [TiktokCreativeInput!]!
}

input TiktokCreativeInput {
  adName: String!
  identityType: String!
  identityId: String!
  adFormat: String!
  videoId: String!
  callToAction: String
  landingPageUrl: String
  identityAuthorizedBcId: String
}

type Metadata {
  error: Error
}

type Error {
  name: String
  message: String
}

type EngageAdsCreateTiktokSparkAdResponse {
  campaignId: String
  metadata: Metadata
}

input FacebookBoostInput {
  nativeId: String!
  adAccountCredentialId: Int!
  profileCredentialId: Int!
  campaignId: String!
  duration: Int!
  budget: Float!
  audienceId: String!
  advertiserId: String!
}

type FacebookBoostResponse {
  boostId: String
  boostingErrors: [String]
}

input LinkedInCredentialsFilter {
  applicationCompanyId: String!
  userId: String!
  activeInd: Int!
}

input LinkedInAssociatedPagesQuery {
  companyId: String!
  credentialId: Int!
}

type LinkedInCredentialRecord {
  socialAccountId: String!
  targetPageName: String!
  credentialId: Int
  channelName: String!
  targetPageLogoUrl: String
  tokenId: String
  tokenDetails: TokenDetails
}

type TokenDetails {
  token: String
}

type Mutation {
  engageAdsCreateTiktokSparkAd(params: EngageAdsCreateTiktokSparkAdParams!): EngageAdsCreateTiktokSparkAdResponse!
  boostFacebookPost(input: FacebookBoostInput!): FacebookBoostResponse!
  engageAdsCreateLinkedInTextAd(params: EngageAdsCreateLinkedInTextAdParams!): EngageAdsCreateLinkedInTextAdResponse!
  engageAdsCreateLinkedInImageAd(params: EngageAdsCreateLinkedInImageAdParams!): EngageAdsCreateLinkedInImageAdResponse!
  engageAdsCreateLinkedInVideoAd(params: EngageAdsCreateLinkedInVideoAdParams!): EngageAdsCreateLinkedInVideoAdResponse!
  engageAdsCreateLinkedInDocumentAd(params: EngageAdsCreateLinkedInDocumentAdParams!): EngageAdsCreateLinkedInDocumentAdResponse!
}

type Budget {
  amount: String!
  currencyCode: String!
}

type LinkedInCampaign {
  id: Int!
  name: String!
  status: String!
  type: String
  dailyBudget: Budget
  unitCost: Budget
}

input LinkedInCampaignQueryInput {
  companyId: String!
  credentialId: Int!
  adAccountId: String!
  status: String
  type: String
}

input LinkedInTextAdCampaignInput {
  campaignId: String!
  adAccountId: String!
}

input LinkedInCreativeTextAdInput {
  adName: String!
  destinationUrl: String!
  description: String!
  callToAction: String
}

input EngageAdsCreateLinkedInTextAdParams {
  companyId: String!
  credentialId: Int!
  advertiserId: String!
  postId: String
  campaign: LinkedInTextAdCampaignInput!
  creative: LinkedInCreativeTextAdInput!
}

type EngageAdsCreateLinkedInTextAdResponse {
  campaignId: String
  creativeId: String
  metadata: Metadata
}

input LinkedInImageAdCampaignInput {
  campaignId: String!
  adAccountId: String!
}

input LinkedInCreativeImageAdInput {
  adName: String!
  destinationUrl: String
  description: String
  callToAction: String
  mediaId: String
  title: String
  imageUrl: String
}

input EngageAdsCreateLinkedInImageAdParams {
  companyId: String!
  credentialId: Int!
  advertiserId: String!
  postId: String
  campaign: LinkedInImageAdCampaignInput!
  creative: LinkedInCreativeImageAdInput!
}

type EngageAdsCreateLinkedInImageAdResponse {
  campaignId: String
  creativeId: String
  metadata: Metadata
}

input LinkedInVideoAdCampaignInput {
  campaignId: String!
  adAccountId: String!
}

input LinkedInCreativeVideoAdInput {
  adName: String!
}

input EngageAdsCreateLinkedInVideoAdParams {
  companyId: String!
  credentialId: Int!
  advertiserId: String!
  postId: String
  campaign: LinkedInVideoAdCampaignInput!
  creative: LinkedInCreativeVideoAdInput!
}

type EngageAdsCreateLinkedInVideoAdResponse {
  campaignId: String
  creativeId: String
  metadata: Metadata
}

input LinkedInDocumentAdCampaignInput {
  campaignId: String!
  adAccountId: String!
}

input LinkedInCreativeDocumentAdInput {
  adName: String!
}

input EngageAdsCreateLinkedInDocumentAdParams {
  companyId: String!
  credentialId: Int!
  advertiserId: String!
  postId: String
  campaign: LinkedInDocumentAdCampaignInput!
  creative: LinkedInCreativeDocumentAdInput!
}

type EngageAdsCreateLinkedInDocumentAdResponse {
  campaignId: String
  creativeId: String
  metadata: Metadata
}

`;
