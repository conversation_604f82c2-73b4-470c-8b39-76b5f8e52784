import { LambdaLogger } from "@meltwater/lambda-monitoring";
import type { GetTiktokAdvertisersInfoParams } from "@meltwater/engage-ads-graphql-sdk/dist/types/tiktok-advertisers-info";
import type { GetTiktokAdvertiserInfoResponse } from "@meltwater/engage-ads-tiktok-services/dist/types/tiktok-advertiser";
import { tiktokAdvertiserInfoService } from "../services/TiktokAdvertiserInfo";

const logger = LambdaLogger.getInstance();

export const tikTokAdvertisersInfo = async (
  _: unknown,
  { input }: { input: GetTiktokAdvertisersInfoParams },
): Promise<GetTiktokAdvertiserInfoResponse> => {
  logger.info("Processing TikTok advertisers info query", {
    "internal.engage.ads.args": JSON.stringify(input),
  });

  try {
    const result = await tiktokAdvertiserInfoService.getTikTokAdvertisersInfo(input);
    logger.info("TikTok advertisers info query processed successfully", {
      "internal.engage.ads.response": JSON.stringify(result),
      "internal.engage.ads.count": result.data.length,
    });
    return result;
  } catch (error) {
    logger.error("Failed to process TikTok advertisers info query", {
      "internal.engage.ads.error": error instanceof Error ? error.message : "Unknown error",
      "internal.engage.ads.args": JSON.stringify(input),
    });
    throw error;
  }
};
