import { LambdaLogger } from "@meltwater/lambda-monitoring";
import { tiktokRegionService } from "../services/TiktokRegionService";
import type { GetTiktokRegionArgs } from "../types";

const logger = LambdaLogger.getInstance();

export const tikTokRegions = async (_: unknown, args: GetTiktokRegionArgs) => {
  try {
    const result = await tiktokRegionService.getTiktokRegion(args);
    logger.info("TikTok Region query processed successfully", { resultCount: result.data.length });
    return result;
  } catch (error) {
    logger.error("Error processing TikTok Region query", { error });
    throw error;
  }
};
