import { LambdaLogger } from "@meltwater/lambda-monitoring";
import type { GraphQLContext } from "../types/context";
import type { FacebookSavedAudience } from "../types";
import { facebookSavedAudienceService } from "../services/FacebookSavedAudienceService";

const logger = LambdaLogger.getInstance();

export const getFacebookSavedAudience = async (
  _parent: unknown,
  args: { filter: { credentialId?: number } },
  context: GraphQLContext,
): Promise<FacebookSavedAudience[]> => {
  logger.info("GraphQL resolver: Getting Facebook saved audiences", {
    credentialId: args.filter.credentialId,
  });

  try {
    const audiences = await facebookSavedAudienceService.getFacebookSavedAudience(
      args.filter.credentialId || 0,
      context.accessToken || "",
    );

    return audiences;
  } catch (error) {
    logger.error("Error in GraphQL resolver for Facebook saved audiences", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });
    return [];
  }
};
