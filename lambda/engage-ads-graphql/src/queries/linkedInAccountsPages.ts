import { LambdaLogger } from "@meltwater/lambda-monitoring";
import { linkedInAccountsPagesService } from "../services/LinkedInAccountsPagesService";
import type { LinkedInCredentialsFilter } from "../types";

const logger = LambdaLogger.getInstance();

export const getLinkedInAdAccounts = async (_: unknown, { input }: { input: LinkedInCredentialsFilter }) => {
  logger.info("Processing getLinkedInAdAccounts query", { "internal.engage.ads.request": JSON.stringify(input) });
  try {
    return await linkedInAccountsPagesService.getLinkedInAdAccounts(input);
  } catch (error) {
    logger.error("Error fetching LinkedIn ad accounts:", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      input,
    });
    throw error;
  }
};
