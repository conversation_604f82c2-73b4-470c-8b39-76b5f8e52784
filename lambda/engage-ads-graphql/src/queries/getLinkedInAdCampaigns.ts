import { LambdaLogger } from "@meltwater/lambda-monitoring";
import { linkedInAdCampaignsService } from "../services/LinkedInAdCampaignsService";
import type { LinkedInCampaignQueryInput } from "../types";

const logger = LambdaLogger.getInstance();

export const getLinkedInAdCampaigns = async (_: unknown, { input }: { input: LinkedInCampaignQueryInput }) => {
  logger.info("GraphQL resolver: getLinkedInAdCampaigns", { input });
  try {
    return await linkedInAdCampaignsService.getLinkedInAdCampaigns({
      companyId: input.companyId,
      credentialId: input.credentialId,
      adAccountId: input.adAccountId,
      status: input.status,
      type: input.type,
    });
  } catch (error) {
    logger.error("Error in getLinkedInAdCampaigns resolver", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });
    throw error;
  }
};
