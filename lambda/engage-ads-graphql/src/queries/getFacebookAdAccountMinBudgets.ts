import { LambdaLogger } from "@meltwater/lambda-monitoring";
import type { GraphQLContext } from "../types/context";
import type { FacebookAdAccountMinBudgets } from "../types";
import { facebookAdAccountMinBudgetsService } from "../services/FacebookAdAccountMinBudgetsService";

const logger = LambdaLogger.getInstance();

export const getFacebookAdAccountMinBudgets = async (
  _parent: unknown,
  args: { credentialId?: number },
  context: GraphQLContext,
): Promise<FacebookAdAccountMinBudgets> => {
  logger.info("GraphQL resolver: Getting Facebook ad account minimum budgets", {
    credentialId: args.credentialId,
  });

  try {
    const minBudgets = await facebookAdAccountMinBudgetsService.getFacebookAdAccountMinBudgets(
      args.credentialId || 0,
      context.accessToken || "",
    );

    return minBudgets;
  } catch (error) {
    logger.error("Error in GraphQL resolver for Facebook ad account minimum budgets", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });

    // Return default values in case of error
    return {
      currency: "USD",
      impressionsBudget: 0,
      videoBudget: 0,
      highFreqBudget: 0,
      lowFreqBudget: 0,
    };
  }
};
