import { tikTokAdAccountService } from "../services";
import type { TikTokAdAccountQueryInput } from "@meltwater/engage-ads-tiktok-services";
import { LambdaLogger } from "@meltwater/lambda-monitoring";

const logger = LambdaLogger.getInstance();

export const tikTokAdAccounts = async (_: unknown, { input }: { input: TikTokAdAccountQueryInput }) => {
  logger.info("Processing TikTok Ad Accounts query", { input });

  try {
    const accessToken = process.env.GRAPHQL_S31_KEY;
    if (!accessToken) {
      throw new Error("Access token is missing");
    }

    const result = await tikTokAdAccountService.getTikTokAdAccounts(input, accessToken);
    logger.info("TikTok Ad Accounts query processed successfully", { resultCount: result.length });
    return result;
  } catch (error) {
    logger.error("Error processing TikTok Ad Accounts query", { error });
    throw error;
  }
};
