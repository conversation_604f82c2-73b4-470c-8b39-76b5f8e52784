import type { EngageAdsChannelInsightsArgs } from "../types";
import { engageAdsChannelInsightsService } from "../services";
import { LambdaLogger } from "@meltwater/lambda-monitoring";

const logger = LambdaLogger.getInstance();

export const engageAdsChannelInsights = async (_: unknown, args: EngageAdsChannelInsightsArgs) => {
  logger.info("Processing channel insights request", { filter: args.filter });
  return engageAdsChannelInsightsService.getChannelInsights(args);
};
