import { LambdaLogger } from "@meltwater/lambda-monitoring";
import type { FacebookAdCampaign, FacebookAdCampaignData, GraphQLContext } from "../types";
import { facebookAdCampaignsService } from "../services/FacebookAdCampaignsService";

const logger = LambdaLogger.getInstance();

export const getFacebookAdCampaigns = async (
  _parent: unknown,
  args: { filter: FacebookAdCampaignData },
  context: GraphQLContext,
): Promise<FacebookAdCampaign[]> => {
  logger.info("GraphQL resolver: Getting Facebook ad campaigns", {
    credentialId: args.filter.credentialId,
  });

  try {
    const campaigns = await facebookAdCampaignsService.getFacebookAdCampaigns(
      { credentialId: args.filter.credentialId || 0 },
      context.accessToken || "",
    );

    return campaigns;
  } catch (error) {
    logger.error("Error in GraphQL resolver for Facebook ad campaigns", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });
    return [];
  }
};
