import { LambdaLogger } from "@meltwater/lambda-monitoring";
import { campaignService } from "../services/CampaignService";
import type { CampaignQueryInput } from "../types";

const logger = LambdaLogger.getInstance();

export const queryCampaigns = async (_: unknown, { input }: { input: CampaignQueryInput }) => {
  logger.info("Processing queryCampaigns", { input });

  try {
    const campaigns = await campaignService.getCampaignsByAdvertiserIds(
      input.advertiserIds,
      process.env.GRAPHQL_S31_KEY ?? "",
    );
    return campaigns;
  } catch (error) {
    logger.error("Error processing queryCampaigns", { error });
    throw error;
  }
};
