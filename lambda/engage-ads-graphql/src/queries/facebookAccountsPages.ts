import { LambdaLogger } from "@meltwater/lambda-monitoring";
import { facebookAdAccountsPagesService } from "../services/FacebookAdAccountsPagesService";
import type { FacebookCredentialsFilter } from "../types";

const logger = LambdaLogger.getInstance();

export const getFacebookAccountsPages = async (_: unknown, { input }: { input: FacebookCredentialsFilter }) => {
  logger.info("Processing getFacebookAccountsPages query", { input });
  try {
    return await facebookAdAccountsPagesService.getFacebookAdAccountsPages(input);
  } catch (error) {
    logger.error("Error in getFacebookAccountsPages query", { error });
    throw error;
  }
};
