import { LambdaLogger } from "@meltwater/lambda-monitoring";
import { linkedInAccountsPagesService } from "../services/LinkedInAccountsPagesService";

const logger = LambdaLogger.getInstance();

export const getLinkedInAssociatedPages = async (
  _: unknown,
  { query }: { query: { companyId: string; credentialId: number } },
) => {
  logger.info("Processing getLinkedInAssociatedPages query", {
    "internal.engage.ads.request": JSON.stringify(query),
  });
  try {
    return await linkedInAccountsPagesService.getLinkedInAssociatedPages(query.credentialId, query.companyId);
  } catch (error) {
    logger.error("Error fetching LinkedIn associated pages:", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      query,
    });
    throw error;
  }
};
