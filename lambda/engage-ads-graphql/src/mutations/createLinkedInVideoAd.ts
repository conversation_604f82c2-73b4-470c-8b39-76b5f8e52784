import type { CreateLinkedInVideoAdArgs } from "../types/createLinkedInVideoAdTypes";
import { linkedInVideoAdService } from "../services/LinkedInVideoAdService";
import { LambdaLogger } from "@meltwater/lambda-monitoring";

const logger = LambdaLogger.getInstance();

export const createLinkedInVideoAd = async (_: unknown, args: CreateLinkedInVideoAdArgs) => {
  logger.info("GraphQL Mutation: createLinkedInVideoAd called", {
    args: JSON.stringify(args),
  });

  try {
    const result = await linkedInVideoAdService.createVideoAd(args.params);

    logger.info("LinkedIn Video Ad creation successful", { result: JSON.stringify(result) });

    return {
      campaignId: result.campaign?.campaignId ?? null,
      creativeId: result.creative?.creativeId ?? null,
      metadata: null,
    };
  } catch (error) {
    logger.error("Failed to create LinkedIn Video Ad", {
      error: error instanceof Error ? error.message : String(error),
    });
    return {
      campaignId: null,
      creativeId: null,
      metadata: {
        error: {
          name: error instanceof Error ? error.name : "UnknownError",
          message: error instanceof Error ? error.message : String(error),
        },
      },
    };
  }
};
