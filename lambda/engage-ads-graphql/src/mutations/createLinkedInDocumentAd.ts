import type { CreateLinkedInDocumentAdArgs } from "../types/createLinkedInDocumentAdTypes";
import { linkedInDocumentAdService } from "../services/LinkedInDocumentAdService";
import { LambdaLogger } from "@meltwater/lambda-monitoring";

const logger = LambdaLogger.getInstance();

export const createLinkedInDocumentAd = async (_: unknown, args: CreateLinkedInDocumentAdArgs) => {
  logger.info("GraphQL Mutation: createLinkedInDocumentAd called", {
    args: JSON.stringify(args),
  });

  try {
    const result = await linkedInDocumentAdService.createDocumentAd(args.params);

    logger.info("LinkedIn Document Ad creation successful", { result: JSON.stringify(result) });

    return {
      campaignId: result.campaign?.campaignId ?? null,
      creativeId: result.creative?.creativeId ?? null,
      metadata: null,
    };
  } catch (error) {
    logger.error("Failed to create LinkedIn Document Ad", {
      error: error instanceof Error ? error.message : String(error),
    });
    return {
      campaignId: null,
      creativeId: null,
      metadata: {
        error: {
          name: error instanceof Error ? error.name : "UnknownError",
          message: error instanceof Error ? error.message : String(error),
        },
      },
    };
  }
};
