import type { CreateLinkedInImageAdArgs } from "../types/createLinkedInImageAdTypes";
import { linkedInImageAdService } from "../services/LinkedInImageAdService";
import { LambdaLogger } from "@meltwater/lambda-monitoring";

const logger = LambdaLogger.getInstance();

export const createLinkedInImageAd = async (_: unknown, args: CreateLinkedInImageAdArgs) => {
  logger.info("GraphQL Mutation: createLinkedInImageAd called", {
    args: JSON.stringify(args),
  });

  try {
    const result = await linkedInImageAdService.createImageAd(args.params);

    logger.info("LinkedIn Image Ad creation successful", { result: JSON.stringify(result) });

    return {
      campaignId: result.campaign?.campaignId ?? null,
      creativeId: result.creative?.creativeId ?? null,
      metadata: null,
    };
  } catch (error) {
    logger.error("Failed to create LinkedIn Image Ad", {
      error: error instanceof Error ? error.message : String(error),
    });
    return {
      campaignId: null,
      creativeId: null,
      metadata: {
        error: {
          name: error instanceof Error ? error.name : "UnknownError",
          message: error instanceof Error ? error.message : String(error),
        },
      },
    };
  }
};
