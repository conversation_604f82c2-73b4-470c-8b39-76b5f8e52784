import type { CreateTiktokSparkAdArgs } from "../types/createTiktokSparkAdTypes";
import { tiktokSparkAdService } from "../services/TiktokSparkAdService";
import { LambdaLogger } from "@meltwater/lambda-monitoring";

const logger = LambdaLogger.getInstance();

const createTiktokSparkAdMutation = async (_: unknown, args: CreateTiktokSparkAdArgs) => {
  logger.info("Creating TikTok Spark Ad", {
    "internal.engage.ads.args": JSON.stringify(args),
  });

  try {
    const tikTokResult = await tiktokSparkAdService.createSparkAd(args);
    logger.info("TikTok Spark Ad created successfully", {
      "internal.engage.ads.response": JSON.stringify(tikTokResult),
    });
    return {
      campaignId: tikTokResult.campaign?.campaignId || null,
      metadata: null,
    };
  } catch (error) {
    logger.error("Failed to create TikTok Spark Ad", {
      "internal.engage.ads.error": error instanceof Error ? error.message : "Unknown error",
      "internal.engage.ads.args": JSON.stringify(args),
    });
    if (error instanceof Error) {
      return {
        campaignId: null,
        metadata: { error: { name: error.name, message: error.message } },
      };
    }
    throw error;
  }
};

export const createTiktokSparkAd = createTiktokSparkAdMutation;
