import type { CreateLinkedInTextAdArgs } from "../types/createLinkedInTextAdTypes";
import { linkedInTextAdService } from "../services/LinkedInTextAdService";
import { LambdaLogger } from "@meltwater/lambda-monitoring";

const logger = LambdaLogger.getInstance();

export const createLinkedInTextAd = async (_: unknown, args: CreateLinkedInTextAdArgs) => {
  logger.info("GraphQL Mutation: createLinkedInTextAd called", {
    args: JSON.stringify(args),
  });

  try {
    const result = await linkedInTextAdService.createTextAd(args.params);

    logger.info("LinkedIn Text Ad creation successful", { result: JSON.stringify(result) });

    return {
      campaignId: result.campaign?.campaignId ?? null,
      creativeId: result.creative?.creativeId ?? null,
      metadata: null,
    };
  } catch (error) {
    logger.error("Failed to create LinkedIn Text Ad", {
      error: error instanceof Error ? error.message : String(error),
    });
    return {
      campaignId: null,
      creativeId: null,
      metadata: {
        error: {
          name: error instanceof Error ? error.name : "UnknownError",
          message: error instanceof Error ? error.message : String(error),
        },
      },
    };
  }
};
