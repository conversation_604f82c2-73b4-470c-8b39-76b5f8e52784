import { LambdaLogger } from "@meltwater/lambda-monitoring";
import type {
  FacebookBoostInput,
  FacebookBoostResponse,
  GraphQLContext,
  FacebookBoostParamsWithDatabase,
} from "../types";
import { facebookBoostService } from "../services/FacebookBoostService";
import {
  type GroupedMessageChannelResult,
  type GroupedMessageProfileResult,
} from "@meltwater/engage-ads-facebook-services";

const logger = LambdaLogger.getInstance();

export const boostFacebookPost = async (
  _parent: unknown,
  args: { input: FacebookBoostInput },
  context: GraphQLContext,
): Promise<FacebookBoostResponse> => {
  const { input } = args;
  const {
    nativeId,
    adAccountCredentialId,
    profileCredentialId,
    campaignId,
    duration,
    budget,
    audienceId,
    advertiserId,
  } = input;

  logger.info("GraphQL resolver: Initiating Facebook post boost", {
    nativeId,
    adAccountCredentialId,
    profileCredentialId,
  });

  const accessToken = context.accessToken || "";
  if (!accessToken) {
    logger.error("Access token is missing in context");
    return { boostingErrors: ["Authorization Error: Access token is missing."] };
  }
  logger.info("Access token found in context (first 10 chars):", { tokenStart: accessToken.substring(0, 10) });

  // Removed logic attempting to get companyId from context.
  // Will fetch from WarpzoneModel later using nativeId.

  try {
    // Step 1: Get the Grouped Message using the *profil
    const groupedMessageData = await facebookBoostService.getGroupedMessage(nativeId, accessToken);

    if (!groupedMessageData?.groupedMessage?.channels) {
      logger.error("Could not retrieve valid grouped message data or channels from SPAPI", { nativeId });
      return { boostingErrors: ["Failed to retrieve post details needed for boosting."] };
    }

    let messageId: string | undefined;
    const facebookChannel = groupedMessageData.groupedMessage.channels.find(
      (ch: GroupedMessageChannelResult) => ch.channel === "FACEBOOK", // Added type for ch
    );

    if (facebookChannel?.profiles) {
      // Find the profile using the profileCredentialId
      const targetProfile = facebookChannel.profiles.find(
        (p: GroupedMessageProfileResult) => p.credentialId === profileCredentialId, // Added type for p
      );
      messageId = targetProfile?.legacyMessageId;
    }

    if (!messageId) {
      logger.error("Could not find matching legacyMessageId for the given nativeId and profileCredentialId", {
        nativeId,
        profileCredentialId, // Use the correct ID in logs
        retrievedChannels: groupedMessageData.groupedMessage.channels?.length,
      });
      return { boostingErrors: ["Could not find the specific Facebook post message ID required for boosting."] };
    }

    logger.info("Found legacyMessageId (messageId) for boosting", { nativeId, profileCredentialId, messageId });

    const boostParams: FacebookBoostParamsWithDatabase = {
      messageId,
      campaignId,
      duration,
      budget,
      audienceId,
      adAccountCredentialId,
      nativeId,
      advertiserId,
    };

    const boostResult = await facebookBoostService.boostFacebookPostWithDatabase(boostParams, accessToken);

    logger.info("Facebook post boost process completed via service (including database operations)", {
      nativeId,
      adAccountCredentialId,
      profileCredentialId,
      boostId: boostResult.boostId,
      hasErrors: !!boostResult.boostingErrors?.length,
    });

    return boostResult;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Error in GraphQL resolver for boosting Facebook post", {
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
      nativeId,
      adAccountCredentialId,
      profileCredentialId,
    });
    return { boostingErrors: [`An unexpected error occurred during the boost process: ${errorMessage}`] };
  }
};
