import { ApolloServer } from "@apollo/server";
import { startServerAndCreateLambdaHandler, handlers } from "@as-integrations/aws-lambda";
import { LambdaLogger, LambdaTracer } from "@meltwater/lambda-monitoring";
import type { APIGatewayProxyEventV2, Context, APIGatewayProxyStructuredResultV2 } from "aws-lambda";

import { typeDefs } from "./types";
import { resolvers } from "./resolvers";
import type { GraphQLFormattedError } from "graphql";

const logger = LambdaLogger.getInstance();
const tracer = LambdaTracer.getInstance();

const server = new ApolloServer({
  typeDefs,
  resolvers,
  introspection: process.env.NODE_ENV !== "production",
  formatError: (formattedError: GraphQLFormattedError) => {
    if (formattedError.extensions?.code === "BAD_USER_INPUT") {
      const originalMessage = formattedError.message;
      let customMessage = "Invalid input: ";

      if (originalMessage.includes("Field")) {
        const fieldMatch = originalMessage.match(/Field "(\w+)"/);
        if (fieldMatch) {
          const fieldName = fieldMatch[1];
          customMessage += `${fieldName} is invalid or missing. `;
        }
      }

      if (originalMessage.includes("got invalid value")) {
        customMessage += "Please check your input and try again.";
      }

      return {
        message: customMessage,
        extensions: {
          code: "BAD_USER_INPUT",
          originalError: originalMessage,
        },
      };
    }
    return formattedError;
  },
});

const corsMiddleware = async (event: APIGatewayProxyEventV2) => {
  const allowedOrigins = [
    "https://app.meltwater.com",
    "https://localhost.meltwater.net",
    "https://staging.meltwater.net",
    "https://studio.apollographql.com",
    "https://engage-ads-web.preview.meltwater.net",
  ];

  const origin = event.headers.origin;
  const isAllowed =
    origin &&
    allowedOrigins.some((allowedOrigin) => {
      return origin.includes(allowedOrigin);
    });

  return async (result: APIGatewayProxyStructuredResultV2) => {
    if (typeof result === "object" && result !== null) {
      result.headers = {
        ...result.headers,
        "Access-Control-Allow-Origin": isAllowed ? origin : allowedOrigins[0],
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
        "Access-Control-Allow-Credentials": "true",
      };

      if (event.requestContext.http.method === "OPTIONS") {
        result.statusCode = 200;
        result.body = "";
      }
    }
  };
};

export const graphqlHandler = startServerAndCreateLambdaHandler(
  server,
  handlers.createAPIGatewayProxyEventV2RequestHandler(),
  {
    middleware: [corsMiddleware],
    context: async ({ event }) => {
      const authHeader = event.headers?.authorization || "";
      return {
        accessToken: authHeader,
      };
    },
  },
);

export const handler = async (
  event: APIGatewayProxyEventV2,
  context: Context,
): Promise<APIGatewayProxyStructuredResultV2> => {
  logger.info("Lambda handler started");
  tracer.putMetadata("handlerEvent", event);

  try {
    logger.info("GraphQL request started", {
      "internal.engage.ads.path": event.rawPath,
      "internal.engage.ads.method": event.requestContext.http.method,
    });
    const response = await graphqlHandler(event, context, () => {});
    logger.info("GraphQL request completed", {
      "internal.engage.ads.response": "success",
    });
    if (response === undefined) {
      throw new Error("GraphQL handler returned undefined");
    }
    return response;
  } catch (error) {
    logger.error("Error in Lambda handler:", { error });
    tracer.putMetadata("handlerError", error);
    tracer.addErrorAsMetadata(error as Error);
    return {
      statusCode: 500,
      body: JSON.stringify({ message: "Internal Server Error" }),
    };
  }
};
