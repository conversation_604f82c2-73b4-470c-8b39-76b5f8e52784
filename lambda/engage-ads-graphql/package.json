{"name": "engage-ads-graphql", "version": "1.66.1-engage-20250619170136.0", "author": "Team Area51 <<EMAIL>>", "devDependencies": {"@types/aws-lambda": "^8.10.135", "esbuild": "^0.20.1"}, "files": ["dist"], "license": "MIT", "main": "dist/index.js", "private": true, "scripts": {"build": "tsc", "build:package": "node ../../build.js", "publish:ecr": "bash ../../scripts/publish-to-ecr.sh engage-ads $(grep '\"version\":' package.json | awk -F '\"' '{print $4}')", "deploy:lambda": "bash ../../scripts/deploy-lambda.sh engage-ads-lambda engage-ads $(grep '\"version\":' package.json | awk -F '\"' '{print $4}')", "clean": "rimraf dist build tsconfig.tsbuildinfo", "local:lambda": "npx ts-node-dev --respawn --transpile-only --no-notify src/local/local.ts", "test:lambda:tiktokInsights": "ts-node --transpile-only src/test/lambda-tester.ts getTikTokInsights", "test:lambda:tiktokInsightsSortByVideoViews": "ts-node --transpile-only src/test/lambda-tester.ts getTikTokInsightsSortByVideoViews", "test:lambda:createTiktokSparkAdReach": "ts-node-dev --respawn --transpile-only --no-notify src/test/lambda-tester.ts createTiktokSparkAdReach", "test:lambda:createTiktokSparkAdEngagement": "ts-node-dev --respawn --transpile-only --no-notify src/test/lambda-tester.ts createTiktokSparkAdEngagement", "test:lambda:createTiktokSparkAdTraffic": "ts-node-dev --respawn --transpile-only --no-notify src/test/lambda-tester.ts createTiktokSparkAdTraffic", "test:lambda:createTiktokSparkAdVideoViews": "ts-node-dev --respawn --transpile-only --no-notify src/test/lambda-tester.ts createTiktokSparkAdVideoViews", "test:lambda:createTiktokSparkAdVideoViewsBCAUTH": "ts-node-dev --respawn --transpile-only --no-notify src/test/lambda-tester.ts createTiktokSparkAdVideoViewsBCAUTH", "test:lambda:getTiktokRegions": "ts-node-dev --respawn --transpile-only --no-notify src/test/lambda-tester.ts getTiktokRegions", "test:lambda:tiktokadaccounts": "ts-node-dev --respawn --transpile-only --no-notify src/test/lambda-tester.ts getTikTokAdAccounts", "test:lambda:tiktokadvertisersinfo": "ts-node-dev --respawn --transpile-only --no-notify src/test/lambda-tester.ts getTiktokAdvertisersInfo", "test:lambda:createTiktokSparkAdWithExistingCampaign": "ts-node-dev --respawn --transpile-only --no-notify src/test/lambda-tester.ts createTiktokSparkAdWithExistingCampaign", "test:lambda:getFacebookAccountsPages": "ts-node-dev --respawn --transpile-only --no-notify src/test/lambda-tester.ts getFacebookAccountsPages", "test:lambda:facebookInsights": "ts-node-dev --respawn --transpile-only --no-notify src/test/lambda-tester.ts getFacebookInsights", "test:lambda:facebookInsightsSortedByLikes": "ts-node --transpile-only src/test/lambda-tester.ts getFacebookInsightsSortedByLikes", "test:lambda:getTiktokCampaigns": "ts-node-dev --respawn --transpile-only --no-notify src/test/lambda-tester.ts getCampaigns", "test:lambda:getFacebookAdCampaigns": "ts-node --transpile-only src/test/lambda-tester.ts getFacebookAdCampaigns", "test:lambda:getFacebookAdAccountMinBudgets": "ts-node --transpile-only src/test/lambda-tester.ts getFacebookAdAccountMinBudgets", "test:lambda:getFacebookSavedAudience": "ts-node --transpile-only src/test/lambda-tester.ts getFacebookSavedAudience", "test:lambda:boostFacebookPost": "ts-node --transpile-only src/test/lambda-tester.ts boostFacebookPost", "test:lambda:getLinkedInAdAccounts": "ts-node --transpile-only src/test/lambda-tester.ts getLinkedInAdAccounts", "test:lambda:getLinkedInAssociatedPages": "ts-node --transpile-only src/test/lambda-tester.ts getLinkedInAssociatedPages", "test:lambda:getEngageAdsChannelInsightsLinkedIn": "ts-node --transpile-only src/test/lambda-tester.ts getEngageAdsChannelInsightsLinkedIn", "test:lambda:getLinkedInAdCampaigns": "ts-node --transpile-only src/test/lambda-tester.ts getLinkedInAdCampaigns", "test:lambda:createLinkedInTextAd": "ts-node --transpile-only src/test/lambda-tester.ts createLinkedInTextAd", "test:lambda:createLinkedInImageAd": "ts-node --transpile-only src/test/lambda-tester.ts createLinkedInImageAd", "test:lambda:createLinkedInVideoAd": "ts-node --transpile-only src/test/lambda-tester.ts createLinkedInVideoAd", "test:lambda:createLinkedInDocumentAd": "ts-node --transpile-only src/test/lambda-tester.ts createLinkedInDocumentAd"}, "types": "dist/index.d.ts", "volta": {"node": "20.11.1"}, "dependencies": {"@apollo/server": "^4.10.4", "@as-integrations/aws-lambda": "^3.1.0", "@aws-sdk/client-secrets-manager": "^3.645.0", "@meltwater/engage-ads-commons": "^2.9.2", "@meltwater/engage-ads-facebook-services": "^1.31.3", "@meltwater/engage-ads-linkedin-services": "^1.15.0", "@meltwater/engage-ads-tiktok-services": "^2.36.2", "@meltwater/lambda-monitoring": "^0.2.5", "graphql": "^16.8.1"}}