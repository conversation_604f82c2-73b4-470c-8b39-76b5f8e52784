{"name": "engage-ads-warpzone-processor", "description": "", "version": "1.21.2", "author": "", "devDependencies": {"@types/aws-lambda": "^8.10.135", "esbuild": "^0.20.1"}, "files": ["dist"], "license": "MIT", "main": "dist/index.js", "private": true, "scripts": {"build": "tsc", "build:package": "node ../../build.js", "publish:ecr": "bash ../../scripts/publish-to-ecr.sh warpzone-processor $(grep '\"version\":' package.json | awk -F '\"' '{print $4}')", "deploy:lambda": "bash ../../scripts/deploy-lambda.sh warpzone-processor-lambda warpzone-processor $(grep '\"version\":' package.json | awk -F '\"' '{print $4}')", "clean": "rimraf dist build tsconfig.tsbuildinfo", "local:lambda": "npx ts-node-dev --respawn --transpile-only --no-notify local/warpzone-consumer.ts"}, "types": "dist/index.d.ts", "volta": {"node": "20.11.1"}, "dependencies": {"@aws-sdk/client-lambda": "^3.0.0", "@meltwater/engage-ads-db-sdk": "^1.22.2", "@meltwater/grimoire-analytics-services": "^3.0.0", "@meltwater/grimoire-commons": "^4.8.4", "@meltwater/grimoire-lambda-abstracts": "^0.26.2", "@meltwater/grimoire-publish-services": "^5.0.8", "@meltwater/lambda-monitoring": "^0.2.5"}}