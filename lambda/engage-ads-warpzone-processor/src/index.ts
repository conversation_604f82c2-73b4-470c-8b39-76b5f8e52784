import type { Context, SQSEvent } from "aws-lambda";
import {
  connectToDatabase,
  WarpzoneModel,
  type WarpzoneEvent,
  type WarpzonePayload,
} from "@meltwater/engage-ads-db-sdk";
import { WarpzoneConsumer } from "@meltwater/grimoire-lambda-abstracts";
import { LambdaLogger, LambdaTracer } from "@meltwater/lambda-monitoring";
import { toTimestampInMilliseconds } from "@meltwater/grimoire-commons";

const logger = LambdaLogger.getInstance();

class EngageAdsWarpzoneProcessor extends WarpzoneConsumer {
  // Override processEvent to add more detailed logging
  async processEvent(event: any, context: any): Promise<string> {
    logger.info("processEvent started", {
      "internal.engage.ads.recordsCount": event.Records?.length || 0,
      "internal.engage.ads.awsRequestId": context.awsRequestId,
    });

    try {
      if (!event.Records || event.Records.length === 0) {
        logger.error("No SQS records found in event");
        throw new Error("No SQS records found in event");
      }

      logger.info("Parsing SQS record body...");
      const rawBody = event.Records[0].body;
      logger.info("Raw SQS body received", {
        "internal.engage.ads.rawBody": rawBody.substring(0, 500) + (rawBody.length > 500 ? "..." : "")
      });

      const payload = JSON.parse(rawBody);
      const attributes = event.Records[0].attributes;

      logger.info("SQS record parsed successfully", {
        "internal.engage.ads.payloadKeys": Object.keys(payload),
        "internal.engage.ads.payloadType": typeof payload,
      });

      logger.info("Calling handleMessage...");
      await this.handleMessage(payload, attributes);

      logger.info("handleMessage completed successfully");
      return "Processed";
    } catch (error) {
      logger.error("Error in processEvent", {
        error,
        "internal.engage.ads.event": JSON.stringify(event),
      });
      throw error; // Re-throw to properly signal failure to AWS Lambda
    }
  }

  protected async handleMessage(event: WarpzoneEvent, attributes?: unknown): Promise<void> {
    logger.info("Processing warpzone message with payload:", {
      "internal.engage.ads.payload": JSON.stringify(event),
    });
    logger.debug("Message attributes:", { "internal.engage.ads.attributes": JSON.stringify(attributes) });

    try {
      logger.info("Starting database connection...");
      // Ensure database connection
      await connectToDatabase();
      logger.info("Database connection successful");

      if (this.isWarpzoneEvent(event)) {
        logger.info("Event validation passed, processing payload...");
        const payload = this.normalizeDates(event.payload);
        LambdaTracer.addCustomAttributes({
          "meltwater.document.id": payload.document?.documentId,
          "user.id": payload.applicationUserId,
          "meltwater.company.id": payload.companyId,
          "meltwater.social.credential_id": payload.credentialId,
          "meltwater.document.external_id": payload.document?.externalId,
          "meltwater.document.url": payload.document?.metaData?.url,
        });

        logger.info("Creating warpzone payload in database...");
        await WarpzoneModel.createPayload(payload);
        logger.info("Warpzone payload created successfully");

        logger.info("Successfully stored warpzone payload", {
          "meltwater.document.id": payload.document?.documentId,
          "user.id": payload.applicationUserId,
          "meltwater.company.id": payload.companyId,
          "meltwater.social.credential_id": payload.credentialId,
          "meltwater.document.external_id": payload.document?.externalId,
          "meltwater.document.url": payload.document?.metaData?.url,
        });
      } else {
        logger.error("Invalid payload format - event validation failed");
        throw new Error("Invalid payload format");
      }
    } catch (error) {
      logger.error("Error processing warpzone message", {
        error,
        "internal.engage.ads.payload": JSON.stringify(event),
      });
      // Re-throw the error to properly signal failure
      throw error;
    }
  }

  private isWarpzoneEvent(payload: unknown): payload is WarpzoneEvent {
    logger.info("Validating warpzone event structure...");

    if (typeof payload !== "object" || payload === null) {
      logger.error("Payload validation failed: not an object or is null", {
        "internal.engage.ads.payloadType": typeof payload,
        "internal.engage.ads.isNull": payload === null,
      });
      return false;
    }

    const event = payload as Partial<WarpzoneEvent>;

    // Log the structure for debugging
    logger.info("Payload structure analysis", {
      "internal.engage.ads.hasEventId": typeof event.eventId,
      "internal.engage.ads.hasEventType": typeof event.eventType,
      "internal.engage.ads.hasSource": typeof event.source,
      "internal.engage.ads.hasPayloadType": typeof event.payloadType,
      "internal.engage.ads.hasPayloadVersion": typeof event.payloadVersion,
      "internal.engage.ads.hasPublisher": typeof event.publisher,
      "internal.engage.ads.hasPayload": typeof event.payload,
      "internal.engage.ads.hasPayloadEncoding": Array.isArray(event.payloadEncoding),
      "internal.engage.ads.hasVersion": typeof event.version,
      "internal.engage.ads.hasPublishedAt": typeof event.publishedAt,
    });

    const isValid = (
      typeof event.eventId === "string" &&
      typeof event.eventType === "string" &&
      typeof event.source === "string" &&
      typeof event.payloadType === "string" &&
      typeof event.payloadVersion === "number" &&
      typeof event.publisher === "string" &&
      typeof event.payload === "object" &&
      event.payload !== null &&
      Array.isArray(event.payloadEncoding) &&
      typeof event.version === "number" &&
      typeof event.publishedAt === "number"
    );

    logger.info("Payload validation result", {
      "internal.engage.ads.isValid": isValid,
    });

    return isValid;
  }

  private normalizeDates(payload: WarpzonePayload): WarpzonePayload {
    if (payload?.document?.body?.publishDate?.date) {
      const date = payload.document.body.publishDate.date;
      payload.document.body.publishDate.date = toTimestampInMilliseconds(date);
    }
    return payload;
  }
}

const warpzoneProcessor = new EngageAdsWarpzoneProcessor();
export const handler = (event: SQSEvent, context: Context) => warpzoneProcessor.handler(event, context);
