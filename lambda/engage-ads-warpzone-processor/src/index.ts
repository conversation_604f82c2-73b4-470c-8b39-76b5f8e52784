import type { Context, SQSEvent } from "aws-lambda";
import {
  connectToDatabase,
  WarpzoneModel,
  type WarpzoneEvent,
  type WarpzonePayload,
} from "@meltwater/engage-ads-db-sdk";
import { WarpzoneConsumer } from "@meltwater/grimoire-lambda-abstracts";
import { LambdaLogger, LambdaTracer } from "@meltwater/lambda-monitoring";
import { toTimestampInMilliseconds } from "@meltwater/grimoire-commons";

const logger = LambdaLogger.getInstance();

class EngageAdsWarpzoneProcessor extends WarpzoneConsumer {
  protected async handleMessage(event: WarpzoneEvent, attributes?: unknown): Promise<void> {
    logger.info("Processing warpzone message with payload:", {
      "internal.engage.ads.payload": JSON.stringify(event),
    });
    logger.debug("Message attributes:", { "internal.engage.ads.attributes": JSON.stringify(attributes) });

    try {
      // Ensure database connection
      await connectToDatabase();

      if (this.isWarpzoneEvent(event)) {
        const payload = this.normalizeDates(event.payload);
        LambdaTracer.addCustomAttributes({
          "meltwater.document.id": payload.document?.documentId,
          "user.id": payload.applicationUserId,
          "meltwater.company.id": payload.companyId,
          "meltwater.social.credential_id": payload.credentialId,
          "meltwater.document.external_id": payload.document?.externalId,
          "meltwater.document.url": payload.document?.metaData?.url,
        });
        await WarpzoneModel.createPayload(payload);

        logger.info("Successfully stored warpzone payload", {
          "meltwater.document.id": payload.document?.documentId,
          "user.id": payload.applicationUserId,
          "meltwater.company.id": payload.companyId,
          "meltwater.social.credential_id": payload.credentialId,
          "meltwater.document.external_id": payload.document?.externalId,
          "meltwater.document.url": payload.document?.metaData?.url,
        });
      } else {
        throw new Error("Invalid payload format");
      }
    } catch (error) {
      logger.error("Error processing warpzone message", {
        error,
        "internal.engage.ads.payload": JSON.stringify(event),
      });
    }
  }

  private isWarpzoneEvent(payload: unknown): payload is WarpzoneEvent {
    if (typeof payload !== "object" || payload === null) {
      return false;
    }

    const event = payload as Partial<WarpzoneEvent>;
    return (
      typeof event.eventId === "string" &&
      typeof event.eventType === "string" &&
      typeof event.source === "string" &&
      typeof event.payloadType === "string" &&
      typeof event.payloadVersion === "number" &&
      typeof event.publisher === "string" &&
      typeof event.payload === "object" &&
      event.payload !== null &&
      Array.isArray(event.payloadEncoding) &&
      typeof event.version === "number" &&
      typeof event.publishedAt === "number"
    );
  }

  private normalizeDates(payload: WarpzonePayload): WarpzonePayload {
    if (payload?.document?.body?.publishDate?.date) {
      const date = payload.document.body.publishDate.date;
      payload.document.body.publishDate.date = toTimestampInMilliseconds(date);
    }
    return payload;
  }
}

const warpzoneProcessor = new EngageAdsWarpzoneProcessor();
export const handler = (event: SQSEvent, context: Context) => warpzoneProcessor.handler(event, context);
