import "dotenv/config";
import { handler } from "../src";
import type { Context, SQSEvent } from "aws-lambda";

const eventInformation = {
  eventId: "1738076628101_3fc2c9e2-afb7-4a48-b497-76584336c6ec",
  eventType: "update",
  source: "conversations",
  payloadType: "process-post-facebook",
  payloadVersion: 1,
  publisher: "conversations.process-post",
  payload: {
    from: "conversations-precog",
    applicationUserId: "5ab5080dd188f99628b26eb0",
    companyId: "59d6a021b59ce51f1de3368e",
    channel: "facebook",
    document: {
      id: "fb_59d6a021b59ce51f1de3368e_101304255870028_604408998999960",
      body: {
        publishDate: {
          date: 1738076423000,
        },
        content: {
          text: "We’re on Bluesky now! Follow us there too and, if you’re in the Worcester area, come see us at Off The Rails on 3/22 for Emo Night Worcester!",
          textLength: 141,
        },
        contentTags: [],
        emojis: [],
      },
      metaData: {
        fetchingTime: 1738076480000,
        url: "https://www.facebook.com/415760674531461/posts/604408998999960",
        source: {
          id: "101304255870028",
          location: {
            countryCode: "zz",
          },
          informationType: "social",
          socialOriginType: "facebook",
        },
        provider: {
          type: "socialconnections",
        },
        mainAuthor: {
          externalId: "id:facebook.com:101304255870028",
        },
        authors: [
          {
            authorInfo: {
              rawName: "A Blockbuster Summer",
              externalId: "id:facebook.com:101304255870028",
            },
          },
        ],
        mediaType: "sc",
        discussionType: "og",
        applicationTags: [
          "connectionsCredential=2259",
          "connectionsPollId=baaff840-dd88-11ef-8c54-d38455f08ddd",
          "isHidden=false",
          "userLikes=false",
        ],
        userTags: [],
        analyzedUrl: "www.facebook.com/415760674531461/posts/604408998999960",
        indexingTime: 1738076532655,
        randomSampling: 12953,
        audienceRestrictions: {
          cities: [],
          region: [],
        },
        receivedTime: 1738076518402,
        domains: ["facebook.com"],
      },
      enrichments: {
        sentiment: {
          discrete: "n",
        },
        socialScores: {
          fb_post_comments: 0,
          fb_post_reactions: 0,
          fb_post_shares: 0,
        },
        languageCode: "en",
        charikarLSH: "86JhZYvo9bJU48bY7hXdRoJ1MIG2GiKJN7dvKGc718SEHQKPGZpeXpxPDV2XDQzc",
        nsfw: false,
        namedEntities: [
          {
            name: "worcester",
            type: "location",
            sentiment: {
              discrete: "n",
            },
          },
          {
            canonicalName: "Bluesky",
            name: "bluesky",
            type: "organization",
            sentiment: {
              discrete: "n",
            },
            knowledgeGraphId: "55f66a89-b990-58aa-905e-b3212cbbe04e",
          },
          {
            name: "off the rails",
            type: "location",
            sentiment: {
              discrete: "n",
            },
          },
        ],
        topLevelEntitySentiment: {
          n: ["55f66a89-b990-58aa-905e-b3212cbbe04e"],
          p: [],
          u: [],
          v: [],
        },
        emotions: [],
        topics: [
          {
            level1: "arts_and_entertainment",
            level2: "music_and_audio",
            level3: "rock_music",
          },
          {
            level1: "arts_and_entertainment",
            level2: "events_and_listings",
            level3: "bars_clubs_and_nightlife",
          },
        ],
        conceptsTop: [
          {
            conceptName: "Emo",
          },
        ],
        categories: [
          {
            label: "Music",
          },
        ],
        promotional: false,
      },
      externalId: "id:facebook.com:101304255870028_604408998999960",
      systemData: {
        policies: {
          storage: {
            privateTo: ["59d6a021b59ce51f1de3368e"],
          },
        },
      },
      documentId: "fb_59d6a021b59ce51f1de3368e_101304255870028_604408998999960",
    },
    credentialId: 2259,
  },
  payloadEncoding: ["identity"],
  version: 2,
  publishedAt: 1738076628101,
};

async function localTest() {
  const mockEvent: SQSEvent = {
    Records: [
      {
        messageId: "1234",
        receiptHandle: "abc123",
        body: JSON.stringify(eventInformation),
        attributes: {
          ApproximateFirstReceiveTimestamp: "*************",
          ApproximateReceiveCount: "1",
          SenderId: "************",
          SentTimestamp: "*************",
        },
        messageAttributes: {},
        md5OfBody: "",
        eventSource: "aws:sqs",
        eventSourceARN: "arn:aws:sqs:region:account-id:queue-name",
        awsRegion: "us-east-1",
      },
    ],
  };

  const mockContext: Context = {
    callbackWaitsForEmptyEventLoop: false,
    functionName: "functionName",
    functionVersion: "functionVersion",
    invokedFunctionArn: "invokedFunctionArn",
    memoryLimitInMB: "128",
    awsRequestId: "awsRequestId:local",
    logGroupName: "logGroupName",
    logStreamName: "logStreamName",
    identity: undefined,
    clientContext: undefined,
    getRemainingTimeInMillis: () => 0,
    done: () => {},
    fail: () => {},
    succeed: () => {},
  };

  try {
    await handler(mockEvent, mockContext);
    console.log("Lambda function executed successfully.");
  } catch (error) {
    console.error("Error executing lambda function:", error);
  }
}

localTest();
