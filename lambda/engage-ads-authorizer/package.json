{"name": "engage-ads-authorizer", "description": "Gateway Authorizer for Engage Ads", "version": "1.2.2", "author": "Team Area51 <<EMAIL>>", "devDependencies": {"@types/aws-lambda": "^8.10.135", "esbuild": "^0.20.1"}, "files": ["dist"], "license": "MIT", "main": "dist/index.js", "private": true, "scripts": {"build": "tsc", "build:package": "node ../../build.js", "publish:ecr": "bash ../../scripts/publish-to-ecr.sh authorizer-lambda $(grep '\"version\":' package.json | awk -F '\"' '{print $4}')", "deploy:lambda": "bash ../../scripts/deploy-lambda.sh authorizer authorizer-lambda $(grep '\"version\":' package.json | awk -F '\"' '{print $4}')", "clean": "rimraf dist build tsconfig.tsbuildinfo", "local:lambda": "npx ts-node-dev --respawn --transpile-only --no-notify src/local/local.ts"}, "types": "dist/index.d.ts", "volta": {"node": "20.11.1"}, "dependencies": {"@meltwater/lambda-monitoring": "^0.2.5", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.1.0"}}