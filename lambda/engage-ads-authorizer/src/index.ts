import "dotenv/config";
import * as jwt from "jsonwebtoken";
import jwksClient from "jwks-rsa";
import type { APIGatewayTokenAuthorizerEvent } from "aws-lambda";

import { LambdaLogger } from "@meltwater/lambda-monitoring";

const logger = LambdaLogger.getInstance();

interface Credentials {
  company: {
    _id: string;
  };
  user: {
    _id: string;
    email: string;
    isInternal: boolean;
    timezone: string;
  };
}

interface PolicyDocument {
  Version: string;
  Statement: Array<{
    Effect: string;
    Action: string[];
    Resource: string[];
  }>;
}

interface AuthPolicy {
  principalId: string;
  policyDocument: PolicyDocument;
  context: {
    userId: string;
    isInternal: boolean;
    timezone: string;
    companyId: string;
    userEmail: string;
  };
}

const GYDA_KID = process.env.GYDA_KID ?? "";

const GYDA_JWKS_LOCATION = process.env.GYDA_JWKS_LOCATION as string;
if (!GYDA_JWKS_LOCATION) {
  throw new Error("GYDA_JWKS_LOCATION is not defined in environment variables");
}

const client = jwksClient({
  jwksUri: GYDA_JWKS_LOCATION,
  cacheMaxAge: 3600000, // 1 hour in milliseconds
  cache: true,
});

async function getKey(header: jwt.JwtHeader, callback: jwt.SigningKeyCallback): Promise<void> {
  client.getSigningKey(header.kid || GYDA_KID, (err, key) => {
    if (err) {
      logger.error(`Error getting signing key`, {
        error: err,
        "meltwater.auth.kid": header.kid,
      });

      callback(err);
    } else {
      if (key == null) {
        logger.error(`No signing key found`, {
          "meltwater.auth.kid": header.kid,
        });
        callback(new Error(`No signing key found for ${header.kid}`));
      } else callback(null, key.getPublicKey());
    }
  });
}

async function generatePolicy(authToken: string, methodArn: string): Promise<AuthPolicy> {
  try {
    const decodedToken = await new Promise<Credentials>((resolve, reject) => {
      jwt.verify(authToken, getKey, (err, decoded) => {
        if (err) {
          logger.error("Error verifying token", { err });
          reject(err);
        } else {
          resolve(decoded as Credentials);
        }
      });
    });

    logger.info("Authorization credentials", {
      "user.id": decodedToken.user._id,
      "meltwater.company.id": decodedToken.company._id,
    });

    return {
      principalId: `${decodedToken.company._id}:${decodedToken.user._id}:${decodedToken.user.email}`,
      policyDocument: {
        Version: "2012-10-17",
        Statement: [
          {
            Effect: "Allow",
            Action: ["execute-api:Invoke"],
            Resource: [methodArn],
          },
        ],
      },
      context: {
        userId: decodedToken.user._id,
        isInternal: decodedToken.user.isInternal,
        timezone: decodedToken.user.timezone,
        companyId: decodedToken.company._id,
        userEmail: decodedToken.user.email,
      },
    };
  } catch (e) {
    logger.error("Error generating policy", { err: e });
    throw e;
  }
}

export const handler = async (event: APIGatewayTokenAuthorizerEvent): Promise<AuthPolicy> => {
  try {
    logger.appendPersistentKeys({
      service: {
        name: "authorizer",
      },
      "@timestamp": new Date().toISOString(),
      "@system": "engage-ads",
      "@service": "authorizer",
    });

    if (!event.authorizationToken || !event.methodArn) {
      throw new Error("Invalid event structure");
    }

    const policyDocument = await generatePolicy(event.authorizationToken, event.methodArn);
    logger.info("Policy document generated", {
      "internal.engage.ads.policy": JSON.stringify(policyDocument),
      "user.id": policyDocument.context.userId,
      "meltwater.company.id": policyDocument.context.companyId,
    });
    return policyDocument;
  } catch (e) {
    logger.error("Error in Lambda handler", { err: e });
    throw new Error("Unauthorized");
  }
};
