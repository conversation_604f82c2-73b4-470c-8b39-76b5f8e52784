const { build } = require("esbuild");
const { openTelemetryPlugin } = require("@coralogix/esbuild-plugin-node");

build({
  entryPoints: ["src/index.ts"], // Replace with your actual entry point
  bundle: true,
  platform: "node",
  target: "node18",
  outfile: "./build/index.js",
  minify: true,
  sourcemap: true,
  plugins: [
    // OpenTelemetry plugin for build-time instrumentation
    openTelemetryPlugin({
      instrumentationConfig: {
        "@opentelemetry/instrumentation-aws-sdk": {
          suppressInternalInstrumentation: true,
        },
        "@opentelemetry/instrumentation-mongoose": {
          enabled: false,
        },
        "@opentelemetry/instrumentation-mongodb": {
          enhancedDatabaseReporting: true,
        },
        "@opentelemetry/instrumentation-winston": {
          logHook: (span, record) => {
            const traceId = record.trace_id;
            const spanId = record.span_id;
            const traceFlags = record.trace_flags;

            record["traceId"] = traceId;
            record["spanId"] = spanId;
            record["traceFlags"] = traceFlags;

            delete record.trace_id;
            delete record.span_id;
            delete record.trace_flags;
          },
        },
      },
    }),
  ],
})
  .then(() => {
    console.log("Build successful");
  })
  .catch((error) => {
    console.error("Build failed:", error);
    process.exit(1);
  });
