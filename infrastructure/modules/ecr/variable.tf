variable "region" {
  type        = string
  description = "The AWS region"
}

variable "staging_aws_account_id" {
  type = string
}

variable "production_aws_account_id" {
  type = string
}

variable "name" {
  type        = string
  description = "Name of the ECR repository"
}

variable "function_name" {
  type        = string
  description = "The name of the Lambda function"
  default     = null
}

variable "scan_on_push" {
  type        = bool
  default     = true
  description = "Indicates whether images are scanned after being pushed to the repository"
}

variable "enable_ecs" {
  type        = bool
  default     = false
  description = "Set to true to grant ECS access to the ECR repository"
}

variable "ecs_principals" {
  type        = list(string)
  default     = []
  description = "List of AWS principals (e.g. roles) for ECS tasks that should have ECR pull access"
}
