data "aws_iam_policy_document" "ecr_policy" {
  statement {
    sid    = "defaultPolicy"
    effect = "Allow"
    principals {
      type        = "AWS"
      identifiers = [
        "arn:aws:iam::${var.staging_aws_account_id}:root",
        "arn:aws:iam::${var.production_aws_account_id}:root",
      ]
    }
    actions = [
      "ecr:GetAuthorizationToken",
      "ecr:GetDownloadUrlForLayer",
      "ecr:BatchGetImage",
      "ecr:BatchCheckLayerAvailability",
    ]
  }

  statement {
    sid    = "StagingECRRobotAccess"
    effect = "Allow"
    principals {
      type        = "AWS"
      identifiers = [
        "arn:aws:iam::${var.staging_aws_account_id}:root"
      ]
    }
    actions = [
      "ecr:BatchCheckLayerAvailability",
      "ecr:BatchGetImage",
      "ecr:CompleteLayerUpload",
      "ecr:GetDownloadUrlForLayer",
      "ecr:InitiateLayerUpload",
      "ecr:PutImage",
      "ecr:UploadLayerPart",
      "ecr:DescribeRepositories",
      "ecr:GetRepositoryPolicy",
      "ecr:ListImages"
    ]
  }

  dynamic "statement" {
    for_each = var.function_name != null ? { "lambda_statement" = var.function_name } : {}
    content {
      sid    = "LambdaAccess"
      effect = "Allow"
      principals {
        type        = "Service"
        identifiers = ["lambda.amazonaws.com"]
      }
      actions = [
        "ecr:GetDownloadUrlForLayer",
        "ecr:BatchGetImage",
        "ecr:BatchCheckLayerAvailability"
      ]
      condition {
        test     = "ArnLike"
        variable = "aws:SourceArn"
        values = [
          "arn:aws:lambda:${var.region}:${var.staging_aws_account_id}:function:${var.function_name}",
          "arn:aws:lambda:${var.region}:${var.production_aws_account_id}:function:*"
        ]
      }
    }
  }

  dynamic "statement" {
    for_each = var.enable_ecs && length(var.ecs_principals) > 0 ? { "ecs_statement" = true } : {}
    content {
      sid    = "ECSAccess"
      effect = "Allow"
      principals {
        type        = "AWS"
        identifiers = var.ecs_principals
      }
      actions = [
        "ecr:GetAuthorizationToken",
        "ecr:GetDownloadUrlForLayer",
        "ecr:BatchGetImage",
        "ecr:BatchCheckLayerAvailability"
      ]
    }
  }
}
