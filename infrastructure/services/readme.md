# MongoDB Atlas Cluster and Database Creation

This Terraform configuration file (`database.tf`) automates the creation of a MongoDB Atlas cluster, database, user, and related resources using the MongoDB Atlas Terraform provider.

## Prerequisites

Before running this Terraform configuration, ensure that you have the following:

- Terraform installed on your machine
- MongoDB Atlas account with appropriate permissions
- MongoDB Atlas API keys (public and private keys)

## Configuration

The `database.tf` file creates the following resources:

- MongoDB Atlas cluster with autoscaling enabled (compute and memory autoscaling)
- Private link endpoint for the MongoDB Atlas cluster
- VPC endpoint in AWS for secure connectivity
- Private endpoint service to associate the MongoDB Atlas private link with the AWS VPC
- Database user with specified roles and permissions

You can customize the configuration by modifying the following parameters in the `database.tf` file:

- `project_id`: The ID of your MongoDB Atlas project.
- `cluster_name`: The name of the cluster to be created.
- `region_name`: The AWS region where the cluster will be deployed.
- `vpc_id`: The ID of the AWS VPC where the VPC endpoint will be created.
- `subnet_ids`: The subnet IDs in the AWS VPC for the VPC endpoint.
- `security_group_ids`: The security group IDs for the VPC endpoint.
- `username`: The username for the database user.
- `database_name`: The name of the database to be created.

Make sure to update these parameters according to your requirements.

## Usage

To run this Terraform configuration, follow these steps:

1. Clone the repository or navigate to the directory containing the `database.tf` file.

2. Open a terminal and change to the `infrastructure/services` directory:

3. Initialize the Terraform working directory: terraform init

4. Set the required variables using one of the following methods:

- Environment variables:
  ```
  export TF_VAR_project_id="your-project-id"
  export TF_VAR_mongodbatlas_public_key="your-public-key"
  export TF_VAR_mongodbatlas_private_key="your-private-key"
  export TF_VAR_database_user_password="your-database-user-password"
  ```
- Terraform variables file (`terraform.tfvars`):
  Create a file named `terraform.tfvars` in the same directory as `database.tf` and set the variable values:
  ```
  project_id = "your-project-id"
  mongodbatlas_public_key = "your-public-key"
  mongodbatlas_private_key = "your-private-key"
  database_user_password = "your-database-user-password"
  ```
- Inline variables:
  Utilize the variable definitions in the `database.tf` file and set the values directly:
  ```hcl
  variable "project_id" {
    default = "your-project-id"
  }
  variable "mongodbatlas_public_key" {
    default = "your-public-key"
  }
  variable "mongodbatlas_private_key" {
    default = "your-private-key"
  }
  variable "database_user_password" {
    default = "your-database-user-password"
  }
  ```

5. Preview the changes to be applied: terraform plan

6. Apply the changes to create the resources: terraform apply
   Review the proposed changes and enter "yes" when prompted to confirm the creation of resources.

7. Once the resources are created successfully, Terraform will display the output with the relevant information, including the connection string for the MongoDB Atlas cluster.

## Cleanup

To destroy the resources created by this Terraform configuration, run the following command: terraform destroy

Review the proposed changes and enter "yes" when prompted to confirm the destruction of resources.

Note: Be cautious when destroying resources, as it will permanently delete the cluster, database, user, VPC endpoint, and related resources created by this configuration.
