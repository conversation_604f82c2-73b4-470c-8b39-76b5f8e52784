data "aws_iam_policy_document" "warpzone_processor_key_policy_document" {
  version = "2012-10-17"

  statement {
    sid    = "Enable IAM User Permissions"
    effect = "Allow"
    principals {
      type        = "AWS"
      identifiers = [
        "arn:aws:iam::${var.staging_aws_account_id}:root",
        "arn:aws:iam::${var.production_aws_account_id}:root"
      ]
    }
    actions   = ["kms:*"]
    resources = ["*"]
  }

  statement {
    sid    = "Allow AWS Services"
    effect = "Allow"
    principals {
      type = "Service"
      identifiers = ["sns.amazonaws.com"]
    }
    actions   = ["kms:Decrypt", "kms:GenerateDataKey"]
    resources = ["*"]
  }

  statement {
    sid    = "Allow Lambda Service"
    effect = "Allow"
    principals {
      type = "Service"
      identifiers = ["lambda.amazonaws.com"]
    }
    actions   = ["kms:Decrypt", "kms:GenerateDataKey"]
    resources = ["*"]
  }

  statement {
    sid    = "Allow Lambda Role"
    effect = "Allow"
    principals {
      type        = "AWS"
      identifiers = [module.this_lambda.lambda_role_arn]
    }
    actions   = ["kms:Decrypt", "kms:GenerateDataKey"]
    resources = ["*"]
  }
}

data "aws_iam_policy_document" "warpzone_sns_sqs_policy" {
  statement {
    effect    = "Allow"
    actions   = ["sqs:SendMessage"]
    sid       = "AllowSNSPublishWarpzoneProcessor"
    principals {
      type        = "Service"
      identifiers = ["sns.amazonaws.com"]
    }
    resources = [aws_sqs_queue.warpzone_processor_queue.arn]
    condition {
      test     = "ArnLike"
      variable = "aws:SourceArn"
      values   = [var.warp_zone_sns_arn]
    }
  }
}
