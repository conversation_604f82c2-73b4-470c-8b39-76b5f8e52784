locals {
  engage_ads_lambda_name = "${local.resource_prefix}engage-ads-lambda"
}

data "aws_ecr_repository" "engage_ads_repo" {
  name = "engage-ads"
}

module "engage_ads_lambda" {
  source  = "terraform-aws-modules/lambda/aws"
  version = "7.4.0"

  function_name  = local.engage_ads_lambda_name
  description    = local.is_feature_branch ? "Engage Ads Lambda for feature branch ${terraform.workspace}" : "Engage Ads Lambda"
  create_package = false

  image_uri    = "${data.aws_ecr_repository.engage_ads_repo.repository_url}:${local.is_feature_branch ? "engage-ads-graphql-${local.image_tag_base}" : "latest"}"
  package_type = "Image"
  create_role  = true

  memory_size   = var.memory_size
  timeout       = var.timeout
  publish       = true
  runtime       = "nodejs20.x"
  handler       = "index.handler"
  tracing_mode  = "Active"
  architectures = ["arm64"]

  vpc_subnet_ids         = var.private_subnet_ids
  vpc_security_group_ids = var.security_group_id

  environment_variables = merge(
    {
      WC_SOCIAL_DATA_API_Key     = var.WC_SOCIAL_DATA_API_Key
      WILDCARDS_STAGING_BASE_URL = var.WILDCARDS_STAGING_BASE_URL
      SECTION31_STAGING_BASE_URL = var.SECTION31_STAGING_BASE_URL
      CX_DOMAIN                  = var.coralogix_domain
      CX_SECRET                  = var.coralogix_secret
      CX_TRACING_MODE            = var.coralogix_tracing_mode
      CX_TAGS_ENABLED            = var.coralogix_tags_enabled
      CX_APPLICATION             = var.coralogix_application
      CX_SUB_SYSTEM              = var.coralogix_sub_system
      CX_REPORTING_STRATEGY      = var.coralogix_reporting_strategy
      MONGODB_URI                = var.mongodb_uri
      APOLLO_KEY                 = "service:Engage-Ads-jegtw:APNonN11lv_Zub8IHnhrsA"
      APOLLO_GRAPH_REF           = "Engage-Ads-jegtw@current"
      APOLLO_SCHEMA_REPORTING    = false
      GRAPHQL_S31_KEY            = var.graphql_s31_key
      GRAPHQL_S31_URL            = var.graphql_s31_url
      ENTITLEMENTS_API_URL       = var.entitlements_api_url
      IDENTITY_SERVICES_URL      = var.identity_services_url
      SOCIAL_PUBLISHER_API_URL   = var.social_publisher_api_url
      LINKEDIN_VERSION           = var.linkedin_version
    },
    local.is_feature_branch ? { FEATURE_BRANCH = terraform.workspace } : {}
  )

  attach_policies    = true
  number_of_policies = 2
  policies = [
    "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole",
    "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly",
    "arn:aws:iam::aws:policy/AWSXRayDaemonWriteAccess",
  ]

  attach_policy_json = true
  policy_json = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "secretsmanager:GetSecretValue",
          "lambda:GetFunction"
        ]
        Resource = "*"
      }
    ]
  })

}

// Main API Gateway (only created for core deployment)
resource "aws_apigatewayv2_api" "engage_ads_api_main" {
  count         = local.is_feature_branch ? 0 : 1
  name          = "${local.resource_prefix}engage-ads-api-main"
  protocol_type = "HTTP"

  tags = {
    LambdaFunction = local.engage_ads_lambda_name
  }
}

resource "aws_apigatewayv2_stage" "engage_ads_stage_main" {
  count       = local.is_feature_branch ? 0 : 1
  api_id      = aws_apigatewayv2_api.engage_ads_api_main[0].id
  name        = "$default"
  auto_deploy = true
}

resource "aws_apigatewayv2_integration" "engage_ads_lambda_main" {
  count                  = local.is_feature_branch ? 0 : 1
  api_id                 = aws_apigatewayv2_api.engage_ads_api_main[0].id
  integration_type       = "AWS_PROXY"
  integration_uri        = module.engage_ads_lambda.lambda_function_invoke_arn
  integration_method     = "POST"
  payload_format_version = "2.0"
}

resource "aws_apigatewayv2_authorizer" "engage_ads_authorizer_main" {
  count                            = local.is_feature_branch ? 0 : 1
  api_id                            = aws_apigatewayv2_api.engage_ads_api_main[0].id
  authorizer_type                   = "REQUEST"
  authorizer_uri                    = module.authorizer_lambda.lambda_function_invoke_arn
  identity_sources                  = ["$request.header.Authorization"]
  name                              = "${local.resource_prefix}engage-ads-authorizer-main"
  authorizer_payload_format_version = "1.0"
  authorizer_result_ttl_in_seconds  = 0
  authorizer_credentials_arn        = aws_iam_role.api_gateway_authorizer_role_main[0].arn
}

// POST /graphql route with authorizer
resource "aws_apigatewayv2_route" "engage_ads_lambda_route_post_main" {
  count             = local.is_feature_branch ? 0 : 1
  api_id             = aws_apigatewayv2_api.engage_ads_api_main[0].id
  route_key          = "POST /graphql"
  target             = "integrations/${aws_apigatewayv2_integration.engage_ads_lambda_main[0].id}"
  authorizer_id      = aws_apigatewayv2_authorizer.engage_ads_authorizer_main[0].id
  authorization_type = "CUSTOM"
}

resource "aws_apigatewayv2_route" "engage_ads_lambda_route_get_main" {
  count     = local.is_feature_branch ? 0 : 1
  api_id    = aws_apigatewayv2_api.engage_ads_api_main[0].id
  route_key = "GET /graphql"
  target    = "integrations/${aws_apigatewayv2_integration.engage_ads_lambda_main[0].id}"
}

// OPTIONS /{proxy+} route for preflight
resource "aws_apigatewayv2_route" "engage_ads_lambda_route_options_main" {
  count     = local.is_feature_branch ? 0 : 1
  api_id    = aws_apigatewayv2_api.engage_ads_api_main[0].id
  route_key = "OPTIONS /{proxy+}"
  target    = "integrations/${aws_apigatewayv2_integration.engage_ads_lambda_main[0].id}"
}

// Root mapping for custom domain
resource "aws_apigatewayv2_api_mapping" "engage_ads_mapping_main" {
  count           = local.is_feature_branch ? 0 : 1
  api_id          = aws_apigatewayv2_api.engage_ads_api_main[0].id
  domain_name     = var.api_domain_name
  stage           = aws_apigatewayv2_stage.engage_ads_stage_main[0].name
}

// Lambda permissions for main API Gateway
resource "aws_lambda_permission" "api_gw_main" {
  count         = local.is_feature_branch ? 0 : 1
  statement_id  = "AllowAPIGatewayInvokeMain"
  action        = "lambda:InvokeFunction"
  function_name = module.engage_ads_lambda.lambda_function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_apigatewayv2_api.engage_ads_api_main[0].execution_arn}/*/*"
}

resource "aws_lambda_permission" "api_gw_authorizer_main" {
  count         = local.is_feature_branch ? 0 : 1
  statement_id  = "AllowAPIGatewayInvokeAuthorizerMain"
  action        = "lambda:InvokeFunction"
  function_name = module.authorizer_lambda.lambda_function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_apigatewayv2_api.engage_ads_api_main[0].execution_arn}/authorizers/${aws_apigatewayv2_authorizer.engage_ads_authorizer_main[0].id}"
}

resource "aws_iam_role" "api_gateway_authorizer_role_main" {
  count = local.is_feature_branch ? 0 : 1
  name  = "${substr(local.resource_prefix, 0, 20)}ar-main"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "apigateway.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy" "api_gateway_authorizer_policy_main" {
  count = local.is_feature_branch ? 0 : 1
  name  = "${substr(local.resource_prefix, 0, 20)}ap-main"
  role  = aws_iam_role.api_gateway_authorizer_role_main[0].id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect   = "Allow"
        Action   = "lambda:InvokeFunction"
        Resource = module.authorizer_lambda.lambda_function_arn
      }
    ]
  })
}

resource "aws_apigatewayv2_api" "engage_ads_api" {
  count         = local.is_feature_branch ? 1 : 0
  name          = "${local.resource_prefix}engage-ads-api"
  protocol_type = "HTTP"

  tags = {
    LambdaFunction = local.engage_ads_lambda_name
  }
}

resource "aws_apigatewayv2_stage" "engage_ads_stage" {
  count       = local.is_feature_branch ? 1 : 0
  api_id      = aws_apigatewayv2_api.engage_ads_api[0].id
  name        = "$default"
  auto_deploy = true
}
resource "aws_apigatewayv2_integration" "engage_ads_lambda" {
  count                  = local.is_feature_branch ? 1 : 0
  api_id                 = aws_apigatewayv2_api.engage_ads_api[0].id
  integration_type       = "AWS_PROXY"
  integration_uri        = module.engage_ads_lambda.lambda_function_invoke_arn
  integration_method     = "POST"
  payload_format_version = "2.0"

  depends_on = [
    module.engage_ads_lambda,
    aws_lambda_permission.api_gw
  ]
}

resource "aws_apigatewayv2_authorizer" "engage_ads_authorizer" {
  count                             = local.is_feature_branch ? 1 : 0
  api_id                            = aws_apigatewayv2_api.engage_ads_api[0].id
  authorizer_type                   = "REQUEST"
  authorizer_uri                    = module.authorizer_lambda.lambda_function_invoke_arn
  identity_sources                  = ["$request.header.Authorization"]
  name                              = "${local.resource_prefix}engage-ads-authorizer"
  authorizer_payload_format_version = "1.0"
  authorizer_result_ttl_in_seconds  = 0
  authorizer_credentials_arn        = aws_iam_role.api_gateway_authorizer_role[0].arn

  depends_on = [aws_iam_role_policy.api_gateway_authorizer_policy]
}
# POST /graphql route with authorizer
resource "aws_apigatewayv2_route" "engage_ads_lambda_route_post" {
  count              = local.is_feature_branch ? 1 : 0
  api_id             = aws_apigatewayv2_api.engage_ads_api[0].id
  route_key          = "POST /graphql"
  target             = "integrations/${aws_apigatewayv2_integration.engage_ads_lambda[0].id}"
  authorizer_id      = aws_apigatewayv2_authorizer.engage_ads_authorizer[0].id
  authorization_type = "CUSTOM"

  depends_on = [
    aws_apigatewayv2_integration.engage_ads_lambda,
    aws_apigatewayv2_authorizer.engage_ads_authorizer,
    aws_lambda_permission.api_gw_authorizer
  ]
}

resource "aws_apigatewayv2_route" "engage_ads_lambda_route_get" {
  count     = local.is_feature_branch ? 1 : 0
  api_id    = aws_apigatewayv2_api.engage_ads_api[0].id
  route_key = "GET /graphql"
  target    = "integrations/${aws_apigatewayv2_integration.engage_ads_lambda[0].id}"

  depends_on = [
    aws_apigatewayv2_integration.engage_ads_lambda
  ]
}


# OPTIONS /{proxy+} route for preflight
resource "aws_apigatewayv2_route" "engage_ads_lambda_route_options" {
  count     = local.is_feature_branch ? 1 : 0
  api_id    = aws_apigatewayv2_api.engage_ads_api[0].id
  route_key = "OPTIONS /{proxy+}"
  target    = "integrations/${aws_apigatewayv2_integration.engage_ads_lambda[0].id}"
}
resource "aws_apigatewayv2_api_mapping" "engage_ads_mapping" {
  count           = local.is_feature_branch ? 1 : 0
  api_id          = aws_apigatewayv2_api.engage_ads_api[0].id
  domain_name     = var.api_domain_name
  stage           = aws_apigatewayv2_stage.engage_ads_stage[0].name
  api_mapping_key = "feature/${terraform.workspace}"

  depends_on = [aws_apigatewayv2_stage.engage_ads_stage]
}

resource "aws_lambda_permission" "api_gw" {
  count         = local.is_feature_branch ? 1 : 0
  statement_id  = "AllowAPIGatewayInvoke"
  action        = "lambda:InvokeFunction"
  function_name = module.engage_ads_lambda.lambda_function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_apigatewayv2_api.engage_ads_api[0].execution_arn}/*/*"
}

resource "aws_lambda_permission" "api_gw_authorizer" {
  count         = local.is_feature_branch ? 1 : 0
  statement_id  = "AllowAPIGatewayInvokeAuthorizer"
  action        = "lambda:InvokeFunction"
  function_name = module.authorizer_lambda.lambda_function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_apigatewayv2_api.engage_ads_api[0].execution_arn}/*/*"
}
resource "aws_iam_role" "api_gateway_authorizer_role" {
  count = local.is_feature_branch ? 1 : 0
  name  = "${substr(local.resource_prefix, 0, 20)}ar-${substr(terraform.workspace, 0, 20)}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "apigateway.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy" "api_gateway_authorizer_policy" {
  count = local.is_feature_branch ? 1 : 0
  name  = "${substr(local.resource_prefix, 0, 20)}ap-${substr(terraform.workspace, 0, 20)}"
  role  = aws_iam_role.api_gateway_authorizer_role[0].id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect   = "Allow"
        Action   = "lambda:InvokeFunction"
        Resource = module.authorizer_lambda.lambda_function_arn
      }
    ]
  })
}

output "engage_ads_lambda_function_name" {
  description = "Name of the Engage Ads Lambda function"
  value       = module.engage_ads_lambda.lambda_function_name
}

output "engage_ads_api_id" {
  description = "ID of the Engage Ads API Gateway"
  value       = local.is_feature_branch ? aws_apigatewayv2_api.engage_ads_api[0].id : null
}

output "engage_ads_api_endpoint" {
  description = "URL of the Engage Ads API Gateway endpoint"
  value       = local.is_feature_branch ? aws_apigatewayv2_stage.engage_ads_stage[0].invoke_url : null
}

output "engage_ads_api_id_main" {
  description = "ID of the main Engage Ads API Gateway"
  value       = local.is_feature_branch ? null : aws_apigatewayv2_api.engage_ads_api_main[0].id
}

output "engage_ads_api_endpoint_main" {
  description = "URL of the main Engage Ads API Gateway endpoint"
  value       = local.is_feature_branch ? null : aws_apigatewayv2_stage.engage_ads_stage_main[0].invoke_url
}

