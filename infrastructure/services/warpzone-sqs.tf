resource "aws_kms_key" "warpzone_processor_key" {
  description = "Engage Ads Warpzone Processor - KMS Key for WarpZone message Encryption"
  policy      = data.aws_iam_policy_document.warpzone_processor_key_policy_document.json
}

resource "aws_kms_alias" "warpzone_processor_key_alias" {
  name          = "alias/kms/engage-ads-warpzone-processor-key"
  target_key_id = aws_kms_key.warpzone_processor_key.id
}

resource "aws_sqs_queue" "warpzone_processor_dead_letter_queue" {
  name             = "${local.resource_prefix}warpzone-processor-deadletter"
  delay_seconds    = 0
  max_message_size = 262144

  kms_master_key_id = aws_kms_alias.warpzone_processor_key_alias.name

  tags = {
    Environment = terraform.workspace
  }
}

resource "aws_sqs_queue" "warpzone_processor_queue" {
  name                       = "${local.resource_prefix}warpzone-processor-queue"
  delay_seconds              = 0
  max_message_size           = 262144
  receive_wait_time_seconds  = 10
  visibility_timeout_seconds = 1200
  redrive_policy             = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.warpzone_processor_dead_letter_queue.arn
    maxReceiveCount     = 1
  })

  kms_master_key_id = aws_kms_alias.warpzone_processor_key_alias.name

  tags = {
    Environment = terraform.workspace
  }
}

resource "aws_sqs_queue_policy" "warpzone_processor_queue_policy" {
  queue_url = aws_sqs_queue.warpzone_processor_queue.id
  policy    = data.aws_iam_policy_document.warpzone_sns_sqs_policy.json
}

resource "aws_sns_topic_subscription" "warpzone_processor_sns_subscription" {
  topic_arn          = var.warp_zone_sns_arn
  protocol           = "sqs"
  endpoint           = aws_sqs_queue.warpzone_processor_queue.arn
  raw_message_delivery = true

  filter_policy = jsonencode({
    Version        = [{ numeric = [ "=", 2 ] }]
    PayloadType    = [ "process-post-tiktok", "process-post-facebook", "process-post-linkedin" ]
  })
}
