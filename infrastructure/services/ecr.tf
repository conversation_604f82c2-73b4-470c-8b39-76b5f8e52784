module "authorizer" {

  source = "../modules/ecr"

  name                      = "authorizer-lambda"
  function_name             = "authorizer"
  region                    = var.aws_region
  staging_aws_account_id    = var.staging_aws_account_id
  production_aws_account_id = var.production_aws_account_id
}


module "engage-ads" {
  source                    = "../modules/ecr"
  name                      = "engage-ads"
  function_name             = "engage-ads-lambda"
  region                    = var.aws_region
  staging_aws_account_id    = var.staging_aws_account_id
  production_aws_account_id = var.production_aws_account_id
}

module "warpzone-processor" {
  source                    = "../modules/ecr"
  name                      = "warpzone-processor"
  function_name             = "warpzone-processor-lambda"
  region                    = var.aws_region
  staging_aws_account_id    = var.staging_aws_account_id
  production_aws_account_id = var.production_aws_account_id
}

