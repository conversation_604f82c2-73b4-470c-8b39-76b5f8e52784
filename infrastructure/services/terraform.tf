terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    mongodbatlas = {
      source = "mongodb/mongodbatlas"
      version = "1.21.1"
    }
  }

  backend "s3" {
    bucket = "terraform-state-meltwater-engage-ads-develop"
    key    = "grimoire-commons/terraform.tfstate"
    region = "eu-west-1"
  }
}


provider "aws" {
  region = "eu-west-1"
  default_tags {
    tags = {
      MWBilling_COE        = "SCM" # Example SCM
      MWBilling_Department = 432 # Example 432
      MWBilling_Product    = "engage-ads" # Example "socialpublishing"
      MWBilling_Feature    = "default" # Example "publisher"
      MWBilling_Service    = "graphql" # Example "grimoire-commons"
      MWBilling_Team       = "area51" # Example "grimoire"
      MWBilling_Env        = "opex" # Example "opex" or "cogx"
      clientdata           = "no" # Example "no"
    }
  }
}
