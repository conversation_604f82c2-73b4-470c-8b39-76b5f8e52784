variable "aws_region" {
  default = "eu-west-1"
}

variable "mongodb_project_id" {
  description = "Your MongoDB Atlas Project ID"
  type        = string
}

variable "mongodbatlas_public_key" {
  description = "Your MongoDB Atlas Public API Key"
  type        = string
}

variable "mongodbatlas_private_key" {
  description = "Your MongoDB Atlas Private API Key"
  type        = string
}

variable "staging_aws_account_id" {
  default = "************"
}

variable "production_aws_account_id" {
  default = "************"
}

variable "env" {
  default = "dev"
}

variable "memory_size" {
  description = "The amount of memory, in MB, that is allocated to your Lambda function."
  type        = number
  default     = 512
}

variable "timeout" {
  description = "The amount of time that Lambda allows a function to run before stopping it."
  type        = number
  default     = 900
}

variable "private_subnet_ids" {
  description = "The list of private subnet IDs in which the Lambda function will be deployed."
  type        = list(string)
  default     = ["subnet-0b449b62fd2b746e0", "subnet-0782615239bd69476", "subnet-0c9adf0bcab7eef54"]
}

variable "security_group_id" {
  description = "The security group ID that is associated with the Lambda function."
  type        = list(string)
  default     = ["sg-02a92aff25354fdc9"]
}


variable "gyda_kid" {
  description = "The value for GYDA_KID environment variable"
  type        = string
}

variable "gyda_jwks_location" {
  description = "The value for GYDA_JWKS_LOCATION environment variable"
  type        = string
  default     = " https://staging.meltwater.net/.well-known/jwks.json"
}

variable "WC_SOCIAL_DATA_API_Key" {
  description = "The API key for wildcard API (staging environment)"
  type        = string
}

variable "WILDCARDS_STAGING_BASE_URL" {
  description = "Wildcards API base URL (staging environment)"
  type        = string
  default     = "https://wc-staging-v1-social-data.meltwater.io"
}

variable "SECTION31_STAGING_BASE_URL" {
  description = "Section 31 API base URL (staging environment)"
  type        = string
  default     = "https://section-31-graph.staging.section31.meltwater.io"
}

variable "coralogix_domain" {
  default = "coralogix.com"
}

variable "coralogix_secret" {
  default = "coralogix-api-key"
}

variable "coralogix_tracing_mode" {
  default = "otel"
}

variable "coralogix_tags_enabled" {
  default = true
}

variable "coralogix_application" {
  default = "engage"
}
variable "coralogix_sub_system" {
  default = "ads"
}
variable "coralogix_reporting_strategy" {
  default = "LOW_OVERHEAD"
}

variable "existing_authorizer_lambda_arn" {
  description = "The ARN of the existing Lambda authorizer function"
  type        = string
  default = "arn:aws:lambda:eu-west-1:************:function:authorizer"
}

variable "existing_domain_name" {
  description = "The existing API Gateway domain name"
  default     = "api.engage-ads.meltwater.net"
}

variable "is_feature_branch" {
  type        = bool
  default     = false
  description = "Flag to indicate if this is a feature branch deployment"
}

variable "branch_name" {
  type        = string
  default     = "main"
  description = "Name of the current branch"
}

variable "api_domain_name" {
  type        = string
  default = "api.engage-ads.meltwater.net"
  description = "Domain name for the API Gateway"
}


variable "mongodb_uri" {
  type        = string
  description = "Mongo database connection URI"
}

variable "graphql_s31_key" {
  type        = string
  description = "Section 31 graphql key"
}

variable "graphql_s31_url" {
  type        = string
  default     = "https://section-31-graph.staging.section31.meltwater.io/graphql"
  description = "Section 31 graphql endpoint"

}

variable "identity_services_url" {
  default = "https://v1.staging.identity.meltwater.io"
}

variable "entitlements_api_url" {
  default = "https://v6.staging.entitlements.meltwater.io"
}

variable "warp_zone_sns_arn" {
  default = "arn:aws:sns:eu-west-1:769375816390:WarpZonePipelineTopic"
}

variable "social_publisher_api_url" {
  description = "Base API URL for the publish API"
  type = string
  default = "https://api.dev.social-publisher.meltwater.net/graphql"
}

# LinkedIn REST API version used by Engage Ads lambda (format YYYYMM)
variable "linkedin_version" {
  type        = string
  description = "LinkedIn REST API version (YYYYMM format)"
  default     = "202504"
}
