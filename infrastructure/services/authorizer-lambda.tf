locals {
  authorizer_function_name = "${local.resource_prefix}authorizer"
}

data "aws_ecr_repository" "authorizer_repo" {
  name = "authorizer-lambda"
}

module "authorizer_lambda" {
  source         = "terraform-aws-modules/lambda/aws"
  version        = "7.4.0"
  function_name  = local.authorizer_function_name
  description    = local.is_feature_branch ? "Authorizer Lambda for feature branch ${terraform.workspace}" : "Authorizer Lambda"
  create_package = false
  create_role    = true
  image_uri      = "${data.aws_ecr_repository.authorizer_repo.repository_url}:${local.is_feature_branch ? "engage-ads-authorizer-${local.image_tag_base}" : "latest"}"
  package_type   = "Image"

  memory_size   = var.memory_size
  timeout       = var.timeout
  publish       = true
  runtime       = "nodejs20.x"
  handler       = "index.handler"
  tracing_mode  = "Active"
  architectures = ["arm64"]

  vpc_subnet_ids         = var.private_subnet_ids
  vpc_security_group_ids = var.security_group_id

  environment_variables = merge(
    {
      GYDA_KID              = var.gyda_kid
      GYDA_JWKS_LOCATION    = var.gyda_jwks_location
      CX_DOMAIN             = var.coralogix_domain
      CX_SECRET             = var.coralogix_secret
      CX_TRACING_MODE       = var.coralogix_tracing_mode
      CX_TAGS_ENABLED       = var.coralogix_tags_enabled
      CX_APPLICATION        = var.coralogix_application
      CX_SUB_SYSTEM         = var.coralogix_sub_system
      CX_REPORTING_STRATEGY = var.coralogix_reporting_strategy
    },
    local.is_feature_branch ? { FEATURE_BRANCH = terraform.workspace } : {}
  )

  attach_policies    = true
  number_of_policies = 2
  policies = [
    "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole",
    "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly",
    "arn:aws:iam::aws:policy/AWSXRayDaemonWriteAccess",
  ]

  attach_policy_json = true
  policy_json = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "secretsmanager:GetSecretValue",
          "lambda:GetFunction"
        ]
        Resource = "*"
      }
    ]
  })
}

module "authorizer_lambda_latest_alias" {
  source = "terraform-aws-modules/lambda/aws//modules/alias"

  create        = true
  refresh_alias = true

  name = "latest"

  function_name = module.authorizer_lambda.lambda_function_name
}

// Outputs
output "authorizer_lambda_function_name" {
  description = "Name of the Authorizer Lambda function"
  value       = module.authorizer_lambda.lambda_function_name
}

output "authorizer_lambda_function_arn" {
  description = "ARN of the Authorizer Lambda function"
  value       = module.authorizer_lambda.lambda_function_arn
}
