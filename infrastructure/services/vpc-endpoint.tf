variable "vpc_id" {
  description = "Your existing AWS VPC ID"
  type        = string
  default     = "vpc-0e2d76925787cc68e" 
}

# Security Group for VPC Endpoint
resource "aws_security_group" "vpc_endpoint_sg" {
  name        = "vpc_endpoint_sg"
  description = "Security group for VPC Endpoint"
  vpc_id      = var.vpc_id

  # Ingress Rules (Inbound)
  ingress {
    from_port   = 0
    to_port     = 65535
    protocol    = "tcp"
    cidr_blocks = ["**********/21"]
  }

  # Egress Rules (Outbound)
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

# Create an AWS VPC Endpoint for MongoDB Atlas
resource "aws_vpc_endpoint" "mongodb_endpoint" {
  vpc_id             = var.vpc_id
  service_name       = mongodbatlas_privatelink_endpoint.private_endpoint.endpoint_service_name
  vpc_endpoint_type  = "Interface"
  subnet_ids         = var.private_subnet_ids
  security_group_ids = [aws_security_group.vpc_endpoint_sg.id]
  private_dns_enabled = false

  depends_on = [
    aws_security_group.vpc_endpoint_sg,
    mongodbatlas_privatelink_endpoint.private_endpoint
  ]
}

# Add the AWS VPC Endpoint to the MongoDB Atlas Private Endpoint Service
resource "mongodbatlas_privatelink_endpoint_service" "private_endpoint_service" {
  project_id          = var.mongodb_project_id
  private_link_id     = mongodbatlas_privatelink_endpoint.private_endpoint.id
  endpoint_service_id = aws_vpc_endpoint.mongodb_endpoint.id
  provider_name       = "AWS"
}

# Output the VPC endpoint ID and network interface IDs for reference
output "mongodb_endpoint_id" {
  description = "The ID of the MongoDB VPC endpoint"
  value       = aws_vpc_endpoint.mongodb_endpoint.id
}

output "mongodb_endpoint_network_interfaces" {
  description = "The network interface IDs of the MongoDB VPC endpoint"
  value       = aws_vpc_endpoint.mongodb_endpoint.network_interface_ids
}

output "mongodb_endpoint_dns_entry" {
  description = "The DNS entries for the VPC endpoint"
  value       = aws_vpc_endpoint.mongodb_endpoint.dns_entry
}

# Extract the private endpoint hostnames from the cluster's connection strings
locals {
  private_endpoint_hostnames = flatten([
    for cluster in mongodbatlas_cluster.engage_ads_cluster[*] :
      [
        for pe in cluster.connection_strings[0].private_endpoint :
          split("://", pe.srv_connection_string)[1]
      ]
  ])
}
