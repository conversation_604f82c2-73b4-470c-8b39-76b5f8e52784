# Create IAM policy for ECR access
resource "aws_iam_policy" "ecr_access_policy" {
  name        = "LambdaECRAccessPolicy"
  description = "Policy for Lambda to access ECR images"
  policy      = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "ecr:GetDownloadUrlForLayer",
          "ecr:BatchGetImage",
          "ecr:BatchCheckLayerAvailability"
        ],
        Resource = "*"
      }
    ]
  })
}
