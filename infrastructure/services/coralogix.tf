module "cloudwatch_logs" {
  source = "coralogix/aws/coralogix//modules/cloudwatch-logs"
  version = "1.0.99"
  coralogix_region   = "Europe"
  secret_manager_enabled = true
  create_secret = false
  private_key = "coralogix-api-key" # Name of the secret in AWS Secrets Manager
  application_name   = "engage"
  subsystem_name     = "ads"
  log_groups         = [
    module.authorizer_lambda.lambda_cloudwatch_log_group_name,
    module.engage_ads_lambda.lambda_cloudwatch_log_group_name
  ]
}
