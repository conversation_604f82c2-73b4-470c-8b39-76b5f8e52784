variable "create_mongodb_resources" {
  description = "Whether to create MongoDB Atlas resources"
  type        = bool
  default     = true  # Set to true to create MongoDB Atlas resources
}

# Configure the MongoDB Atlas Provider
provider "mongodbatlas" {
  public_key  = var.mongodbatlas_public_key
  private_key = var.mongodbatlas_private_key
}

# Create a MongoDB Atlas Cluster
resource "mongodbatlas_cluster" "engage_ads_cluster" {
  project_id   = var.mongodb_project_id
  name         = "engage-ads-cluster"
  cluster_type = "REPLICASET"

  replication_specs {
    num_shards = 1
    regions_config {
      region_name     = "EU_WEST_1"
      electable_nodes = 3
      priority        = 7
      read_only_nodes = 0
    }
  }

  provider_name               = "AWS"
  disk_size_gb                = 10
  provider_instance_size_name = "M10"

  termination_protection_enabled = true

  cloud_backup                 = true 
  pit_enabled                 = true 

  auto_scaling_disk_gb_enabled                    = true
  auto_scaling_compute_enabled                    = true
  auto_scaling_compute_scale_down_enabled         = true
  provider_auto_scaling_compute_max_instance_size = "M20"
  provider_auto_scaling_compute_min_instance_size = "M10"
  
}

# Create a MongoDB Atlas Private Endpoint
resource "mongodbatlas_privatelink_endpoint" "private_endpoint" {
  project_id    = var.mongodb_project_id
  provider_name = "AWS"
  region        = "EU_WEST_1"
}

# Add IP whitelist for GitHub Actions
resource "mongodbatlas_project_ip_access_list" "github_actions" {
  project_id = var.mongodb_project_id
  cidr_block = "************/32"  # GitHub Actions IP
  comment    = "GitHub Actions Runner IP"
}

# Local variables for connection strings
locals {
  # Extract connection strings for use in other resources
  private_connection_strings = flatten([
    for cluster in mongodbatlas_cluster.engage_ads_cluster[*] :
      [
        for pe in cluster.connection_strings[0].private_endpoint :
          pe.srv_connection_string
      ]
  ])
}

# Outputs for reference
output "endpoint_service_name" {
  value = mongodbatlas_privatelink_endpoint.private_endpoint.endpoint_service_name
}

output "private_endpoint_id" {
  value = mongodbatlas_privatelink_endpoint.private_endpoint.id
}

output "private_connection_string" {
  value = length(local.private_connection_strings) > 0 ? local.private_connection_strings[0] : "Waiting for connection string"
}

output "debug_connection_strings" {
  value = mongodbatlas_cluster.engage_ads_cluster.connection_strings
}
