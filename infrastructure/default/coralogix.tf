resource "aws_secretsmanager_secret" "corlogix_api_key" {
  name = "coralogix-api-key"
}

resource "aws_vpc_endpoint" "coralogix" {
  vpc_id              = var.vpc_id
  service_name        = "com.amazonaws.vpce.eu-west-1.vpce-svc-01f6152d495e211f0"
  vpc_endpoint_type   = "Interface"

  security_group_ids  = var.security_group_id
  subnet_ids          = var.private_subnet_ids
  private_dns_enabled = true
}
