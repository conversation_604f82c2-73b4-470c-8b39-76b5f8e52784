terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }

  backend "s3" {
    bucket = "terraform-state-meltwater-engage-ads-develop"
    key    = "engage-ads-backend/default/terraform.tfstate"
    region = "eu-west-1"
  }
}

provider "aws" {
  region = var.aws_region
  default_tags {
    tags = {
      MWBilling_COE        = "SCM"
      MWBilling_Department = 432
      MWBilling_Product    = "engage-ads"
      MWBilling_Feature    = "default"
      MWBilling_Service    = "graphql"
      MWBilling_Team       = "area51"
      MWBilling_Env        = "opex"
      clientdata           = "no"
    }
  }
}
