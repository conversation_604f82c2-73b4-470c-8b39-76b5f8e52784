# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [2.5.2](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-types@2.5.1...@meltwater/engage-ads-types@2.5.2) (2024-06-13)

**Note:** Version bump only for package @meltwater/engage-ads-types

## 2.5.1 (2024-06-01)

**Note:** Version bump only for package @meltwater/engage-ads-types

# [2.5.0](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-types@2.4.1...@meltwater/grimoire-types@2.5.0) (2024-05-29)

### Features

- Adding TikTok Lambda and code ([#68](https://github.com/meltwater/grimoire-commons/issues/68)) ([87f0912](https://github.com/meltwater/grimoire-commons/commit/87f09122226ab5c22d68432692dfc680439cb86e))

## [2.4.2-ENGAGE.0](https://github.com/meltwater/grimoire-lambdas/compare/@meltwater/grimoire-types@2.4.1...@meltwater/grimoire-types@2.4.2-ENGAGE.0) (2024-05-28)

**Note:** Version bump only for package @meltwater/grimoire-types

## [2.4.1](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-types@2.4.0...@meltwater/grimoire-types@2.4.1) (2024-05-28)

### Bug Fixes

- switch to section 31 graphql credential endpoint ([#60](https://github.com/meltwater/grimoire-commons/issues/60)) ([ba3186c](https://github.com/meltwater/grimoire-commons/commit/ba3186ceeca20ee8317c1dc2021c802090acac0a))

## [2.4.1-ENGAGE.1](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-types@2.4.1-ENGAGE.0...@meltwater/grimoire-types@2.4.1-ENGAGE.1) (2024-05-15)

**Note:** Version bump only for package @meltwater/grimoire-types

## [2.4.1-ENGAGE.0](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-types@2.4.0...@meltwater/grimoire-types@2.4.1-ENGAGE.0) (2024-05-15)

### Bug Fixes

- switch to section 31 graphql credential endpoint ([0241d54](https://github.com/meltwater/grimoire-commons/commit/0241d541f6a2472df8c32bd5c52c17bb6f272386))

### Features

- add resumable upload ([ba0f3b3](https://github.com/meltwater/grimoire-commons/commit/ba0f3b3d630a2e995cb795637dc69305e4b01e80))

# [2.4.0](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-types@2.3.0...@meltwater/grimoire-types@2.4.0) (2024-05-15)

### Features

- add resumable upload ([#57](https://github.com/meltwater/grimoire-commons/issues/57)) ([5f949fb](https://github.com/meltwater/grimoire-commons/commit/5f949fb0c3dcf8a36b696c49356b314a133e2a23))

## [2.3.2-ENGAGE.0](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-types@2.3.1-ENGAGE.0...@meltwater/grimoire-types@2.3.2-ENGAGE.0) (2024-05-03)

**Note:** Version bump only for package @meltwater/grimoire-types

## [2.3.1](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-types@2.3.1-ENGAGE.0...@meltwater/grimoire-types@2.3.1) (2024-05-03)

**Note:** Version bump only for package @meltwater/grimoire-types

## [2.3.1-ENGAGE.0](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-types@2.3.0...@meltwater/grimoire-types@2.3.1-ENGAGE.0) (2024-05-02)

### Features

- add resumable upload ([ba0f3b3](https://github.com/meltwater/grimoire-commons/commit/ba0f3b3d630a2e995cb795637dc69305e4b01e80))

# [2.3.0](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-types@2.2.1...@meltwater/grimoire-types@2.3.0) (2024-04-09)

### Features

- Adding Facebook Publish Lambda ([#53](https://github.com/meltwater/grimoire-commons/issues/53)) ([6a512bf](https://github.com/meltwater/grimoire-commons/commit/6a512bf0be3b6316084ecd898a502cd8b7c9a480))

## [2.2.2-ENGAGE.1](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-types@2.2.2-ENGAGE.0...@meltwater/grimoire-types@2.2.2-ENGAGE.1) (2024-04-08)

### Bug Fixes

- builds and versions and packages ([76c5a8a](https://github.com/meltwater/grimoire-commons/commit/76c5a8a9e8e19360a401048691e9bb3e7559b50a))

## [2.2.2-ENGAGE.0](https://github.com/meltwater/grimoire-lambdas/compare/@meltwater/grimoire-types@2.2.1...@meltwater/grimoire-types@2.2.2-ENGAGE.0) (2024-04-08)

### Features

- Adding Facebook Publish Lambda ([261ee73](https://github.com/meltwater/grimoire-lambdas/commit/261ee7361fe1f8ae114840c398908d918dab185c))

## [2.2.1](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-types@2.2.0...@meltwater/grimoire-types@2.2.1) (2024-04-08)

### Bug Fixes

- add IG Collaborators & update names of packages to be associated with our team on NPM ([#48](https://github.com/meltwater/grimoire-commons/issues/48)) ([2a3dcda](https://github.com/meltwater/grimoire-commons/commit/2a3dcda4d00ff19baec286c7c8ec5fdcf3bc7d6b))

## [2.2.1-ENGAGE.0](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-types@2.2.0...@meltwater/grimoire-types@2.2.1-ENGAGE.0) (2024-03-18)

### Bug Fixes

- add collaborators ([0f687dd](https://github.com/meltwater/grimoire-commons/commit/0f687dd0f6cc7d3b913f3da7156f6bcd269ad344))

# [2.2.0](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-types@2.1.2...@meltwater/grimoire-types@2.2.0) (2024-02-23)

### Features

- Instagram Product tagging ([#42](https://github.com/meltwater/grimoire-commons/issues/42)) ([1efac54](https://github.com/meltwater/grimoire-commons/commit/1efac54b934f6cfabd5616cd04b025f86e6c397f))

## [2.1.3-ENGAGE.0](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-types@2.1.2...@meltwater/grimoire-types@2.1.3-ENGAGE.0) (2024-02-23)

### Features

- Instagram Product tagging ([8282906](https://github.com/meltwater/grimoire-commons/commit/828290670d62e781323eb0b9c5cca4e2904dd034))

## [2.1.2](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-types@2.1.1...@meltwater/grimoire-types@2.1.2) (2024-02-23)

### Bug Fixes

- add skuId to message schema ([#41](https://github.com/meltwater/grimoire-commons/issues/41)) ([548cf12](https://github.com/meltwater/grimoire-commons/commit/548cf123a24a67ff79e8bcfb521aa96eee6f5271))

## [2.1.2-ENGAGE.0](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-types@2.1.1...@meltwater/grimoire-types@2.1.2-ENGAGE.0) (2024-02-22)

### Bug Fixes

- add skuId ([acd7510](https://github.com/meltwater/grimoire-commons/commit/acd751038f3a4f26e43ec048261dba8ed3a9184e))

## [2.1.1](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-types@2.1.0...@meltwater/grimoire-types@2.1.1) (2024-02-15)

### Bug Fixes

- add more product tagging fields ([#38](https://github.com/meltwater/grimoire-commons/issues/38)) ([7457990](https://github.com/meltwater/grimoire-commons/commit/7457990c416155963df83707fe2f443a141b41db))

## [2.1.1-ENGAGE.0](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-types@2.1.0...@meltwater/grimoire-types@2.1.1-ENGAGE.0) (2024-02-15)

### Bug Fixes

- add types for product tagging ([1e0b89c](https://github.com/meltwater/grimoire-commons/commit/1e0b89c0b744ee91d74862ae5f205bc73433989b))

# 2.1.0 (2024-02-08)

### Features

- instagram publish ([#24](https://github.com/meltwater/grimoire-commons/issues/24)) ([e1f2f2a](https://github.com/meltwater/grimoire-commons/commit/e1f2f2a035f33302edca266e22310e9b1c344e75))
- instagram publisher ([#19](https://github.com/meltwater/grimoire-commons/issues/19)) ([3e13113](https://github.com/meltwater/grimoire-commons/commit/3e13113971da30fe1d27d4fe418ac8b76ab24bc5))

## 2.0.1-ENGAGE.1 (2024-02-08)

### Features

- instagram publish ([#24](https://github.com/meltwater/grimoire-commons/issues/24)) ([e1f2f2a](https://github.com/meltwater/grimoire-commons/commit/e1f2f2a035f33302edca266e22310e9b1c344e75))
- instagram publisher ([#19](https://github.com/meltwater/grimoire-commons/issues/19)) ([3e13113](https://github.com/meltwater/grimoire-commons/commit/3e13113971da30fe1d27d4fe418ac8b76ab24bc5))

## 2.0.1-ENGAGE.0 (2024-02-08)

### Features

- instagram publish ([#24](https://github.com/meltwater/grimoire-commons/issues/24)) ([e1f2f2a](https://github.com/meltwater/grimoire-commons/commit/e1f2f2a035f33302edca266e22310e9b1c344e75))
- instagram publisher ([#19](https://github.com/meltwater/grimoire-commons/issues/19)) ([3e13113](https://github.com/meltwater/grimoire-commons/commit/3e13113971da30fe1d27d4fe418ac8b76ab24bc5))

## [1.3.4-ENGAGE.3](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-types@1.3.4-ENGAGE.2...@meltwater/grimoire-types@1.3.4-ENGAGE.3) (2024-02-05)

### Bug Fixes

- build before pushing tags ([8a52b30](https://github.com/meltwater/grimoire-commons/commit/8a52b304e37f54052245338b61c625deb8a25e37))

## [1.3.4-ENGAGE.2](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-types@1.3.4-ENGAGE.1...@meltwater/grimoire-types@1.3.4-ENGAGE.2) (2024-02-05)

### Bug Fixes

- publish test ([f472532](https://github.com/meltwater/grimoire-commons/commit/f4725323c646a3cb801f35bba804b8618f50e1c1))

## [1.3.4-ENGAGE.1](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-types@1.3.4-ENGAGE.0...@meltwater/grimoire-types@1.3.4-ENGAGE.1) (2024-02-05)

### Bug Fixes

- cleanup dependencies & unused files ([60016c7](https://github.com/meltwater/grimoire-commons/commit/60016c71d23395f7055c738eced181776ea4fd50))

## [1.3.4-ENGAGE.0](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-types@1.3.3...@meltwater/grimoire-types@1.3.4-ENGAGE.0) (2024-02-05)

### Bug Fixes

- clear cache for published packages ([59df6e8](https://github.com/meltwater/grimoire-commons/commit/59df6e812636d1009d61f92293a0d9c41353c6e1))

## [1.3.3](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-types@1.3.2...@meltwater/grimoire-types@1.3.3) (2024-02-05)

### Bug Fixes

- remove grimoire-decorators ([14d5f6b](https://github.com/meltwater/grimoire-commons/commit/14d5f6bd88bd667c5655d8120c18663669867c52))

## [1.3.2](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-types@1.3.1...@meltwater/grimoire-types@1.3.2) (2024-02-05)

### Bug Fixes

- add mpkg property ([2d611ad](https://github.com/meltwater/grimoire-commons/commit/2d611adda64da333058834df5eaffe0c3d43ddb5))

## [1.3.1](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-types@1.3.0...@meltwater/grimoire-types@1.3.1) (2024-02-05)

### Bug Fixes

- private:true is not needed for packages intended to be published ([24b4376](https://github.com/meltwater/grimoire-commons/commit/24b43769179002149ce0fc6be14987b10e1b532c))

# 1.3.0 (2024-02-05)

### Bug Fixes

- add publish-config ([8d0f0ec](https://github.com/meltwater/grimoire-commons/commit/8d0f0ecb79fb4a2bf27031dbae8b90da24f5eacc))

### Features

- instagram publisher ([#19](https://github.com/meltwater/grimoire-commons/issues/19)) ([3e13113](https://github.com/meltwater/grimoire-commons/commit/3e13113971da30fe1d27d4fe418ac8b76ab24bc5))

# 1.2.0 (2024-02-05)

### Features

- instagram publisher ([#19](https://github.com/meltwater/grimoire-commons/issues/19)) ([3e13113](https://github.com/meltwater/grimoire-commons/commit/3e13113971da30fe1d27d4fe418ac8b76ab24bc5))

# 1.1.0 (2024-02-01)

### Bug Fixes

- all packages should be private ([72f78ee](https://github.com/meltwater/grimoire-commons/commit/72f78ee3b11aeb9908f945427450463750808384))
- imports ([93eeddd](https://github.com/meltwater/grimoire-commons/commit/93eeddd0af544eaf25393b70bb4ea4c324f34243))
- **instagram:** missing notifications ([6f5ee59](https://github.com/meltwater/grimoire-commons/commit/6f5ee59df53f6390d8408966a998de73fd9c6427))
- publish thumbnail ([5b740c6](https://github.com/meltwater/grimoire-commons/commit/5b740c615c952ea5e6eae97670c8ad63f5777bbe))

### Features

- **instagram:** publish product tags ([b227e1f](https://github.com/meltwater/grimoire-commons/commit/b227e1f409c02759fc27866a4fea60cdac93003e))
- **instagram:** publish reels, mixed media & story ([5cb01f5](https://github.com/meltwater/grimoire-commons/commit/5cb01f5547f51c70ada1305d09e6e86c08196cc4))
- **monitoring:** add tracing & monitoring ([8f033a6](https://github.com/meltwater/grimoire-commons/commit/8f033a6347d042b7e24beb6e016e72066114d8e6))
