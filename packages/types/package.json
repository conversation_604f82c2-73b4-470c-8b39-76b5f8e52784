{"name": "@meltwater/engage-ads-types", "version": "2.5.3", "author": "Team Grimoire <<EMAIL>>", "devDependencies": {"typescript": "^5.3.3"}, "files": ["dist", "dist-types"], "license": "MIT", "main": "./dist/index.js", "scripts": {"build": "tsc", "clean": "rimraf dist dist-types tsconfig.tsbuildinfo", "registry:clear-cache": "bash ../../scripts/mpkg-clear-cache.sh $(echo $npm_package_name | cut -d '/' -f 2) $npm_package_version"}, "types": "./dist/index.d.ts", "publishConfig": {"access": "restricted", "registry": "https://registry.npmjs.org/"}, "mpkg": ["dist/**/*"], "gitHead": "19fbe41361142131fcd06d2c120c60926296017d"}