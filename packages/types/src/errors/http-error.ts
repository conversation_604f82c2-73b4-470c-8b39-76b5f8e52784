import { EngageError } from "./engage-error";

export class EngageHttpError extends EngageError {
  public status: number;
  public statusText: string;
  public response: Response | unknown;
  public headers: unknown;

  constructor(status: number, statusText: string, response: Response | unknown, headers: unknown) {
    super(`HTTP ${status}: ${statusText}`, statusText);
    Object.setPrototypeOf(this, new.target.prototype);

    this.name = this.constructor.name;
    this.status = status;
    this.statusText = statusText;
    this.response = response;
    this.headers = headers;
  }
}
