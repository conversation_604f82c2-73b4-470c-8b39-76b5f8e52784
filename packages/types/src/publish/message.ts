import type { PublishAsset } from "./asset";
import type { PublishAttachedLink } from "./attached-link";
import type { PublishError } from "./error";
import type { PublishExtras } from "./extras";
import type { PublishGeo } from "./geo";
import type { PublishTargeting } from "./targeting";

export interface PublishMessage {
  _id: string;
  assets: PublishAsset[];
  attachedLinkModel?: PublishAttachedLink;
  credentialId: number;
  companyId: string;
  channel: string;
  groupId: string;
  profile: string;
  profileId: string;
  postNow: boolean;
  messageText: string;
  messageDisplayText: string;
  firstComment?: string;
  userTags: string[];
  postStatus: PublishPostStatus;
  postType: PublishPostType;
  nativeId: string;
  scheduledDate: Date;
  permalink?: string;
  createdBy: string;
  updatedBy: string;
  assignedTo?: string;
  approvedBy?: string;
  rejectedBy?: string;
  titleText: string;
  privacy: string;
  allowEmbedding: boolean;
  videoMadeForKids: boolean;
  youtubeTags: string[];
  playlists: { key: string; name: string }[];
  category: { key: string; name: string };
  license: string;
  approvedOn?: Date;
  rejectedOn?: Date;
  sentDate?: Date;
  publishErrors?: PublishError[];
  audienceRestriction?: {
    minAge: string;
    countries: PublishGeo[];
    regions: PublishGeo[];
    cities: PublishGeo[];
    states: PublishGeo[];
    areas: PublishGeo[];
    zip: PublishGeo[];
    locations: PublishTargeting[];
    degrees: PublishTargeting[];
    fieldsOfStudy: PublishTargeting[];
    industries: PublishTargeting[];
    jobFunctions: PublishTargeting[];
    languages: PublishTargeting[];
    schools: PublishTargeting[];
    seniorities: PublishTargeting[];
    staffCount: string[];
  };
  extras: PublishExtras;
}

// TODO: Consider carefully if "null" and "" are actually needed - I do see them in the database!
export type PublishPostStatus =
  | null
  | ""
  | "DELETED"
  | "DRAFT"
  | "FAILED"
  | "MEDIA_COMPLETED"
  | "MEDIA_FAILED"
  | "MEDIA_PENDING"
  | "POSTED"
  | "POST_NOW"
  | "SCHEDULED"
  | "SCHEDULED_POST"
  | "SUBMITTED";

export type PublishPostType = "POST" | "STORY" | "CAROUSEL" | "REEL";
