import { Lamb<PERSON><PERSON>og<PERSON>, LambdaTracer } from "@meltwater/lambda-monitoring";
import type { HttpClient } from "@meltwater/engage-ads-commons";
import type { CampaignRecord, GraphQLRequestBody, GraphQLGetCampaignsResponse } from "./types/tiktok-campaigns";

const tracer = LambdaTracer.getInstance();
const logger = LambdaLogger.getInstance();

export class CampaignRepository {
  constructor(private readonly httpClient: HttpClient) {}

  @tracer.captureMethod({ subSegmentName: "CampaignRepository:getCampaignsByAdvertiserIds" })
  async getCampaignsByAdvertiserIds(advertiserIds: string[]): Promise<CampaignRecord[]> {
    logger.info("Fetching campaigns by advertiserIds", { advertiserIds });

    const query = `
    query QueryCampaigns($input: CampaignQueryInput!) {
      queryCampaigns(input: $input) {
        campaignId
        campaignName
      }
    }
  `;

    const variables = {
      input: {
        advertiserIds,
      },
    };

    const requestBody: GraphQLRequestBody = { query, variables };

    const response = await this.httpClient.post<GraphQLRequestBody, GraphQLGetCampaignsResponse>(
      "/graphql",
      requestBody,
    );

    if (!response.data?.queryCampaigns) {
      logger.error("Invalid response structure when fetching campaigns", {
        "http.response.body.content.text": JSON.stringify(response),
      });
      return [];
    }

    return response.data.queryCampaigns;
  }
}
