import type { HttpClient } from "@meltwater/engage-ads-commons";
import { LambdaLogger, LambdaTracer } from "@meltwater/lambda-monitoring";
import { WarpzoneModel, connectToDatabase } from "@meltwater/engage-ads-db-sdk";
import type {
  TikTokInsights,
  GetTikTokInsightsQueryParams,
  WildcardsResponse,
  WarpzoneQuery,
  GetTikTokInsightsQueryResponse,
} from "./types/tiktok-insights";

const logger = LambdaLogger.getInstance();
const tracer = LambdaTracer.getInstance();

export class TikTokInsightsRepository {
  constructor(private readonly httpClient: HttpClient) {}

  @tracer.captureMethod()
  async getTikTokInsights({
    startDate,
    endDate,
    size,
    order,
    aggregationType,
    startFrom,
    sortBy,
    profileIds,
    match,
  }: GetTikTokInsightsQueryParams): Promise<GetTikTokInsightsQueryResponse> {
    try {
      // Connect to database
      await connectToDatabase();

      // Get posts from database
      const query: WarpzoneQuery = {
        "document.body.publishDate.date": {
          $gte: new Date(startDate).getTime(),
          $lte: new Date(endDate).getTime(),
        },
        channel: "tiktok",
      };

      // Add profile ID filter if provided
      if (profileIds && profileIds.length > 0) {
        query["document.metaData.source.id"] = { $in: profileIds };
      }

      if (match && match.captionKeyword) {
        query["$text"] = { $search: match.captionKeyword };
      }

      const [posts, totalCount] = await Promise.all([
        WarpzoneModel.find(query)
          .sort({ "document.body.publishDate.date": -1 })
          .skip(startFrom || 0)
          .limit(size || 100),
        WarpzoneModel.countDocuments(query),
      ]);

      logger.info("Found posts from database", {
        count: posts.length,
        profileIds,
        dateRange: { startDate, endDate },
      });

      // Map posts to video IDs and create a lookup map
      const postIdVideoId = new Map();
      for (const post of posts) {
        // Extract video ID from URL
        const url = post.document.metaData.url;
        const videoIdMatch = url.match(/video\/(\d+)/);
        if (!videoIdMatch) {
          logger.warn("Could not extract video ID from URL", { url });
          continue;
        }
        const videoId = videoIdMatch[1];
        postIdVideoId.set(post._id, videoId);
      }

      const videoIds = Array.from(postIdVideoId.keys());
      logger.info("Extracted video IDs", { count: videoIds.length });

      // Prepare insights array
      const insights: TikTokInsights[] = [];

      if (videoIds.length > 0) {
        logger.info("Processing video IDs", { count: videoIds.length });
        try {
          // Process each post and create insights
          for (const post of posts) {
            const videoId = postIdVideoId.get(post._id);
            if (!videoId) {
              logger.warn("Video not found in map", { videoId });
              continue;
            }

            // Initialize insight with default values and data from database
            const insight: TikTokInsights = {
              video_id: videoId,
              permalink: post.document.metaData.url,
              document_id: post.document.id,
              video_views: 0,
              likes: 0,
              comments: 0,
              average_watch_time: 0,
              video_length: 0,
              engagements: 0,
              engagement_rate: 0,
              reach: 0,
              full_video_watch_rate: 0,
              total_time_watched: 0,
              shares: 0,
              impression_sources: [],
              thumbnail_url: "",
              caption: post.document.body.content.text,
              create_time: new Date(post.document.body.publishDate.date).toISOString(),
              company_id: post.companyId,
              profile_id: post.document.metaData.source.id,
              channel: "tiktok",
            };

            try {
              // Make API call for single video ID
              const queryParams = {
                "start-date": startDate,
                "end-date": endDate,
                size: (size || 100).toString(),
                order: order || "asc",
                "aggregation-type": aggregationType || "sum",
                "sort-by": sortBy || "likes",
                match: JSON.stringify({ video_id: videoId }),
              };

              logger.info("Making API call for video", {
                videoId,
                queryParams,
                endpoint: "/tiktok/v1/post/top-docs",
              });

              const response: WildcardsResponse = await this.httpClient.get("/tiktok/v1/post/top-docs", {
                params: queryParams,
                headers: {
                  "Accept-Language": "en-US,en;q=0.9",
                  "Content-Type": "application/json",
                },
                paramsSerializer: (params) => {
                  const searchParams = new URLSearchParams();
                  Object.entries(params).forEach(([key, value]) => {
                    searchParams.append(key, value);
                  });
                  return searchParams.toString();
                },
              });

              logger.info("API response received", {
                videoId,
                dataLength: response?.data?.length,
                hasMatch: response?.data?.some((p) => p._source.video_id === videoId),
              });

              // Find matching post in response
              const matchingPost = response?.data?.find((post) => post._source.video_id === videoId);

              if (!matchingPost) {
                logger.warn("No metrics found", {
                  videoId,
                  availableIds: response?.data?.map((p) => p._source.video_id),
                });
                insights.push(insight);
                continue;
              }

              const sourceData = matchingPost._source;

              // Get warpzone document to check for attachments
              const warpzoneDoc = await WarpzoneModel.findOne({ "document.id": post.document.id });
              const attachments = warpzoneDoc?.document?.attachments || [];
              const firstAttachment = attachments.length > 0 ? attachments[0] : null;

              // Apply metrics from source data
              Object.assign(insight, {
                video_views: Number(sourceData.video_views) || 0,
                likes: Number(sourceData.likes) || 0,
                comments: Number(sourceData.comments) || 0,
                average_watch_time: Number(sourceData.average_watch_time) || 0,
                video_length: Number(sourceData.video_length) || 0,
                engagements: Number(sourceData.engagements) || 0,
                engagement_rate: Number(sourceData.engagement_rate) || 0,
                reach: Number(sourceData.reach) || 0,
                full_video_watch_rate: Number(sourceData.full_video_watch_rate) || 0,
                total_time_watched: Number(sourceData.total_time_watched) || 0,
                shares: Number(sourceData.shares) || 0,
                impression_sources: sourceData.impression_sources || [],
                thumbnail_url: sourceData.thumbnail_url || firstAttachment?.link || "",
                mimeType: firstAttachment?.mimeType,
                caption: sourceData.caption || insight.caption,
              });

              logger.info("Retrieved metrics", {
                videoId,
                metrics: {
                  views: insight.video_views,
                  likes: insight.likes,
                  engagement: insight.engagement_rate,
                },
              });

              insights.push(insight);
            } catch (error) {
              logger.error("Failed to fetch metrics", { videoId, error });
              insights.push(insight);
            }
          }
        } catch (error) {
          logger.error("Failed to process posts", { error });
        }
      }

      return { insights, totalCount };
    } catch (error) {
      logger.error("Failed to get TikTok insights", { error });
      throw error;
    }
  }

  @tracer.captureMethod()
  async getTikTokInsightsSorted({
    startDate,
    endDate,
    size,
    order,
    aggregationType,
    startFrom,
    sortBy,
    profileIds,
    match,
  }: GetTikTokInsightsQueryParams): Promise<GetTikTokInsightsQueryResponse> {
    try {
      await connectToDatabase();

      const queryParams: Record<string, string | number | string[] | undefined> = {
        "start-date": startDate,
        "end-date": endDate,
        size: (size || 100).toString(),
        order: order || "asc",
        "start-from": startFrom,
        "aggregation-type": aggregationType || "sum",
        "sort-by": sortBy || "likes",
      };

      if (match?.captionKeyword) {
        queryParams["match"] = JSON.stringify({ "caption.keyword": match?.captionKeyword });
      }

      const response: WildcardsResponse = await this.httpClient.get("/tiktok/v1/post/top-docs", {
        params: queryParams,
        headers: {
          "Accept-Language": "en-US,en;q=0.9",
          "Content-Type": "application/json",
        },
        paramsSerializer: (params) => {
          const searchParams = new URLSearchParams();
          Object.entries(params).forEach(([key, value]) => {
            searchParams.append(key, value);
          });
          if (profileIds) {
            profileIds.forEach((profileId) => searchParams.append("tiktok-profiles", profileId));
          }
          const result = searchParams.toString();
          logger.info("Generated query string:", { result });
          return result;
        },
      });

      const wcPosts = response.data;
      const videoIdsWC = wcPosts.map((post) => post._source.video_id);

      const regexArray = videoIdsWC.map((vId) => new RegExp(vId));

      // Get posts from database
      const query: WarpzoneQuery = {
        channel: "tiktok",
      };

      query["document.metaData.url"] = { $in: regexArray };

      // Add profile ID filter if provided
      if (profileIds && profileIds.length > 0) {
        query["document.metaData.source.id"] = { $in: profileIds };
      }

      const posts = await WarpzoneModel.find(query);

      logger.info("Found posts from database", {
        count: posts.length,
        profileIds,
        dateRange: { startDate, endDate },
      });

      // Map posts to video IDs and create a lookup map
      const videoIdPostIdMap = new Map();
      for (const post of posts) {
        // Extract video ID from URL
        const url = post.document.metaData.url;
        const videoIdMatch = url.match(/video\/(\d+)/);
        if (!videoIdMatch) {
          logger.warn("Could not extract video ID from URL", { url });
          continue;
        }
        const videoId = videoIdMatch[1];
        videoIdPostIdMap.set(videoId, post._id);
      }

      const videoIds = Array.from(videoIdPostIdMap.keys());
      logger.info("Extracted video IDs", { count: videoIds.length });

      // Prepare insights array
      const insights: TikTokInsights[] = [];

      if (videoIds.length > 0) {
        logger.info("Processing video IDs", { count: videoIds.length });
        try {
          // Process each post and create insights
          for (const wcPost of wcPosts) {
            const videoId = wcPost._source.video_id;
            const postId = videoIdPostIdMap.get(videoId);
            const post = posts.find((p) => p._id === postId);

            if (!post) {
              logger.warn("Video not found in map of postsIds", { postId });
              continue;
            }

            // Initialize insight with default values and data from database
            const insight: TikTokInsights = {
              video_id: videoId,
              permalink: post.document.metaData.url,
              document_id: post.document.id,
              video_views: 0,
              likes: 0,
              comments: 0,
              average_watch_time: 0,
              video_length: 0,
              engagements: 0,
              engagement_rate: 0,
              reach: 0,
              full_video_watch_rate: 0,
              total_time_watched: 0,
              shares: 0,
              impression_sources: [],
              thumbnail_url: "",
              caption: post.document.body.content.text,
              create_time: new Date(post.document.body.publishDate.date).toISOString(),
              company_id: post.companyId,
              profile_id: post.document.metaData.source.id,
              channel: "tiktok",
            };

            try {
              // Wilcard post with insights
              const matchingPost = wcPost;

              const sourceData = matchingPost._source;

              // Get warpzone document to check for attachments
              const warpzoneDoc = await WarpzoneModel.findOne({ "document.id": post.document.id });
              const attachments = warpzoneDoc?.document?.attachments || [];
              const firstAttachment = attachments.length > 0 ? attachments[0] : null;

              // Apply metrics from source data
              Object.assign(insight, {
                video_views: Number(sourceData.video_views) || 0,
                likes: Number(sourceData.likes) || 0,
                comments: Number(sourceData.comments) || 0,
                average_watch_time: Number(sourceData.average_watch_time) || 0,
                video_length: Number(sourceData.video_length) || 0,
                engagements: Number(sourceData.engagements) || 0,
                engagement_rate: Number(sourceData.engagement_rate) || 0,
                reach: Number(sourceData.reach) || 0,
                full_video_watch_rate: Number(sourceData.full_video_watch_rate) || 0,
                total_time_watched: Number(sourceData.total_time_watched) || 0,
                shares: Number(sourceData.shares) || 0,
                impression_sources: sourceData.impression_sources || [],
                thumbnail_url: sourceData.thumbnail_url || firstAttachment?.link || "",
                mimeType: firstAttachment?.mimeType,
                caption: sourceData.caption || insight.caption,
              });

              insights.push(insight);
            } catch (error) {
              logger.error("Failed to match sorted metrics", { videoId, error });
              insights.push(insight);
            }
          }
        } catch (error) {
          logger.error("Failed to process sorted posts", { error });
        }
      }
      const totalCount = await this.getTikTokTotalCount(startDate, endDate, match?.captionKeyword, profileIds);
      return { insights, totalCount: totalCount };
    } catch (error) {
      logger.error("Failed to get TikTok sorted insights", { error });
      throw error;
    }
  }

  @tracer.captureMethod()
  async getTikTokTotalCount(
    startDate: string,
    endDate: string,
    captionKeyword?: string,
    profileIds?: string[],
  ): Promise<number> {
    try {
      const queryParams: Record<string, string | string[] | undefined> = {
        "start-date": startDate,
        "end-date": endDate,
      };

      if (captionKeyword) {
        queryParams["match"] = JSON.stringify({ "caption.keyword": captionKeyword });
      }

      logger.info("Fetching TikTok total count with params:", {
        queryParams,
        profileIds,
      });

      const response = await this.httpClient.get<{ data: { total_count: number } }>(`/tiktok/v1/post/total-count`, {
        params: queryParams,
        paramsSerializer: (params) => {
          const searchParams = new URLSearchParams();
          Object.entries(params).forEach(([key, value]) => {
            if (value !== undefined) {
              searchParams.append(key, value.toString());
            }
          });
          if (profileIds) {
            profileIds.forEach((profileId) => searchParams.append("tiktok-profiles", profileId));
          }
          const result = searchParams.toString();
          logger.info("Generated query string:", { result });
          return result;
        },
      });

      logger.info("TikTok total count response:", { response });
      return response.data.total_count;
    } catch (error) {
      logger.error("Error fetching TikTok total count:", {
        error,
        errorMessage: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined,
      });

      throw error; // Re-throw original error if no API response
    }
  }
}
