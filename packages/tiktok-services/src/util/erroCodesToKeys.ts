import { ERROR_KEYS } from "./errorKeys";
export const ERROR_CODE_TO_KEY: { [key: number]: string } = {
  // Existing error codes
  10000: ERROR_KEYS.INVALID_PARAMETERS,
  10001: ERROR_KEYS.AUTHENTICATION_FAILED,
  10002: ERROR_KEYS.ACCESS_TOKEN_EXPIRED,
  10003: ERROR_KEYS.INVALID_ACCESS_TOKEN,
  10004: ERROR_KEYS.INSUFFICIENT_PERMISSIONS,
  10005: ERROR_KEYS.QUOTA_LIMIT_EXCEEDED,
  10006: ERROR_KEYS.USER_NOT_FOUND,
  10007: ERROR_KEYS.VIDEO_NOT_FOUND,
  10008: ERROR_KEYS.COMMENT_NOT_FOUND,
  10009: ERROR_KEYS.ACTION_NOT_ALLOWED,
  10010: ERROR_KEYS.DUPLICATE_REQUEST,
  10011: ERROR_KEYS.SERVICE_UNAVAILABLE,
  10012: ERROR_KEYS.INTERNAL_SERVER_ERROR,
  10013: ERROR_KEYS.RATE_LIMIT_EXCEEDED,
  10014: ERROR_KEYS.UNSUPPORTED_MEDIA_TYPE,
  10015: ERROR_KEYS.CONTENT_VIOLATION,
  10016: ERROR_KEYS.ACCOUNT_RESTRICTED,
  10017: ERROR_KEYS.OPERATION_TIMEOUT,
  10018: ERROR_KEYS.FEATURE_UNAVAILABLE,
  10019: ERROR_KEYS.INVALID_SIGNATURE,
  10020: ERROR_KEYS.DATA_FORMAT_ERROR,

  // TikTok API for Business error codes
  0: ERROR_KEYS.SUCCESSFUL,
  20001: ERROR_KEYS.PARTIALLY_SUCCESSFUL,
  40000: ERROR_KEYS.INVALID_PARAMETERS_BUSINESS,
  40001: ERROR_KEYS.NO_PERMISSION,
  40002: ERROR_KEYS.PARAMETER_ERROR,
  40006: ERROR_KEYS.API_VERSION_INCOMPATIBLE,
  40007: ERROR_KEYS.OBJECT_NOT_FOUND,
  40008: ERROR_KEYS.ENDPOINT_NOT_IMPLEMENTED,
  40009: ERROR_KEYS.SANDBOX_UNSUPPORTED,
  40010: ERROR_KEYS.DOMAIN_UNSUPPORTED,
  40011: ERROR_KEYS.TOO_MANY_IDS,
  40013: ERROR_KEYS.SANDBOX_ACCOUNT_NOT_FOUND,
  40014: ERROR_KEYS.SANDBOX_FEATURE_UNSUPPORTED,
  40050: ERROR_KEYS.DUPLICATED_REQUESTS,
  40051: ERROR_KEYS.INVALID_API_VERSION,
  40052: ERROR_KEYS.ACO_MATERIAL_EXISTS,
  40053: ERROR_KEYS.INVALID_VIDEO_ID,
  40016: ERROR_KEYS.RATE_LIMIT_FREQUENT,
  40100: ERROR_KEYS.RATE_LIMIT_FREQUENT,
  40133: ERROR_KEYS.RATE_LIMIT_FREQUENT,
  40502: ERROR_KEYS.MAX_AD_LIMIT,
  40132: ERROR_KEYS.RATE_LIMIT_FIELD_VALUE,
  40110: ERROR_KEYS.INVALID_AUTHORIZATION_CODE,
  40101: ERROR_KEYS.INVALID_AUTH_PARAMETERS,
  40102: ERROR_KEYS.ACCESS_TOKEN_EXPIRED_BUSINESS,
  40104: ERROR_KEYS.ACCESS_TOKEN_EMPTY,
  40105: ERROR_KEYS.INVALID_ACCESS_TOKEN,
  40106: ERROR_KEYS.INVALID_CORE_USER,
  40103: ERROR_KEYS.REFRESH_TOKEN_EXPIRED,
  40107: ERROR_KEYS.INVALID_REFRESH_TOKEN,
  40108: ERROR_KEYS.INVALID_AUTHORIZATION_TYPE,
  40131: ERROR_KEYS.AUTHENTICATION_ERROR,
  40109: ERROR_KEYS.DECIPHERING_ERROR,
  40112: ERROR_KEYS.INCORRECT_PASSWORD,
  40113: ERROR_KEYS.APP_BLOCKED,
  40115: ERROR_KEYS.AUTH_TIMESTAMP_EXPIRED,
  40116: ERROR_KEYS.INVALID_SIGNATURE_BUSINESS,
  40117: ERROR_KEYS.METHOD_NOT_ALLOWED,
  40118: ERROR_KEYS.ALLOWLIST_REQUIRED,
  40119: ERROR_KEYS.DEVELOPER_ADVERTISER_MISMATCH,
  40121: ERROR_KEYS.NON_TCM_CREATOR,
  40122: ERROR_KEYS.TCM_CREATOR_INVALID_REGION,
  40124: ERROR_KEYS.DEVELOPER_PROFILE_INCOMPLETE,
  40125: ERROR_KEYS.DEVELOPER_PERMISSION_DENIED,
  41001: ERROR_KEYS.INTERFACE_OFFLINE,
  41002: ERROR_KEYS.FIELDS_NOT_IN_USE,
  40065: ERROR_KEYS.TARGETING_AGE_RESTRICTION,
  40200: ERROR_KEYS.TASK_ERROR,
  40201: ERROR_KEYS.TASK_NOT_READY,
  40202: ERROR_KEYS.ENTITY_CONFLICT,
  40300: ERROR_KEYS.ADVERTISER_NOT_FOUND,
  40301: ERROR_KEYS.ADVERTISER_MISMATCH,
  40700: ERROR_KEYS.INTERNAL_SERVICE_ERROR,
  40901: ERROR_KEYS.VIDEO_TRANSCODING,
  40902: ERROR_KEYS.URL_FETCH_FAILED,
  40903: ERROR_KEYS.IMAGE_URL_UNAVAILABLE,
  40913: ERROR_KEYS.IMAGE_URL_UNAVAILABLE,
  40905: ERROR_KEYS.FILE_NOT_FOUND,
  40906: ERROR_KEYS.FILE_EMPTY,
  40907: ERROR_KEYS.FILE_TOO_LARGE,
  40910: ERROR_KEYS.FILE_EXPIRED,
  40914: ERROR_KEYS.FILE_INVALID,
  40908: ERROR_KEYS.FILE_TYPE_UNSUPPORTED,
  40900: ERROR_KEYS.SIGNATURE_MISMATCH,
  40909: ERROR_KEYS.ENCRYPTION_UNSUPPORTED,
  40911: ERROR_KEYS.MATERIAL_NAME_DUPLICATE,
  40904: ERROR_KEYS.ILLEGAL_IMAGE_CONTENT,
  40912: ERROR_KEYS.IMAGE_URL_INVALID,
  40915: ERROR_KEYS.FILE_SPECS_UNMET,
  41000: ERROR_KEYS.IP_BANNED,
  50000: ERROR_KEYS.SYSTEM_ERROR,
  50002: ERROR_KEYS.REQUEST_PROCESSING_ERROR,
  51305: ERROR_KEYS.SATELLITE_SERVICE_ERROR,
  60001: ERROR_KEYS.SYSTEM_MAINTENANCE,
};
