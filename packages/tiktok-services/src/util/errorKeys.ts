export const ERROR_KEYS = {
  INVALID_PARAMETERS: "invalid-parameters",
  AUTHENTICATION_FAILED: "authentication-failed",
  ACCESS_TOKEN_EXPIRED: "access-token-expired",
  INSUFFICIENT_PERMISSIONS: "insufficient-permissions",
  QUOTA_LIMIT_EXCEEDED: "quota-limit-exceeded",
  USER_NOT_FOUND: "user-not-found",
  VIDEO_NOT_FOUND: "video-not-found",
  COMMENT_NOT_FOUND: "comment-not-found",
  ACTION_NOT_ALLOWED: "action-not-allowed",
  DUPLICATE_REQUEST: "duplicate-request",
  SERVICE_UNAVAILABLE: "service-unavailable",
  INTERNAL_SERVER_ERROR: "internal-server-error",
  RATE_LIMIT_EXCEEDED: "rate-limit-exceeded",
  UNSUPPORTED_MEDIA_TYPE: "unsupported-media-type",
  CONTENT_VIOLATION: "content-violation",
  ACCOUNT_RESTRICTED: "account-restricted",
  OPERATION_TIMEOUT: "operation-timeout",
  FEATURE_UNAVAILABLE: "feature-unavailable",
  INVALID_SIGNATURE: "invalid-signature",
  DATA_FORMAT_ERROR: "data-format-error",

  SUCCESSFUL: "successful",
  PARTIALLY_SUCCESSFUL: "partially-successful",
  INVALID_PARAMETERS_BUSINESS: "invalid-parameters-business",
  NO_PERMISSION: "no-permission",
  PARAMETER_ERROR: "parameter-error",
  API_VERSION_INCOMPATIBLE: "api-version-incompatible",
  OBJECT_NOT_FOUND: "object-not-found",
  ENDPOINT_NOT_IMPLEMENTED: "endpoint-not-implemented",
  SANDBOX_UNSUPPORTED: "sandbox-unsupported",
  DOMAIN_UNSUPPORTED: "domain-unsupported",
  TOO_MANY_IDS: "too-many-ids",
  SANDBOX_ACCOUNT_NOT_FOUND: "sandbox-account-not-found",
  SANDBOX_FEATURE_UNSUPPORTED: "sandbox-feature-unsupported",
  DUPLICATED_REQUESTS: "duplicated-requests",
  INVALID_API_VERSION: "invalid-api-version",
  ACO_MATERIAL_EXISTS: "aco-material-exists",
  INVALID_VIDEO_ID: "invalid-video-id",
  RATE_LIMIT_FREQUENT: "rate-limit-frequent",
  MAX_AD_LIMIT: "max-ad-limit",
  RATE_LIMIT_FIELD_VALUE: "rate-limit-field-value",
  INVALID_AUTHORIZATION_CODE: "invalid-authorization-code",
  INVALID_AUTH_PARAMETERS: "invalid-auth-parameters",
  ACCESS_TOKEN_EXPIRED_BUSINESS: "access-token-expired-business",
  ACCESS_TOKEN_EMPTY: "access-token-empty",
  INVALID_ACCESS_TOKEN: "invalid-access-token",
  INVALID_CORE_USER: "invalid-core-user",
  REFRESH_TOKEN_EXPIRED: "refresh-token-expired",
  INVALID_REFRESH_TOKEN: "invalid-refresh-token",
  INVALID_AUTHORIZATION_TYPE: "invalid-authorization-type",
  AUTHENTICATION_ERROR: "authentication-error",
  DECIPHERING_ERROR: "deciphering-error",
  INCORRECT_PASSWORD: "incorrect-password",
  APP_BLOCKED: "app-blocked",
  AUTH_TIMESTAMP_EXPIRED: "auth-timestamp-expired",
  INVALID_SIGNATURE_BUSINESS: "invalid-signature-business",
  METHOD_NOT_ALLOWED: "method-not-allowed",
  ALLOWLIST_REQUIRED: "allowlist-required",
  DEVELOPER_ADVERTISER_MISMATCH: "developer-advertiser-mismatch",
  NON_TCM_CREATOR: "non-tcm-creator",
  TCM_CREATOR_INVALID_REGION: "tcm-creator-invalid-region",
  DEVELOPER_PROFILE_INCOMPLETE: "developer-profile-incomplete",
  DEVELOPER_PERMISSION_DENIED: "developer-permission-denied",
  INTERFACE_OFFLINE: "interface-offline",
  FIELDS_NOT_IN_USE: "fields-not-in-use",
  TARGETING_AGE_RESTRICTION: "targeting-age-restriction",
  TASK_ERROR: "task-error",
  TASK_NOT_READY: "task-not-ready",
  ENTITY_CONFLICT: "entity-conflict",
  ADVERTISER_NOT_FOUND: "advertiser-not-found",
  ADVERTISER_MISMATCH: "advertiser-mismatch",
  INTERNAL_SERVICE_ERROR: "internal-service-error",
  VIDEO_TRANSCODING: "video-transcoding",
  URL_FETCH_FAILED: "url-fetch-failed",
  IMAGE_URL_UNAVAILABLE: "image-url-unavailable",
  FILE_NOT_FOUND: "file-not-found",
  FILE_EMPTY: "file-empty",
  FILE_TOO_LARGE: "file-too-large",
  FILE_EXPIRED: "file-expired",
  FILE_INVALID: "file-invalid",
  FILE_TYPE_UNSUPPORTED: "file-type-unsupported",
  SIGNATURE_MISMATCH: "signature-mismatch",
  ENCRYPTION_UNSUPPORTED: "encryption-unsupported",
  MATERIAL_NAME_DUPLICATE: "material-name-duplicate",
  ILLEGAL_IMAGE_CONTENT: "illegal-image-content",
  IMAGE_URL_INVALID: "image-url-invalid",
  FILE_SPECS_UNMET: "file-specs-unmet",
  IP_BANNED: "ip-banned",
  SYSTEM_ERROR: "system-error",
  REQUEST_PROCESSING_ERROR: "request-processing-error",
  SATELLITE_SERVICE_ERROR: "satellite-service-error",
  SYSTEM_MAINTENANCE: "system-maintenance",
};
