import type { HttpClient } from "@meltwater/engage-ads-commons";
import { LambdaTracer } from "@meltwater/lambda-monitoring";
import { LambdaLogger } from "@meltwater/lambda-monitoring";
import type {
  TikTokAdAccount,
  TikTokAdAccountQueryInput,
  TikTokProfile,
  GraphQLRequest,
  GraphQLResponseWrapper,
  TikTokAdAccountResponse,
  TikTokProfileResponse,
  HttpClientResponse,
} from "./types/tiktok-ad-account";

const tracer = LambdaTracer.getInstance();
const logger = LambdaLogger.getInstance();

export class TikTokAdAccountRepository {
  constructor(private readonly httpClient: HttpClient) {}

  @tracer.captureMethod({ subSegmentName: "TikTokAdAccountRepository:getTikTokAdAccounts" })
  async getTikTokAdAccounts(input: TikTokAdAccountQueryInput, accessToken: string): Promise<TikTokAdAccount[]> {
    try {
      const graphqlRequest: GraphQLRequest = {
        query: `
          query Query($query: QueryCredential) {
            credentialQuery(query: $query) {
              CredentialId
              TargetPageName
              TargetPageLogoUrl
              SocialAccountId
              associatedCredentials {
                credentialId
                targetPageName
                targetPageLogoUrl
                socialAccountId
                tikTokIdentity {
                  identity_id
                  identity_type
                  identity_authorized_bc_id
                }
              }
            }
          }
        `,
        variables: {
          query: input,
        },
      };

      const headers = {
        Authorization: `${accessToken}`,
      };

      const response: HttpClientResponse<GraphQLResponseWrapper> = await this.httpClient.post(
        "/graphql",
        graphqlRequest,
        { headers },
      );

      if (!response || !response.data || !response.data.credentialQuery) {
        logger.error("Invalid response structure", { "http.response.body.content.text": JSON.stringify(response) });
        throw new Error("Invalid response structure");
      }

      const adAccounts = response.data.credentialQuery.map(
        (account: TikTokAdAccountResponse): TikTokAdAccount => ({
          credentialId: account.CredentialId,
          targetPageName: account.TargetPageName,
          targetPageLogoUrl: account.TargetPageLogoUrl,
          socialAccountId: account.SocialAccountId,
          associatedProfiles: account.associatedCredentials
            ? account.associatedCredentials.map(
                (profile: TikTokProfileResponse): TikTokProfile => ({
                  credentialId: profile.credentialId,
                  targetPageName: profile.targetPageName,
                  socialAccountId: profile.socialAccountId,
                  targetPageLogoUrl: profile.targetPageLogoUrl,
                  identityId: profile.tikTokIdentity.identity_id,
                  identityType: profile.tikTokIdentity.identity_type,
                  identityAuthorizedBcId: profile.tikTokIdentity.identity_authorized_bc_id,
                }),
              )
            : [],
        }),
      );

      logger.info("Processed TikTok ad accounts", { count: adAccounts.length });

      return adAccounts;
    } catch (error) {
      logger.error("Error fetching TikTok ad accounts:", {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        input,
        accessTokenLength: accessToken.length,
      });
      throw error;
    }
  }
}
