import { LambdaTracer, LambdaLogger } from "@meltwater/lambda-monitoring";
import type {
  CreateTiktokAdApiParams,
  CreateTiktokAdGroupApiParams,
  CreateTiktokAdGroupResponse,
  CreateTiktokAdResponse,
  CreateTiktokCampaignApiParams,
  CreateTiktokCampaignResponse,
  TiktokSparkAdsParams,
} from "./types/tiktok-spark-ads";
import type { GetTiktokRegionAPIParams, GetTiktokRegionParams, GetTiktokRegionResponse, RegionInfo } from "./types/";

import { CampaignApi } from "./sdk/CampaignApi";
import { AdGroupApi } from "./sdk/AdGroupApi";
import { AdApi } from "./sdk/AdApi";
import { RegionApi } from "./sdk/RegionApi";
import { AdvertiserInfoApi } from "./sdk/AdvertiserInfoApi";
import type {
  AdvertiserInfo,
  GetTiktokAdvertiserAPIParams,
  GetTiktokAdvertiserInfoResponse,
  GetTiktokAdvertiserParams,
} from "./types/tiktok-advertiser";
import { handlerErrorCode } from "./util/handleError";

const logger = LambdaLogger.getInstance();

const tracer = LambdaTracer.getInstance();

export class TiktokBoostAdRepository {
  constructor(private readonly accessToken: string) {
    if (!accessToken) {
      throw new Error("Access token is required");
    }
  }

  @tracer.captureMethod({ subSegmentName: "TiktokRepository:createCampaign" })
  async createCampaign(payload: TiktokSparkAdsParams): Promise<CreateTiktokCampaignResponse> {
    const params: CreateTiktokCampaignApiParams = {
      advertiser_id: payload.campaign.advertiserId,
      budget: payload.campaign.budget,
      campaign_name: payload.campaign.campaignName,
      objective_type: payload.campaign.objectiveType,
      budget_mode: payload.campaign.budgetMode,
      budget_optimize_on: false,
    };

    const campaignAPI = new CampaignApi(this.accessToken);
    const result = await campaignAPI.createCampaign(params);

    const errorKey = handlerErrorCode(result.code);

    const error = new Error();
    error.name = errorKey;
    error.message = result.message;

    logger.debug("result for campaign creation is", { "http.response.body.content.text": JSON.stringify(result) });

    if (!result.data.campaign_id) {
      throw error;
    }

    return {
      campaignId: result.data.campaign_id,
      campaignName: params.campaign_name,
      advertiserId: params.advertiser_id,
      // Include other fields as needed
    };
  }

  @tracer.captureMethod({ subSegmentName: "TiktokRepository:createAdGroup" })
  async createAdGroup(payload: TiktokSparkAdsParams & { campaignId: string }): Promise<CreateTiktokAdGroupResponse> {
    const scheduleEndTime = payload.adGroup.scheduleEndTime;
    const params: CreateTiktokAdGroupApiParams = {
      advertiser_id: payload.adGroup.advertiserId,
      campaign_id: payload.campaignId,
      adgroup_name: payload.adGroup.adgroupName,
      schedule_start_time: payload.adGroup.scheduleStartTime,
      schedule_type: payload.adGroup.scheduleType,
      billing_event: payload.adGroup.billingEvent,
      pacing: payload.adGroup.pacing,
      budget: payload.adGroup.budget,
      budget_mode: payload.adGroup.budgetMode,
      location_ids: payload.adGroup.locationIds,
      placements: payload.adGroup.placements,
      schedule_end_time: scheduleEndTime || undefined,
      optimization_goal: payload.adGroup.optimizationGoal,
      bid_price: payload.adGroup.bidPrice,
      conversion_bid_price: payload.adGroup.conversionBidPrice,
    };

    if (payload.campaign.objectiveType === "REACH") {
      params.frequency = payload.adGroup.frequency;
      params.frequency_schedule = payload.adGroup.frequencySchedule;
    }
    if (payload.campaign.objectiveType === "TRAFFIC") {
      params.promotion_type = payload.adGroup.promotionType || "WEBSITE";
    }

    const adGroupAPI = new AdGroupApi(this.accessToken);
    const result = await adGroupAPI.createAdGroup(params);

    logger.debug("result for ad group creation is", { "http.response.body.content.text": JSON.stringify(result) });

    if (!result.data.adgroup_id) {
      throw new Error(result.message);
    }

    return {
      adgroupId: result.data.adgroup_id,
      adgroupName: params.adgroup_name,
      advertiserId: params.advertiser_id,
      campaignId: params.campaign_id,
      scheduleEndTime: params.schedule_end_time,
    };
  }

  @tracer.captureMethod({ subSegmentName: "TiktokRepository:createAd" })
  async createAd(
    payload: TiktokSparkAdsParams & { adgroupId: string; campaignId: string },
  ): Promise<CreateTiktokAdResponse> {
    const params: CreateTiktokAdApiParams = {
      advertiser_id: payload.ad.advertiserId,
      adgroup_id: payload.adgroupId,
      creatives: payload.ad.creatives.map((c) => ({
        ad_name: c.adName,
        identity_type: c.identityType,
        identity_id: c.identityId,
        identity_authorized_bc_id: c.identityAuthorizedBcId,
        ad_format: c.adFormat,
        tiktok_item_id: c.videoId,
        call_to_action: c.callToAction,
        landing_page_url: c.landingPageUrl,
      })),
    };

    const adAPI = new AdApi(this.accessToken);
    const result = await adAPI.createAd(params);
    logger.debug("result for ad creation is", { "http.response.body.content.text": JSON.stringify(result) });

    if (!result.data.ad_ids?.length) {
      throw new Error(result.message);
    }

    return {
      adId: result.data.ad_ids[0],
      adName: params.creatives[0].ad_name,
      advertiserId: params.advertiser_id,
      campaignId: payload.campaignId,
      adgroupId: params.adgroup_id,
      // Include other fields as needed
    };
  }

  @tracer.captureMethod({ subSegmentName: "TiktokRepository:getBoostingRegions" })
  async getBoostingRegions(payload: GetTiktokRegionParams): Promise<GetTiktokRegionResponse> {
    if (!this.accessToken)
      return {
        data: [],
      };

    const params: GetTiktokRegionAPIParams = {
      advertiser_id: payload.advertiserId,
      objective_type: payload.objectiveType,
      placements: payload.placements,
      level_range: payload.level,
    };

    const regionAPI = new RegionApi(this.accessToken);
    const result = await regionAPI.getRegion(params);

    const regionInfo = result.data.region_info.map((region: RegionInfo) => ({
      nextLevelIds: region.next_level_ids,
      areaType: region.area_type,
      level: region.level,
      regionCode: region.region_code,
      locationId: region.location_id,
      name: region.name,
      parentId: region.parent_id,
      supportBelow18: region.support_below_18,
    }));

    return {
      data: regionInfo,
    };
  }

  @tracer.captureMethod({ subSegmentName: "TiktokRepository:getBoostingAdvertisersInfo" })
  async getBoostingAdvertisersInfo(payload: GetTiktokAdvertiserParams): Promise<GetTiktokAdvertiserInfoResponse> {
    if (!this.accessToken)
      return {
        data: [],
      };

    const params: GetTiktokAdvertiserAPIParams = {
      advertiser_ids: payload.advertiserIds,
    };
    const advertiserApi = new AdvertiserInfoApi(this.accessToken);
    const result = await advertiserApi.getAdvertiserInfo(params);

    const advInfo = result.data.list.map((adv: AdvertiserInfo) => ({
      advertiserId: adv.advertiser_id,
      timezone: adv.timezone,
      currency: adv.currency,
    }));

    return {
      data: advInfo,
    };
  }
}
