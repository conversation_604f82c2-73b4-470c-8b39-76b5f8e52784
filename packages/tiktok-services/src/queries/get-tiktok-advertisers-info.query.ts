import type { IQuery } from "@meltwater/cqrs";
import { LambdaTracer } from "@meltwater/lambda-monitoring";
import type { TiktokBoostAdRepository } from "../tiktok-boost-ad-repository";
import type { GetTiktokAdvertiserInfoResponse, GetTiktokAdvertiserParams } from "../types/tiktok-advertiser";

const tracer = LambdaTracer.getInstance();

export class GetTikTokAdvertisersInfoQuery
  implements IQuery<GetTiktokAdvertiserParams, GetTiktokAdvertiserInfoResponse>
{
  constructor(private repository: TiktokBoostAdRepository) {}

  @tracer.captureMethod({ subSegmentName: "GetTikTokAdvertisersInfoQuery:execute" })
  public async execute(query: GetTiktokAdvertiserParams): Promise<GetTiktokAdvertiserInfoResponse> {
    const { ...input } = query;
    return this.repository.getBoostingAdvertisersInfo(input);
  }
}
