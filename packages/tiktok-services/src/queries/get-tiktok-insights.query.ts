import type { IQuery } from "@meltwater/cqrs";
import { LambdaTracer } from "@meltwater/lambda-monitoring";
import type { TikTokInsightsRepository } from "../tiktok-insights-repository";
import type { GetTikTokInsightsQueryResponse, GetTikTokInsightsQueryParams } from "../types/tiktok-insights";

const tracer = LambdaTracer.getInstance();

export class GetTikTokInsightsQuery implements IQuery<GetTikTokInsightsQueryParams, GetTikTokInsightsQueryResponse> {
  constructor(private readonly tikTokInsightsRepository: TikTokInsightsRepository) {}

  @tracer.captureMethod({ subSegmentName: "GetTikTokInsightsQuery:execute" })
  public async execute(params: GetTikTokInsightsQueryParams): Promise<GetTikTokInsightsQueryResponse> {
    let result;
    if (params.sortByMetric) {
      result = await this.tikTokInsightsRepository.getTikTokInsightsSorted(params);
    } else {
      result = await this.tikTokInsightsRepository.getTikTokInsights(params);
    }
    return result;
  }
}
