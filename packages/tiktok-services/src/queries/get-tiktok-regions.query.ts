import type { IQuery } from "@meltwater/cqrs";
import { LambdaTracer } from "@meltwater/lambda-monitoring";
import type { GetTiktokRegionParams, GetTiktokRegionResponse } from "../types/tiktok-region";
import type { TiktokBoostAdRepository } from "../tiktok-boost-ad-repository";

const tracer = LambdaTracer.getInstance();

export class GetTikTokRegionsQuery implements IQuery<GetTiktokRegionParams, GetTiktokRegionResponse> {
  constructor(private repository: TiktokBoostAdRepository) {}

  @tracer.captureMethod({ subSegmentName: "GetTikTokRegionsQuery:execute" })
  public async execute(query: GetTiktokRegionParams): Promise<GetTiktokRegionResponse> {
    const { ...input } = query;
    return this.repository.getBoostingRegions(input);
  }
}
