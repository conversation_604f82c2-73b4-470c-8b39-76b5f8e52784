import type { IQuery } from "@meltwater/cqrs";
import { LambdaTracer } from "@meltwater/lambda-monitoring";
import type { TikTokAdAccountRepository } from "../tiktok-ad-account-repository";
import type { TikTokAdAccount, ExtendedTikTokAdAccountQueryInput } from "../types/tiktok-ad-account";

const tracer = LambdaTracer.getInstance();

export class GetTikTokAdAccountsQuery implements IQuery<ExtendedTikTokAdAccountQueryInput, TikTokAdAccount[]> {
  constructor(private readonly tikTokAdAccountRepository: TikTokAdAccountRepository) {}

  @tracer.captureMethod({ subSegmentName: "GetTikTokAdAccountsQuery:execute" })
  public async execute(query: ExtendedTikTokAdAccountQueryInput): Promise<TikTokAdAccount[]> {
    const { accessToken, ...input } = query;
    return this.tikTokAdAccountRepository.getTikTokAdAccounts(input, accessToken);
  }
}
