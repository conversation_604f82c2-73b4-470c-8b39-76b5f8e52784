import type { IQuery } from "@meltwater/cqrs";
import { LambdaTracer } from "@meltwater/lambda-monitoring";
import type { CampaignRepository } from "../tiktok-campaign-repository";
import type { GetCampaignsQueryParams, Campaign } from "../types/tiktok-campaigns";

const tracer = LambdaTracer.getInstance();

export class GetCampaignsQuery implements IQuery<GetCampaignsQueryParams, Campaign[]> {
  constructor(private campaignRepository: CampaignRepository) {}

  @tracer.captureMethod({ subSegmentName: "GetCampaignsQuery:execute" })
  public async execute(params: GetCampaignsQueryParams): Promise<Campaign[]> {
    return this.campaignRepository.getCampaignsByAdvertiserIds(params.advertiserIds);
  }
}
