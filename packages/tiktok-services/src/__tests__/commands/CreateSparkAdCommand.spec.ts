import { CreateSparkAdCommand } from "../../commands/create-spark-ad.command";
import type { TiktokBoostAdRepository } from "../../tiktok-boost-ad-repository";
import type { TiktokSparkAdsParams } from "../../types/tiktok-spark-ads";

jest.mock("@meltwater/lambda-monitoring", () => ({
  LambdaTracer: {
    getInstance: jest.fn().mockReturnValue({
      captureMethod: jest
        .fn()
        .mockImplementation(() => (target: unknown, propertyKey: string, descriptor: PropertyDescriptor) => descriptor),
      putMetadata: jest.fn(),
    }),
  },
  LambdaLogger: {
    getInstance: jest.fn().mockReturnValue({
      error: jest.fn(),
      info: jest.fn(),
    }),
  },
}));

describe("CreateSparkAdCommand", () => {
  let repositoryMock: jest.Mocked<TiktokBoostAdRepository>;
  let command: CreateSparkAdCommand;

  beforeEach(() => {
    jest.clearAllMocks();

    repositoryMock = {
      createCampaign: jest.fn(),
      createAdGroup: jest.fn(),
      createAd: jest.fn(),
    } as unknown as jest.Mocked<TiktokBoostAdRepository>;

    command = new CreateSparkAdCommand(repositoryMock);
  });

  it("should execute createCampaign, createAdGroup, and createAd in order", async () => {
    const params: TiktokSparkAdsParams = {
      campaign: {
        objectiveType: "VIDEO_VIEWS",
        campaignName: "test-campaign",
        budget: 100,
        advertiserId: "123456",
        budgetMode: "BUDGET_MODE_TOTAL",
      },
      adGroup: {
        advertiserId: "123456",
        adgroupName: "test-adgroup",
        scheduleStartTime: "2023-01-01 00:00:00",
        scheduleType: "SCHEDULE_START_END",
        billingEvent: "CPV",
        pacing: "PACING_MODE_SMOOTH",
        budget: 50,
        budgetMode: "BUDGET_MODE_TOTAL",
        locationIds: ["6252001"],
        placements: ["PLACEMENT_TIKTOK"],
        scheduleEndTime: "2023-01-02 00:00:00",
        optimizationGoal: "ENGAGED_VIEW",
        bidPrice: 1,
      },
      ad: {
        advertiserId: "123456",
        creatives: [
          {
            adName: "test-ad",
            identityType: "TT_USER",
            adFormat: "SINGLE_VIDEO",
            videoId: "video123",
            identityId: "identity123",
          },
        ],
      },
      companyId: "companyId123",
      credentialId: 1,
    };

    repositoryMock.createCampaign.mockResolvedValue({ campaignId: "campaign123" });
    repositoryMock.createAdGroup.mockResolvedValue({ adgroupId: "adgroup123" });
    repositoryMock.createAd.mockResolvedValue({ adId: "ad123" });

    const result = await command.execute(params);

    expect(repositoryMock.createCampaign).toHaveBeenCalledWith(params);
    expect(repositoryMock.createAdGroup).toHaveBeenCalledWith({
      ...params,
      campaignId: "campaign123",
    });
    expect(repositoryMock.createAd).toHaveBeenCalledWith({
      ...params,
      adgroupId: "adgroup123",
      campaignId: "campaign123",
    });

    expect(result).toEqual({
      campaign: { campaignId: "campaign123" },
      adGroup: { adgroupId: "adgroup123" },
      ad: { adId: "ad123" },
    });
  });

  it("should handle errors thrown by the repository methods", async () => {
    const params: TiktokSparkAdsParams = {
      campaign: {
        objectiveType: "VIDEO_VIEWS",
        campaignName: "test-campaign",
        budget: 100,
        advertiserId: "123456",
        budgetMode: "BUDGET_MODE_TOTAL",
      },
      adGroup: {
        advertiserId: "123456",
        adgroupName: "test-adgroup",
        scheduleStartTime: "2023-01-01 00:00:00",
        scheduleType: "SCHEDULE_START_END",
        billingEvent: "CPV",
        pacing: "PACING_MODE_SMOOTH",
        budget: 50,
        budgetMode: "BUDGET_MODE_TOTAL",
        locationIds: ["6252001"],
        placements: ["PLACEMENT_TIKTOK"],
        scheduleEndTime: "2023-01-02 00:00:00",
        optimizationGoal: "ENGAGED_VIEW",
        bidPrice: 1,
      },
      ad: {
        advertiserId: "123456",
        creatives: [
          {
            adName: "test-ad",
            identityType: "TT_USER",
            adFormat: "SINGLE_VIDEO",
            videoId: "video123",
            identityId: "identity123",
          },
        ],
      },
      companyId: "companyId123",
      credentialId: 1,
    };

    const error = new Error("Failed to create campaign");
    repositoryMock.createCampaign.mockRejectedValue(error);

    await expect(command.execute(params)).rejects.toThrow("Failed to create campaign");

    expect(repositoryMock.createCampaign).toHaveBeenCalledWith(params);
  });
});
