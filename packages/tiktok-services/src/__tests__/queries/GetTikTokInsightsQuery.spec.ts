import { TikTokInsightsRepository } from "../../tiktok-insights-repository";
import type {
  GetTikTokInsightsQueryResponse,
  PartialGetTikTokInsightsQueryResponse,
} from "../../types/tiktok-insights";
import type { GetTikTokInsightsQueryParams } from "../../types/tiktok-insights";
import { GetTikTokInsightsQuery } from "../../queries/get-tiktok-insights.query";
import { HttpClient } from "@meltwater/engage-ads-commons";

jest.mock("../../tiktok-insights-repository");
const TikTokInsightsRepositoryMock = TikTokInsightsRepository as jest.MockedClass<typeof TikTokInsightsRepository>;

jest.mock("@meltwater/engage-ads-commons", () => ({
  HttpClient: jest.fn().mockImplementation(() => ({
    get: jest.fn(),
  })),
}));

describe("GetTikTokInsightsQuery", () => {
  let tikTokInsightsRepositoryMock: jest.Mocked<TikTokInsightsRepository>;
  let getTikTokInsightsQuery: GetTikTokInsightsQuery;

  beforeEach(() => {
    tikTokInsightsRepositoryMock = new TikTokInsightsRepositoryMock(
      new HttpClient({ baseURL: "" }),
    ) as jest.Mocked<TikTokInsightsRepository>;
    getTikTokInsightsQuery = new GetTikTokInsightsQuery(tikTokInsightsRepositoryMock);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return TikTok insights when repository call is successful", async () => {
    const mockInsights: PartialGetTikTokInsightsQueryResponse = {
      insights: [{ video_id: "videoId 1" }, { video_id: "Insight 2" }],
      totalCount: 2,
    };
    tikTokInsightsRepositoryMock.getTikTokInsights.mockResolvedValue(mockInsights as GetTikTokInsightsQueryResponse);

    const params: GetTikTokInsightsQueryParams = {
      startDate: "2023-01-01",
      endDate: "2023-01-31",
      size: 10,
      order: "asc",
      aggregationType: "daily",
      startFrom: 0,
      sortBy: "videoViews",
      profileId: "test-profile-id",
    };

    const result = await getTikTokInsightsQuery.execute(params);
    expect(result).toEqual(mockInsights);
    expect(tikTokInsightsRepositoryMock.getTikTokInsights).toHaveBeenCalledWith(params);
  });

  it("should return TikTok insights when repository call is successful without profileId", async () => {
    const mockInsights: PartialGetTikTokInsightsQueryResponse = {
      insights: [{ video_id: "videoId 1" }, { video_id: "Insight 2" }],
      totalCount: 2,
    };
    tikTokInsightsRepositoryMock.getTikTokInsights.mockResolvedValue(mockInsights as GetTikTokInsightsQueryResponse);

    const params: GetTikTokInsightsQueryParams = {
      startDate: "2023-01-01",
      endDate: "2023-01-31",
      size: 10,
      order: "asc",
      aggregationType: "daily",
      startFrom: 0,
      sortBy: "videoViews",
    };

    const result = await getTikTokInsightsQuery.execute(params);
    expect(result).toEqual(mockInsights);
    expect(tikTokInsightsRepositoryMock.getTikTokInsights).toHaveBeenCalledWith(params);
  });

  it("should return an empty array and 0 totalCount when repository call returns no data", async () => {
    tikTokInsightsRepositoryMock.getTikTokInsights.mockResolvedValue({ insights: [], totalCount: 0 });

    const params: GetTikTokInsightsQueryParams = {
      startDate: "2023-01-01",
      endDate: "2023-01-31",
      size: 10,
      order: "asc",
      aggregationType: "daily",
      startFrom: 0,
      sortBy: "videoViews",
    };

    const result = await getTikTokInsightsQuery.execute(params);
    expect(result).toEqual({
      insights: [],
      totalCount: 0,
    });
    expect(tikTokInsightsRepositoryMock.getTikTokInsights).toHaveBeenCalledWith(params);
  });
});
