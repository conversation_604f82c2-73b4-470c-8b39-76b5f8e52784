import { TikTokAdAccountRepository } from "../../tiktok-ad-account-repository";
import type { TikTokAdAccount } from "../../types/tiktok-ad-account";
import type { ExtendedTikTokAdAccountQueryInput } from "../../types/tiktok-ad-account";
import { GetTikTokAdAccountsQuery } from "../../queries/get-tiktok-ad-accounts.query";
import { HttpClient } from "@meltwater/engage-ads-commons";

jest.mock("../../tiktok-ad-account-repository");
const TikTokAdAccountRepositoryMock = TikTokAdAccountRepository as jest.MockedClass<typeof TikTokAdAccountRepository>;

jest.mock("@meltwater/engage-ads-commons", () => ({
  HttpClient: jest.fn().mockImplementation(() => ({
    post: jest.fn(),
  })),
}));

describe("GetTikTokAdAccountsQuery", () => {
  let tikTokAdAccountRepositoryMock: jest.Mocked<TikTokAdAccountRepository>;
  let getTikTokAdAccountsQuery: GetTikTokAdAccountsQuery;

  beforeEach(() => {
    tikTokAdAccountRepositoryMock = new TikTokAdAccountRepositoryMock(
      new HttpClient({ baseURL: "" }),
    ) as jest.Mocked<TikTokAdAccountRepository>;
    getTikTokAdAccountsQuery = new GetTikTokAdAccountsQuery(tikTokAdAccountRepositoryMock);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return TikTok ad accounts when repository call is successful", async () => {
    const mockAdAccounts: TikTokAdAccount[] = [
      {
        credentialId: 1,
        targetPageName: "Test Page",
        targetPageLogoUrl: "http://example.com/logo.png",
        socialAccountId: "social_account_123",
        associatedProfiles: [],
      },
    ];
    tikTokAdAccountRepositoryMock.getTikTokAdAccounts.mockResolvedValue(mockAdAccounts);

    const params: ExtendedTikTokAdAccountQueryInput = {
      ApplicationCompanyId: "123",
      ChannelId: 1,
      ActiveInd: 1,
      accessToken: "mock-access-token",
    };

    const result = await getTikTokAdAccountsQuery.execute(params);
    expect(result).toEqual(mockAdAccounts);
    expect(tikTokAdAccountRepositoryMock.getTikTokAdAccounts).toHaveBeenCalledWith(
      {
        ApplicationCompanyId: "123",
        ChannelId: 1,
        ActiveInd: 1,
      },
      "mock-access-token",
    );
  });
});
