import { GetTikTokRegionsQuery } from "../../queries/get-tiktok-regions.query";
import { TiktokBoostAdRepository } from "../../tiktok-boost-ad-repository";
import type { GetTiktokRegionParams, GetTiktokRegionResponse } from "../../types/tiktok-region";

jest.mock("../../tiktok-boost-ad-repository");
const TiktokBoostAdRepositoryMock = TiktokBoostAdRepository as jest.MockedClass<typeof TiktokBoostAdRepository>;

jest.mock("@meltwater/engage-ads-commons", () => ({
  HttpClient: jest.fn().mockImplementation(() => ({
    get: jest.fn(),
  })),
}));

describe("GetTikTokRegionsQuery", () => {
  let tiktokBoostAdRepositoryMock: jest.Mocked<TiktokBoostAdRepository>;
  let getTikTokRegionsQuery: GetTikTokRegionsQuery;

  beforeEach(() => {
    tiktokBoostAdRepositoryMock = new TiktokBoostAdRepositoryMock("") as jest.Mocked<TiktokBoostAdRepository>;
    getTikTokRegionsQuery = new GetTikTokRegionsQuery(tiktokBoostAdRepositoryMock);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return TikTok regions when repository call is successful", async () => {
    const mockResponse: GetTiktokRegionResponse = {
      data: [
        {
          locationId: "",
          name: "North America",
          nextLevelIds: [],
          areaType: "",
          level: "",
          regionCode: "",
          parentId: "",
          supportBelow18: true,
        },
      ],
    };

    tiktokBoostAdRepositoryMock.getBoostingRegions.mockResolvedValue(mockResponse);

    const params: GetTiktokRegionParams = {
      advertiserId: "advertiserId",
      placements: [""],
      objectiveType: "",
      level: "",
    };

    const result = await getTikTokRegionsQuery.execute(params);

    expect(result).toEqual(mockResponse);
    expect(tiktokBoostAdRepositoryMock.getBoostingRegions).toHaveBeenCalledWith(params);
  });

  it("should return an empty regions array when repository call returns no data", async () => {
    const mockResponse: GetTiktokRegionResponse = {
      data: [],
    };

    tiktokBoostAdRepositoryMock.getBoostingRegions.mockResolvedValue(mockResponse);

    const params: GetTiktokRegionParams = {
      advertiserId: "advertiserId",
      placements: [""],
      objectiveType: "",
      level: "",
    };

    const result = await getTikTokRegionsQuery.execute(params);

    expect(result).toEqual(mockResponse);
    expect(tiktokBoostAdRepositoryMock.getBoostingRegions).toHaveBeenCalledWith(params);
  });

  it("should handle repository call errors gracefully", async () => {
    const mockError = new Error("Failed to fetch regions");
    tiktokBoostAdRepositoryMock.getBoostingRegions.mockRejectedValue(mockError);

    const params: GetTiktokRegionParams = {
      advertiserId: "advertiserId",
      placements: [""],
      objectiveType: "",
      level: "",
    };

    await expect(getTikTokRegionsQuery.execute(params)).rejects.toThrow("Failed to fetch regions");
    expect(tiktokBoostAdRepositoryMock.getBoostingRegions).toHaveBeenCalledWith(params);
  });
});
