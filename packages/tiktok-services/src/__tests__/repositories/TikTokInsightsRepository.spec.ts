import { HttpClient } from "@meltwater/engage-ads-commons";
import { TikTokInsightsRepository } from "../../tiktok-insights-repository";

jest.mock("@meltwater/engage-ads-commons", () => ({
  HttpClient: jest.fn().mockImplementation(() => ({
    get: jest.fn().mockResolvedValue({
      data: [
        {
          _index: "tik-tok-video-insights",
          _type: "posts",
          _id: "test_id",
          _score: null,
          _source: {
            video_id: "7140854603465198890",
            create_time: "1658605625",
            create_date_time: "2022-07-23",
            company_id: "test_company",
            profile_id: "test_profile",
            video_views: 200,
            likes: 10,
            comments: 15,
            average_watch_time: 32,
            video_length: 32,
            engagements: 25,
            engagement_rate: 3.12,
            reach: 1000,
            full_video_watch_rate: 0.2104,
            total_time_watched: 5794400,
            shares: 5,
            impression_sources: [],
            thumbnail_url: "https://example.com/thumbnail.jpg",
            caption: "Test video",
          },
          sort: [10],
        },
      ],
      message: null,
    }),
    post: jest.fn(),
  })),
}));

jest.mock("@meltwater/engage-ads-db-sdk", () => {
  const mockPost = {
    document: {
      documentId: "7140854603465198890",
      metaData: { url: "https://www.tiktok.com/video/7140854603465198890", source: { id: "profile123" } },
      body: { content: { text: "Test content" }, publishDate: { date: new Date("2023-01-01").getTime() } },
    },
    companyId: "company123",
  };

  return {
    connectToDatabase: jest.fn().mockResolvedValue(true),
    WarpzoneModel: {
      find: jest.fn().mockReturnValue({
        sort: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockResolvedValue([mockPost]),
      }),
      countDocuments: jest.fn().mockResolvedValue(1),
      findOne: jest.fn().mockResolvedValue({
        document: {
          id: "7140854603465198890",
          attachments: [
            {
              link: "https://example.com/thumbnail.jpg",
              mimeType: "video/mp4",
            },
          ],
        },
      }),
    },
  };
});

jest.mock("@meltwater/lambda-monitoring", () => ({
  LambdaTracer: {
    getInstance: jest.fn().mockReturnValue({
      captureMethod: jest.fn().mockImplementation((fn) => fn),
    }),
  },
  LambdaLogger: {
    getInstance: jest.fn().mockReturnValue({
      info: jest.fn(),
      error: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
    }),
  },
}));

describe("TikTokInsightsRepository", () => {
  let repository: TikTokInsightsRepository;
  let httpClient: HttpClient;

  beforeEach(() => {
    // Create HttpClient with mock implementation
    httpClient = new HttpClient({
      baseURL: "http://test-api",
      defaultHeaders: { "Content-Type": "application/json" },
    });
    repository = new TikTokInsightsRepository(httpClient);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return TikTok insights successfully", async () => {
    const result = await repository.getTikTokInsights({
      startDate: "2023-01-01",
      endDate: "2023-01-31",
      size: 10,
      order: "asc",
      aggregationType: "daily",
      startFrom: 0,
    });

    expect(result.insights).toHaveLength(1);
    expect(result.insights[0]).toMatchObject({
      video_id: "7140854603465198890",
      video_views: 200,
      likes: 10,
      comments: 15,
      average_watch_time: 32,
      video_length: 32,
      engagements: 25,
      engagement_rate: 3.12,
      reach: 1000,
      full_video_watch_rate: 0.2104,
      total_time_watched: 5794400,
      shares: 5,
      impression_sources: [],
      thumbnail_url: "https://example.com/thumbnail.jpg",
      mimeType: "video/mp4",
      caption: "Test video",
    });
  });
});
