import { TikTokAdAccountRepository } from "../../tiktok-ad-account-repository";
import { HttpClient } from "@meltwater/engage-ads-commons";
import type { TikTokAdAccountQueryInput, TikTokAdAccountResponse } from "../../types/tiktok-ad-account";

jest.mock("@meltwater/engage-ads-commons", () => ({
  HttpClient: jest.fn().mockImplementation(() => ({
    post: jest.fn(),
  })),
}));

jest.mock("@meltwater/lambda-monitoring", () => ({
  LambdaTracer: {
    getInstance: jest.fn().mockReturnValue({
      captureMethod: jest
        .fn()
        .mockImplementation(() => (target: unknown, propertyKey: string, descriptor: PropertyDescriptor) => descriptor),
    }),
  },
  LambdaLogger: {
    getInstance: jest.fn().mockReturnValue({
      error: jest.fn(),
      info: jest.fn(),
    }),
  },
}));

describe("TikTokAdAccountRepository", () => {
  let httpClientMock: jest.Mocked<HttpClient>;
  let tikTokAdAccountRepository: TikTokAdAccountRepository;
  let mockResponseData: { data: { credentialQuery: Partial<TikTokAdAccountResponse>[] } };

  beforeEach(() => {
    httpClientMock = new HttpClient({ baseURL: "" }) as jest.Mocked<HttpClient>;
    tikTokAdAccountRepository = new TikTokAdAccountRepository(httpClientMock);
    mockResponseData = {
      data: {
        credentialQuery: [
          {
            CredentialId: 1,
            TargetPageName: "Test Page",
            TargetPageLogoUrl: "http://example.com/logo.png",
            SocialAccountId: "social_account_123", // Added field
            associatedCredentials: [
              {
                credentialId: 2,
                targetPageName: "Associated Page",
                socialAccountId: "123456",
                targetPageLogoUrl: "http://example.com/associated-logo.png",
                tikTokIdentity: {
                  identity_id: "123",
                  identity_type: "TT_USER",
                  identity_authorized_bc_id: "123",
                },
              },
            ],
          },
        ],
      },
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should return an array of TikTok ad accounts including SocialAccountId when API call is successful", async () => {
    httpClientMock.post.mockResolvedValue(mockResponseData);

    const input: TikTokAdAccountQueryInput = {
      ApplicationCompanyId: "123",
      ChannelId: 11,
      ActiveInd: 1,
    };
    const accessToken = "mock-access-token";

    const adAccounts = await tikTokAdAccountRepository.getTikTokAdAccounts(input, accessToken);
    expect(adAccounts).toHaveLength(1);
    expect(adAccounts[0]).toEqual({
      credentialId: 1,
      targetPageName: "Test Page",
      targetPageLogoUrl: "http://example.com/logo.png",
      socialAccountId: "social_account_123", // Verify inclusion
      associatedProfiles: [
        {
          credentialId: 2,
          targetPageName: "Associated Page",
          socialAccountId: "123456",
          targetPageLogoUrl: "http://example.com/associated-logo.png",
          identityId: "123",
          identityType: "TT_USER",
          identityAuthorizedBcId: "123",
        },
      ],
    });
    expect(httpClientMock.post).toHaveBeenCalledWith("/graphql", expect.any(Object), {
      headers: { Authorization: `${accessToken}` },
    });
  });

  it("should not return an array of TikTok associated credentials because it doesn't have the right identity type", async () => {
    httpClientMock.post.mockResolvedValue(mockResponseData);

    const input: TikTokAdAccountQueryInput = {
      ApplicationCompanyId: "123",
      ChannelId: 11,
      ActiveInd: 1,
    };
    const accessToken = "mock-access-token";
    if (mockResponseData.data.credentialQuery[0].associatedCredentials) {
      mockResponseData.data.credentialQuery[0].associatedCredentials[0].tikTokIdentity.identity_type = "BC_AUTH_TT";
    }
    const adAccounts = await tikTokAdAccountRepository.getTikTokAdAccounts(input, accessToken);
    expect(adAccounts).toHaveLength(1);
    expect(adAccounts[0]).toEqual({
      credentialId: 1,
      targetPageName: "Test Page",
      targetPageLogoUrl: "http://example.com/logo.png",
      socialAccountId: "social_account_123", // Verify inclusion
      associatedProfiles: [
        {
          credentialId: 2,
          targetPageName: "Associated Page",
          socialAccountId: "123456",
          targetPageLogoUrl: "http://example.com/associated-logo.png",
          identityId: "123",
          identityType: "BC_AUTH_TT",
          identityAuthorizedBcId: "123",
        },
      ],
    });
    expect(httpClientMock.post).toHaveBeenCalledWith("/graphql", expect.any(Object), {
      headers: { Authorization: `${accessToken}` },
    });
  });
});
