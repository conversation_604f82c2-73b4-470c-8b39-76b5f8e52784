import { TiktokBoostAdRepository } from "../../tiktok-boost-ad-repository";
import { CampaignApi } from "../../sdk/CampaignApi";
import { AdGroupApi } from "../../sdk/AdGroupApi";
import { AdApi } from "../../sdk/AdApi";
import type {
  TiktokSparkAdsParams,
  CreateCampaignApiResponse,
  CreateAdGroupApiResponse,
  CreateAdApiResponse,
  TiktokCampaignData,
  CreateTiktokAdGroupApiParams,
} from "../../types/tiktok-spark-ads";

jest.mock("../../sdk/CampaignApi");
jest.mock("../../sdk/AdGroupApi");
jest.mock("../../sdk/AdApi");

jest.mock("@meltwater/lambda-monitoring", () => ({
  LambdaTracer: {
    getInstance: jest.fn().mockReturnValue({
      captureMethod: jest
        .fn()
        .mockImplementation(() => (target: unknown, propertyKey: string, descriptor: PropertyDescriptor) => descriptor),
      putMetadata: jest.fn(),
    }),
  },
  LambdaLogger: {
    getInstance: jest.fn().mockReturnValue({
      error: jest.fn(),
      info: jest.fn(),
      debug: jest.fn(),
    }),
  },
}));

describe("TiktokBoostAdRepository", () => {
  let campaignApiMock: jest.Mocked<CampaignApi>;
  let adGroupApiMock: jest.Mocked<AdGroupApi>;
  let adApiMock: jest.Mocked<AdApi>;
  let repository: TiktokBoostAdRepository;

  beforeEach(() => {
    jest.clearAllMocks();

    campaignApiMock = {
      createCampaign: jest.fn(),
    } as unknown as jest.Mocked<CampaignApi>;

    adGroupApiMock = {
      createAdGroup: jest.fn(),
    } as unknown as jest.Mocked<AdGroupApi>;

    adApiMock = {
      createAd: jest.fn(),
    } as unknown as jest.Mocked<AdApi>;

    (CampaignApi as jest.MockedClass<typeof CampaignApi>).mockImplementation(() => campaignApiMock);
    (AdGroupApi as jest.MockedClass<typeof AdGroupApi>).mockImplementation(() => adGroupApiMock);
    (AdApi as jest.MockedClass<typeof AdApi>).mockImplementation(() => adApiMock);

    repository = new TiktokBoostAdRepository("test-access-token");
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("createCampaign", () => {
    it("should create a campaign and return campaignId", async () => {
      const payload: TiktokSparkAdsParams = {
        campaign: {
          objectiveType: "VIDEO_VIEWS",
          campaignName: "test-campaign",
          budget: 100,
          advertiserId: "123456",
          budgetMode: "BUDGET_MODE_TOTAL",
        },
        adGroup: {
          advertiserId: "123456",
          adgroupName: "test-adgroup",
          scheduleStartTime: "2023-01-01 00:00:00",
          scheduleType: "SCHEDULE_START_END",
          billingEvent: "CPV",
          pacing: "PACING_MODE_SMOOTH",
          budget: 50,
          budgetMode: "BUDGET_MODE_TOTAL",
          locationIds: ["6252001"],
          placements: ["PLACEMENT_TIKTOK"],
          scheduleEndTime: "2023-01-02 00:00:00",
          optimizationGoal: "ENGAGED_VIEW",
          bidPrice: 1,
        },
        ad: {
          advertiserId: "123456",
          creatives: [
            {
              adName: "test-ad",
              identityType: "TT_USER",
              adFormat: "SINGLE_VIDEO",
              videoId: "video123",
              identityId: "identity123",
            },
          ],
        },
        companyId: "companyId123",
        credentialId: 1,
      };

      const mockCampaignData: TiktokCampaignData = {
        operation_status: "ENABLE",
        modify_time: "2023-01-01 00:00:00",
        create_time: "2023-01-01 00:00:00",
        campaign_id: "campaign123",
        roas_bid: 0,
        campaign_name: "test-campaign",
        campaign_type: "REGULAR_CAMPAIGN",
        is_new_structure: true,
        advertiser_id: "123456",
        is_smart_performance_campaign: false,
        objective: "VIDEO_VIEWS",
        secondary_status: "CAMPAIGN_STATUS_ENABLE",
        budget_mode: "BUDGET_MODE_TOTAL",
        budget: 100,
        objective_type: "VIDEO_VIEWS",
        deep_bid_type: null,
      };

      const mockResponse: CreateCampaignApiResponse = {
        code: 0,
        message: "OK",
        request_id: "test-request-id",
        data: mockCampaignData,
      };

      campaignApiMock.createCampaign.mockResolvedValue(mockResponse);

      const result = await repository.createCampaign(payload);

      expect(result).toEqual({
        campaignId: "campaign123",
        advertiserId: "123456",
        campaignName: "test-campaign",
      });

      expect(campaignApiMock.createCampaign).toHaveBeenCalledWith({
        advertiser_id: "123456",
        budget: 100,
        campaign_name: "test-campaign",
        objective_type: "VIDEO_VIEWS",
        budget_mode: "BUDGET_MODE_TOTAL",
        budget_optimize_on: false,
      });
    });
  });

  describe("createAdGroup", () => {
    it("should create a video views ad group and return adgroupId", async () => {
      const payload: TiktokSparkAdsParams & { campaignId: string } = {
        campaign: {
          objectiveType: "VIDEO_VIEWS",
          campaignName: "test-campaign",
          budget: 100,
          advertiserId: "123456",
          budgetMode: "BUDGET_MODE_TOTAL",
        },
        adGroup: {
          advertiserId: "123456",
          adgroupName: "test-adgroup",
          scheduleStartTime: "2023-01-01 00:00:00",
          scheduleType: "SCHEDULE_START_END",
          billingEvent: "CPV",
          pacing: "PACING_MODE_SMOOTH",
          budget: 50,
          budgetMode: "BUDGET_MODE_TOTAL",
          locationIds: ["6252001"],
          placements: ["PLACEMENT_TIKTOK"],
          scheduleEndTime: "2023-01-02 00:00:00",
          optimizationGoal: "ENGAGED_VIEW",
          bidPrice: 1,
        },
        ad: {
          advertiserId: "123456",
          creatives: [
            {
              adName: "test-ad",
              identityType: "TT_USER",
              adFormat: "SINGLE_VIDEO",
              videoId: "video123",
              identityId: "identity123",
            },
          ],
        },
        companyId: "companyId123",
        credentialId: 1,
        campaignId: "campaign123",
      };

      const mockAdGroupData: CreateTiktokAdGroupApiParams & { id: string; adgroup_id: string } = {
        advertiser_id: "123456",
        campaign_id: "campaign123",
        adgroup_name: "test-adgroup",
        schedule_start_time: "2023-01-01 00:00:00",
        schedule_type: "SCHEDULE_START_END",
        billing_event: "CPV",
        pacing: "PACING_MODE_SMOOTH",
        budget: 50,
        budget_mode: "BUDGET_MODE_TOTAL",
        location_ids: ["6252001"],
        placements: ["PLACEMENT_TIKTOK"],
        schedule_end_time: "2023-01-02 00:00:00",
        optimization_goal: "ENGAGED_VIEW",
        bid_price: 1,
        id: "adgroup123",
        adgroup_id: "adgroup123",
      };

      const mockResponse: CreateAdGroupApiResponse = {
        code: 0,
        message: "OK",
        request_id: "test-request-id",
        data: mockAdGroupData,
      };

      adGroupApiMock.createAdGroup.mockResolvedValue(mockResponse);

      const result = await repository.createAdGroup(payload);

      expect(result).toEqual({
        adgroupId: "adgroup123",
        advertiserId: "123456",
        campaignId: "campaign123",
        adgroupName: "test-adgroup",
        scheduleEndTime: "2023-01-02 00:00:00",
      });

      expect(adGroupApiMock.createAdGroup).toHaveBeenCalledWith({
        advertiser_id: "123456",
        campaign_id: "campaign123",
        adgroup_name: "test-adgroup",
        schedule_start_time: "2023-01-01 00:00:00",
        schedule_type: "SCHEDULE_START_END",
        billing_event: "CPV",
        pacing: "PACING_MODE_SMOOTH",
        budget: 50,
        budget_mode: "BUDGET_MODE_TOTAL",
        location_ids: ["6252001"],
        placements: ["PLACEMENT_TIKTOK"],
        schedule_end_time: "2023-01-02 00:00:00",
        optimization_goal: "ENGAGED_VIEW",
        bid_price: 1,
      });
    });

    it("should create an engagement ad group and return adgroupId", async () => {
      const payload: TiktokSparkAdsParams & { campaignId: string } = {
        campaign: {
          objectiveType: "ENGAGEMENT",
          campaignName: "test-campaign",
          budget: 100,
          advertiserId: "123456",
          budgetMode: "BUDGET_MODE_TOTAL",
        },
        adGroup: {
          advertiserId: "123456",
          adgroupName: "test-adgroup",
          scheduleStartTime: "2023-01-01 00:00:00",
          scheduleType: "SCHEDULE_START_END",
          billingEvent: "CPV",
          pacing: "PACING_MODE_SMOOTH",
          budget: 50,
          budgetMode: "BUDGET_MODE_TOTAL",
          locationIds: ["6252001"],
          placements: ["PLACEMENT_TIKTOK"],
          scheduleEndTime: "2023-01-02 00:00:00",
          optimizationGoal: "ENGAGED_VIEW",
          bidPrice: 1,
          conversionBidPrice: 1,
        },
        ad: {
          advertiserId: "123456",
          creatives: [
            {
              adName: "test-ad",
              identityType: "TT_USER",
              adFormat: "SINGLE_VIDEO",
              videoId: "video123",
              identityId: "identity123",
            },
          ],
        },
        companyId: "companyId123",
        credentialId: 1,
        campaignId: "campaign123",
      };

      const mockAdGroupData: CreateTiktokAdGroupApiParams & { id: string; adgroup_id: string } = {
        advertiser_id: "123456",
        campaign_id: "campaign123",
        adgroup_name: "test-adgroup",
        schedule_start_time: "2023-01-01 00:00:00",
        schedule_type: "SCHEDULE_START_END",
        billing_event: "CPV",
        pacing: "PACING_MODE_SMOOTH",
        budget: 50,
        budget_mode: "BUDGET_MODE_TOTAL",
        location_ids: ["6252001"],
        placements: ["PLACEMENT_TIKTOK"],
        schedule_end_time: "2023-01-02 00:00:00",
        optimization_goal: "ENGAGED_VIEW",
        bid_price: 1,
        id: "adgroup123",
        adgroup_id: "adgroup123",
        conversion_bid_price: 1,
      };

      const mockResponse: CreateAdGroupApiResponse = {
        code: 0,
        message: "OK",
        request_id: "test-request-id",
        data: mockAdGroupData,
      };

      adGroupApiMock.createAdGroup.mockResolvedValue(mockResponse);

      const result = await repository.createAdGroup(payload);

      expect(result).toEqual({
        adgroupId: "adgroup123",
        adgroupName: "test-adgroup",
        advertiserId: "123456",
        campaignId: "campaign123",
        scheduleEndTime: "2023-01-02 00:00:00",
      });

      expect(adGroupApiMock.createAdGroup).toHaveBeenCalledWith({
        advertiser_id: "123456",
        campaign_id: "campaign123",
        adgroup_name: "test-adgroup",
        schedule_start_time: "2023-01-01 00:00:00",
        schedule_type: "SCHEDULE_START_END",
        billing_event: "CPV",
        pacing: "PACING_MODE_SMOOTH",
        budget: 50,
        budget_mode: "BUDGET_MODE_TOTAL",
        location_ids: ["6252001"],
        placements: ["PLACEMENT_TIKTOK"],
        schedule_end_time: "2023-01-02 00:00:00",
        optimization_goal: "ENGAGED_VIEW",
        bid_price: 1,
        conversion_bid_price: 1,
      });
    });
  });

  describe("createAd", () => {
    it("should create an ad and return adId", async () => {
      const payload: TiktokSparkAdsParams & { adgroupId: string; campaignId: string } = {
        campaign: {
          objectiveType: "VIDEO_VIEWS",
          campaignName: "test-campaign",
          budget: 100,
          advertiserId: "123456",
          budgetMode: "BUDGET_MODE_TOTAL",
        },
        adGroup: {
          advertiserId: "123456",
          adgroupName: "test-adgroup",
          scheduleStartTime: "2023-01-01 00:00:00",
          scheduleType: "SCHEDULE_START_END",
          billingEvent: "CPV",
          pacing: "PACING_MODE_SMOOTH",
          budget: 50,
          budgetMode: "BUDGET_MODE_TOTAL",
          locationIds: ["6252001"],
          placements: ["PLACEMENT_TIKTOK"],
          scheduleEndTime: "2023-01-02 00:00:00",
          optimizationGoal: "ENGAGED_VIEW",
          bidPrice: 1,
        },
        ad: {
          advertiserId: "123456",
          creatives: [
            {
              adName: "test-ad",
              identityType: "TT_USER",
              adFormat: "SINGLE_VIDEO",
              videoId: "video123",
              identityId: "identity123",
            },
          ],
        },
        companyId: "companyId123",
        credentialId: 1,
        adgroupId: "adgroup123",
        campaignId: "campaign123",
      };

      const mockResponse: CreateAdApiResponse = {
        code: 0,
        message: "OK",
        request_id: "test-request-id",
        data: {
          ad_ids: ["ad123"], // Adjusted adId
        },
      };

      adApiMock.createAd.mockResolvedValue(mockResponse);

      const result = await repository.createAd(payload);

      expect(result).toEqual({
        adId: "ad123",
        advertiserId: "123456",
        adgroupId: "adgroup123",
        campaignId: "campaign123",
        adName: "test-ad",
      });

      expect(adApiMock.createAd).toHaveBeenCalledWith({
        advertiser_id: "123456",
        adgroup_id: "adgroup123",
        creatives: [
          {
            ad_name: "test-ad",
            identity_type: "TT_USER",
            identity_id: "identity123",
            ad_format: "SINGLE_VIDEO",
            tiktok_item_id: "video123",
          },
        ],
      });
    });
  });
});
