import { Lamb<PERSON><PERSON>og<PERSON>, LambdaTracer } from "@meltwater/lambda-monitoring";
import type { ICommand } from "@meltwater/cqrs";
import type { TiktokBoostAdRepository } from "../tiktok-boost-ad-repository";
import type {
  TiktokSparkAdsParams,
  CreateTiktokSparkAdResponse,
  CreateTiktokCampaignResponse,
  CreateTiktokAdGroupResponse,
  CreateTiktokAdResponse,
} from "../types/tiktok-spark-ads";

const tracer = LambdaTracer.getInstance();
const logger = LambdaLogger.getInstance();

export class CreateSparkAdCommand implements ICommand<TiktokSparkAdsParams, CreateTiktokSparkAdResponse> {
  constructor(private repository: TiktokBoostAdRepository) {}

  @tracer.captureMethod({ subSegmentName: "CreateSparkAdCommand:execute" })
  public async execute(params: TiktokSparkAdsParams): Promise<CreateTiktokSparkAdResponse> {
    tracer.putMetadata("params", params);
    try {
      let campaign: CreateTiktokCampaignResponse;

      if (params.campaign.campaignId) {
        campaign = {
          campaignId: params.campaign.campaignId,
          advertiserId: params.campaign.advertiserId,
        };
      } else {
        campaign = await this.repository.createCampaign(params);
      }

      const adGroup: CreateTiktokAdGroupResponse = await this.repository.createAdGroup({
        ...params,
        campaignId: campaign.campaignId || "",
      });

      const ad: CreateTiktokAdResponse = await this.repository.createAd({
        ...params,
        adgroupId: adGroup.adgroupId || "",
        campaignId: campaign.campaignId || "",
      });

      return {
        campaign,
        adGroup,
        ad,
      };
    } catch (error) {
      logger.info("Error during tiktok boosting", { error });
      throw error;
    }
  }
}
