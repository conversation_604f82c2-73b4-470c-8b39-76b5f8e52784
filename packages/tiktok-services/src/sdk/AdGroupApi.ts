import { Tik<PERSON><PERSON><PERSON><PERSON> } from "./TikTokApi";
import type { CreateTiktokAdGroupApiParams, CreateAdGroupApiResponse } from "../types/tiktok-spark-ads";

export class <PERSON><PERSON>roup<PERSON><PERSON> extends TikTokApi {
  constructor(accessToken: string) {
    super(accessToken);
  }

  async createAdGroup(params: CreateTiktokAdGroupApiParams): Promise<CreateAdGroupApiResponse> {
    const endpoint = "/adgroup/create/";
    return await this.httpClient.post<CreateTiktokAdGroupApiParams, CreateAdGroupApiResponse>(endpoint, params);
  }
}
