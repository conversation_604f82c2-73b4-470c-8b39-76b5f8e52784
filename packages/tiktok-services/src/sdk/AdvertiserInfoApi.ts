import { Tik<PERSON><PERSON><PERSON><PERSON> } from "./TikTokApi";
import type { GetTiktokAdvertiserAPIParams, GetTiktokAdvertiserInfoAPIResponse } from "../types/tiktok-advertiser";

export class AdvertiserIn<PERSON><PERSON><PERSON> extends Tik<PERSON><PERSON><PERSON><PERSON> {
  constructor(accessToken: string) {
    super(accessToken);
  }

  async getAdvertiserInfo(params: GetTiktokAdvertiserAPIParams): Promise<GetTiktokAdvertiserInfoAPIResponse> {
    const endpoint = "/advertiser/info/";
    return await this.httpClient.get<GetTiktokAdvertiserInfoAPIResponse>(endpoint, {
      params,
      paramsSerializer: this.objectToQueryString,
    });
  }

  objectToQueryString(params: { [key: string]: object | string }): string {
    const queryParts: string[] = [];

    for (const key in params) {
      if (Object.prototype.hasOwnProperty.call(params, key)) {
        let value = params[key];
        if (Array.isArray(value)) {
          value = `["${value.join('","')}"]`;
        }
        queryParts.push(`${key}=${value}`);
      }
    }

    return queryParts.join("&");
  }
}
