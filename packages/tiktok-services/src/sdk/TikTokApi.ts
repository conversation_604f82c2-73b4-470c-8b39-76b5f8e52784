import { HttpClient } from "@meltwater/engage-ads-commons";

export class TikTokApi {
  public httpClient: HttpClient;
  public baseURL = "https://business-api.tiktok.com/open_api/v1.3/";

  constructor(accessToken: string) {
    this.httpClient = new HttpClient({
      baseURL: process.env.TIKTOK_API_CUSTOM_URL || this.baseURL,
      defaultHeaders: {
        "Access-Token": accessToken,
        "Content-Type": "application/json",
      },
    });
  }
}
