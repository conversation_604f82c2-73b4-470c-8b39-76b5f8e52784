import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "./TikTok<PERSON>pi";
import type { GetTiktokRegionAPIParams, GetTiktokRegionAPIResponse } from "../types/tiktok-region";

export class RegionApi extends Tik<PERSON><PERSON><PERSON><PERSON> {
  constructor(accessToken: string) {
    super(accessToken);
  }

  async getRegion(params: GetTiktokRegionAPIParams): Promise<GetTiktokRegionAPIResponse> {
    const endpoint = "/tool/region/";
    return await this.httpClient.get<GetTiktokRegionAPIResponse>(endpoint, {
      params,
      paramsSerializer: this.objectToQueryString,
    });
  }

  objectToQueryString(params: { [key: string]: object | string }): string {
    const queryParts: string[] = [];

    for (const key in params) {
      if (Object.prototype.hasOwnProperty.call(params, key)) {
        let value = params[key];
        if (Array.isArray(value)) {
          value = `["${value.join('","')}"]`;
        }
        queryParts.push(`${key}=${value}`);
      }
    }

    return queryParts.join("&");
  }
}
