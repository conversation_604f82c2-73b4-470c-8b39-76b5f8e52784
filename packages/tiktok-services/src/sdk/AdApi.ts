import { Tik<PERSON><PERSON><PERSON><PERSON> } from "./TikTokApi";
import type { CreateTiktokAdApiParams, CreateAdApiResponse } from "../types/tiktok-spark-ads";

export class Ad<PERSON>pi extends TikTok<PERSON><PERSON> {
  constructor(accessToken: string) {
    super(accessToken);
  }

  async createAd(params: CreateTiktokAdApiParams): Promise<CreateAdApiResponse> {
    const endpoint = "/ad/create/";
    return await this.httpClient.post<CreateTiktokAdApiParams, CreateAdApiResponse>(endpoint, params);
  }
}
