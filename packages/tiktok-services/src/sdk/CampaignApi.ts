import { Tik<PERSON><PERSON><PERSON><PERSON> } from "./TikTokApi";
import type { CreateTiktokCampaignApiParams, CreateCampaignApiResponse } from "../types/tiktok-spark-ads";

export class Campaign<PERSON>pi extends TikTokApi {
  constructor(accessToken: string) {
    super(accessToken);
  }

  async createCampaign(params: CreateTiktokCampaignApiParams): Promise<CreateCampaignApiResponse> {
    const endpoint = "/campaign/create/";
    return await this.httpClient.post<CreateTiktokCampaignApiParams, CreateCampaignApiResponse>(endpoint, params);
  }
}
