export interface TikTokAdAccount {
  credentialId: number;
  targetPageName: string;
  targetPageLogoUrl: string | null;
  socialAccountId: string;
  associatedProfiles: TikTokProfile[];
}

export interface TikTokProfile {
  credentialId: number;
  targetPageName: string;
  socialAccountId: string;
  targetPageLogoUrl: string | null;
  identityId: string;
  identityType: string;
  identityAuthorizedBcId: string;
}

export interface TikTokAdAccountQueryInput {
  ApplicationCompanyId: string;
  ChannelId: number;
  ActiveInd: number;
}

export interface GraphQLResponseWrapper {
  credentialQuery: Array<TikTokAdAccountResponse>;
}

export interface HttpClientResponse<T> {
  data: T;
  status?: number;
  headers?: {
    Authorization: string;
    [key: string]: string;
  };
}

export interface TikTokAdAccountResponse {
  CredentialId: number;
  TargetPageName: string;
  TargetPageLogoUrl: string | null;
  SocialAccountId: string;
  associatedCredentials: Array<TikTokProfileResponse>;
}

export interface TikTokProfileResponse {
  credentialId: number;
  targetPageName: string;
  socialAccountId: string;
  targetPageLogoUrl: string | null;
  tikTokIdentity: {
    identity_id: string;
    identity_type: string;
    identity_authorized_bc_id: string;
  };
}

export interface GraphQLRequest {
  query: string;
  variables: {
    query: TikTokAdAccountQueryInput;
  };
}

export interface ExtendedTikTokAdAccountQueryInput extends TikTokAdAccountQueryInput {
  accessToken: string;
}
