export interface GetTikTokInsightsQueryParams {}

export interface TiktokSparkAdsParams {
  campaign: TiktokCampaign;
  adGroup: TiktokAdGroup;
  ad: TiktokAd;
  companyId: string;
  credentialId: number;
}

export interface TiktokCampaign {
  campaignId?: string;
  objectiveType: string;
  campaignName: string;
  budget: number;
  advertiserId: string;
  budgetMode: string;
}

export interface TiktokAdGroup {
  advertiserId: string;
  adgroupName: string;
  scheduleStartTime: string;
  scheduleType: string;
  billingEvent: string;
  pacing: string;
  budget: number;
  budgetMode: string;
  locationIds: string[];
  placements: string[];
  scheduleEndTime: string;
  optimizationGoal: string;
  bidPrice: number;
  frequency?: number;
  frequencySchedule?: number;
  conversionBidPrice?: number;
  promotionType?: string;
}

export interface TiktokCreative {
  adName: string;
  identityType: string;
  identityId: string;
  adFormat: string;
  videoId: string;
  callToAction?: string;
  landingPageUrl?: string;
  identityAuthorizedBcId?: string;
}

export interface TiktokAd {
  advertiserId: string;
  creatives: TiktokCreative[];
}

export interface CreateTiktokCampaignApiParams {
  advertiser_id: string;
  campaign_name: string;
  objective_type: string;

  budget?: number;
  budget_mode: string;
  budget_optimize_on: boolean;
}

export interface CreateTiktokAdGroupApiParams {
  advertiser_id: string;
  campaign_id: string;
  adgroup_name: string;
  schedule_start_time: string;
  schedule_type?: string;
  billing_event: string;
  pacing: string;
  budget: number;
  budget_mode: string;
  location_ids: string[];
  placements: string[];
  schedule_end_time?: string;
  optimization_goal: string;
  bid_price: number;
  frequency?: number;
  frequency_schedule?: number;
  conversion_bid_price?: number;
  promotion_type?: string;
}

export interface CreateTiktokAdApiParams {
  advertiser_id: string;
  adgroup_id: string;
  call_to_action?: string;
  creatives: {
    ad_name: string;
    identity_type: string;
    identity_id: string;
    ad_format: string;
    video_id?: string;
    tiktok_item_id?: string;
    call_to_action?: string;
    landing_page_url?: string;
    identity_authorized_bc_id?: string;
  }[];
}

export interface CreateCampaignApiResponse {
  code: number;
  message: string;
  request_id: string;
  data: TiktokCampaignData;
}

export interface CreateAdGroupApiResponse {
  code: number;
  message: string;
  request_id: string;
  data: CreateTiktokAdGroupApiParams & { id: string; adgroup_id: string };
}

export interface CreateAdApiResponse {
  code: number;
  message: string;
  request_id: string;
  data: {
    ad_ids: string[];
  };
}

export interface CreateTiktokSparkAdResponse {
  campaignId?: string;
  campaign?: CreateTiktokCampaignResponse;
  adGroup?: CreateTiktokAdGroupResponse;
  ad?: CreateTiktokAdResponse;
  metadata?: {
    error?: {
      name: string;
      message: string;
    };
  };
}

export interface CreateTiktokCampaignResponse {
  campaignId?: string;
  campaignName?: string;
  advertiserId?: string;
  // Include other campaign fields as needed
}

export interface CreateTiktokAdGroupResponse {
  adgroupId?: string;
  adgroupName?: string;
  advertiserId?: string;
  campaignId?: string;
  scheduleEndTime?: string;
  // Include other ad group fields as needed
}

export interface CreateTiktokAdResponse {
  adId?: string;
  adName?: string;
  advertiserId?: string;
  campaignId?: string;
  adgroupId?: string;
  // Include other ad fields as needed
}

export interface TiktokCampaignData {
  operation_status: "ENABLE" | "DISABLE";
  modify_time: string;
  create_time: string;
  campaign_id: string;
  roas_bid: number;
  campaign_name: string;
  campaign_type: "REGULAR_CAMPAIGN" | "IOS14_CAMPAIGN";
  is_new_structure: boolean;
  advertiser_id: string;
  is_smart_performance_campaign: boolean;
  objective: string;
  secondary_status: "CAMPAIGN_STATUS_ENABLE" | "CAMPAIGN_STATUS_DISABLE";
  budget_mode: "BUDGET_MODE_DAY" | "BUDGET_MODE_TOTAL" | "BUDGET_MODE_INFINITE";
  budget: number;
  objective_type: string;
  deep_bid_type: string | null;
}
