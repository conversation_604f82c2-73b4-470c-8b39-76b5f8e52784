export interface CampaignRecord {
  campaignId: string;
  campaignName: string;
}

export interface GraphQLRequestBody {
  query: string;
  variables: {
    input: {
      advertiserIds: string[];
    };
  };
}

export interface GraphQLGetCampaignsResponse {
  data: {
    queryCampaigns: {
      campaignId: string;
      campaignName: string;
    }[];
  };
}

export interface GetCampaignsQueryParams {
  advertiserIds: string[];
}

export interface Campaign {
  campaignId: string;
  campaignName: string;
}
