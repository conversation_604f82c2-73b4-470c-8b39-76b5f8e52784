export interface ImpressionSourceWildcards {
  percentage: number;
  impression_source: string;
}

export interface TikTokInsights {
  document_id?: string;
  permalink?: string;
  video_id: string;
  video_views: number;
  likes: number;
  comments: number;
  average_watch_time: number;
  video_length: number;
  engagements: number;
  engagement_rate: number;
  reach: number;
  full_video_watch_rate: number;
  total_time_watched: number;
  shares: number;
  impression_sources: ImpressionSourceWildcards[];
  thumbnail_url: string;
  mimeType?: string;
  caption: string;
  create_time: string;
  company_id: string;
  profile_id: string;
  channel: string;
}

export interface PartialGetTikTokInsightsQueryResponse {
  insights: Partial<TikTokInsights>[];
  totalCount: number;
}

export interface GetTikTokInsightsQueryResponse {
  insights: TikTokInsights[];
  totalCount: number;
}

export interface GetTikTokInsightsQueryParams {
  startDate: string;
  endDate: string;
  size?: number;
  order?: string;
  aggregationType?: string;
  startFrom?: number;
  sortBy?: string;
  profileId?: string;
  profileIds?: string[];
  match?: { captionKeyword?: string };
  sortByMetric?: boolean;
}

export interface WarpzoneQuery {
  "document.body.publishDate.date"?: {
    $gte: number;
    $lte: number;
  };
  "document.metaData.source.id"?: {
    $in: string[];
  };
  "document.metaData.url"?: {
    $in: RegExp[];
  };
  channel?: string;
  $text?: {
    $search: string;
    $language?: string;
    $caseSensitive?: boolean;
    $diacriticSensitive?: boolean;
  };
}

export interface WildcardsResponse {
  data: Array<{
    _index: string;
    _type: string;
    _id: string;
    _score: null;
    _source: {
      video_id: string;
      create_time: string;
      create_date_time: string;
      company_id: string;
      profile_id: string;
      likes: number;
      average_watch_time: number;
      video_length: number;
      engagements: number;
      engagement_rate: number;
      reach: number;
      thumbnail_url: string;
      caption: string;
      comments: number;
      video_views: number;
      full_video_watch_rate: number;
      total_time_watched: number;
      shares: number;
      impression_sources: ImpressionSourceWildcards[];
    };
    sort: number[];
  }>;
  message: string | null;
}
