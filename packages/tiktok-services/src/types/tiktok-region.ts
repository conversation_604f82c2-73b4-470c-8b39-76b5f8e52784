export interface GetTiktokRegionParams {
  advertiserId: string;
  placements: string[];
  objectiveType: string;
  level: string;
}

export interface GetTiktokRegionAPIParams {
  advertiser_id: string;
  placements: string[];
  objective_type: string;
  level_range: string;
}

export interface GetTiktokRegionResponse {
  data: RegionInfoResponse[];
}

interface RegionInfoResponse {
  nextLevelIds: string[];
  areaType: string;
  level: string;
  regionCode: string;
  locationId: string;
  name: string;
  parentId: string;
  supportBelow18: boolean;
}

export interface GetTiktokRegionAPIResponse {
  code: number;
  message: string;
  request_id: string;
  data: TiktokResultData;
}

export interface TiktokResultData {
  region_info: RegionInfo[];
  region_list: string[];
}

export interface RegionInfo {
  next_level_ids: string[];
  area_type: string;
  level: string;
  region_code: string;
  location_id: string;
  name: string;
  parent_id: string;
  support_below_18: boolean;
}
