export interface GetTiktokAdvertiserParams {
  advertiserIds: string[];
}

export interface GetTiktokAdvertiserAPIParams {
  advertiser_ids: string[];
}

export interface GetTiktokAdvertiserInfoResponse {
  data: AdvertiserInfoResponse[];
}

interface AdvertiserInfoResponse {
  advertiserId: string;
  timezone: string;
  currency: string;
}

export interface GetTiktokAdvertiserInfoAPIResponse {
  code: number;
  message: string;
  request_id: string;
  data: TiktokResultData;
}

export interface TiktokResultData {
  list: AdvertiserInfo[];
}

export interface AdvertiserInfo {
  telephone_number: string;
  license_city: string | null;
  advertiser_id: string;
  description: string;
  license_province: string | null;
  brand: string | null;
  advertiser_account_type: string;
  email: string;
  balance: number;
  license_url: string | null;
  status: string;
  create_time: number;
  address: string | null;
  currency: string;
  company: string;
  display_timezone: string;
  promotion_center_province: string | null;
  contacter: string;
  language: string;
  role: string;
  promotion_area: string;
  rejection_reason: string | null;
  cellphone_number: string;
  timezone: string;
  industry: string;
  license_no: string;
  owner_bc_id: string;
  country: string;
  name: string;
  promotion_center_city: string | null;
}
