import type {
  CreateTiktokAdApiParams,
  CreateTiktokAdGroupApiParams,
  CreateTiktokCampaignApiParams,
} from "./tiktok-spark-ads";

export type BusinessAPISDK = {
  ToolApi: ObjectConstructor;
  CampaignCreationApi: ObjectConstructor;
  AdgroupApi: ObjectConstructor;
  AdApi: ObjectConstructor;
  ApiClient: typeof APIClient;
};

class APIClient {
  constructor() {}
  public basePath: string = "";
}

export interface CreateCampaignApi<T> {
  campaignCreate(
    accessToken: string,
    opts: { body: CreateTiktokCampaignApiParams },
    cb: (error: Error, data: T) => void,
  ): void;
}

export interface AdGroupApi<T> {
  adgroupCreate(
    accessToken: string,
    opts: { body: CreateTiktokAdGroupApiParams },
    cb: (error: Error, data: T) => void,
  ): void;
}

export interface AdApi<T> {
  adCreate(accessToken: string, opts: { body: CreateTiktokAdApiParams }, cb: (error: Error, data: T) => void): void;
}
