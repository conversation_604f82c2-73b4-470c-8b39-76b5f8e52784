# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [2.13.1-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.13.0...@meltwater/engage-ads-tiktok-services@2.13.1-ENGAGE.0) (2024-11-04)

### Features

- adding support for returning scheduleEndTime for boosted ad ([daa1021](https://github.com/meltwater/engage-ads-backend/commit/daa102117175c8f483ed45c184e61866b69c64f3))

# [2.13.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.12.3...@meltwater/engage-ads-tiktok-services@2.13.0) (2024-11-03)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.12.5-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.12.3...@meltwater/engage-ads-tiktok-services@2.12.5-ENGAGE.0) (2024-10-29)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.12.3](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.12.2...@meltwater/engage-ads-tiktok-services@2.12.3) (2024-10-16)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.12.3-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.12.2...@meltwater/engage-ads-tiktok-services@2.12.3-ENGAGE.0) (2024-10-15)

### Bug Fixes

- addressing tests ([5eba0c4](https://github.com/meltwater/engage-ads-backend/commit/5eba0c4477d7b0cf36f6db892cd45da531a7a6cb))
- version update ([b25492b](https://github.com/meltwater/engage-ads-backend/commit/b25492b9a0a3dd46cc5e09433a01523de4639f4c))
- version update ([845fa83](https://github.com/meltwater/engage-ads-backend/commit/845fa833b2b425368dfb7bb94a0f3245c6b14a1c))

### Features

- integrating database package with mutation ([77de0db](https://github.com/meltwater/engage-ads-backend/commit/77de0db69d04ae905e2ba1627b89552d6e63f0e3))

## [2.12.2](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.12.0...@meltwater/engage-ads-tiktok-services@2.12.2) (2024-10-14)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.12.2-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.12.0...@meltwater/engage-ads-tiktok-services@2.12.2-ENGAGE.0) (2024-10-14)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

# [2.12.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.11.0...@meltwater/engage-ads-tiktok-services@2.12.0) (2024-10-07)

### Features

- adding support for social account ID for root level ([#287](https://github.com/meltwater/engage-ads-backend/issues/287)) ([1436575](https://github.com/meltwater/engage-ads-backend/commit/143657504cdb08af31a4134c1b5cf9633e6bf116))

## [2.11.3-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.11.0...@meltwater/engage-ads-tiktok-services@2.11.3-ENGAGE.0) (2024-10-04)

### Bug Fixes

- updating version ([7be6228](https://github.com/meltwater/engage-ads-backend/commit/7be6228676c618e63ec43e466abc92cbbac87c5c))

### Features

- adding support for social account ID for root level ([5a88704](https://github.com/meltwater/engage-ads-backend/commit/5a887045266c42778d2c273006adc488a7de2073))

# [2.11.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.10.0...@meltwater/engage-ads-tiktok-services@2.11.0) (2024-10-03)

### Features

- **regions:** ADS-164 Integrate with tiktok regions API ([#282](https://github.com/meltwater/engage-ads-backend/issues/282)) ([49d864b](https://github.com/meltwater/engage-ads-backend/commit/49d864b005cc6475466f0050ff74d751b9628b27))

## [2.10.2-ENGAGE.1](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.10.2-ENGAGE.0...@meltwater/engage-ads-tiktok-services@2.10.2-ENGAGE.1) (2024-10-03)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.10.2-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.10.0...@meltwater/engage-ads-tiktok-services@2.10.2-ENGAGE.0) (2024-10-03)

### Features

- **locations:** Add types for tiktok locations API ([99f7633](https://github.com/meltwater/engage-ads-backend/commit/99f7633a79214163ce29ef2bbb3ac6a1184fab5f))
- **locations:** export types for tiktok locations API ([253ca57](https://github.com/meltwater/engage-ads-backend/commit/253ca57088c58ebf77312ca34dffc876afc6b348))

# [2.10.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.9.0...@meltwater/engage-ads-tiktok-services@2.10.0) (2024-09-30)

### Features

- adding support for generating token and passing the same to rel… ([#281](https://github.com/meltwater/engage-ads-backend/issues/281)) ([8e60836](https://github.com/meltwater/engage-ads-backend/commit/8e60836ef0358facc58a4cdd0b31b45b272f7600))

## [2.9.1-ENGAGE.1](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.9.1-ENGAGE.0...@meltwater/engage-ads-tiktok-services@2.9.1-ENGAGE.1) (2024-09-12)

### Features

- adding logging for tikok boost ad repository ([9acc00e](https://github.com/meltwater/engage-ads-backend/commit/9acc00edfa3988b1c0d2e73c5a95541fe1329532))

## [2.9.1-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.9.0...@meltwater/engage-ads-tiktok-services@2.9.1-ENGAGE.0) (2024-09-10)

### Features

- adding support for generating token and passing the same to relevant repository and mutation ([6839470](https://github.com/meltwater/engage-ads-backend/commit/68394701a5c83d348f17709fdc63687c910ec5dc))

# [2.9.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.8.2...@meltwater/engage-ads-tiktok-services@2.9.0) (2024-09-09)

### Features

- **boosting:** ADS-153 update endpoints urls and payload for boosting tiktok ([#280](https://github.com/meltwater/engage-ads-backend/issues/280)) ([c9e8575](https://github.com/meltwater/engage-ads-backend/commit/c9e85755d81575f73f236739ab8680decc442904))

## [2.8.3-ENGAGE.1](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.8.3-ENGAGE.0...@meltwater/engage-ads-tiktok-services@2.8.3-ENGAGE.1) (2024-09-06)

### Bug Fixes

- apply formatting ([e6a624f](https://github.com/meltwater/engage-ads-backend/commit/e6a624fc42992b460e24bd912e19d5979fc5e48f))

## [2.8.3-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.8.2...@meltwater/engage-ads-tiktok-services@2.8.3-ENGAGE.0) (2024-09-06)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.8.2](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.8.1...@meltwater/engage-ads-tiktok-services@2.8.2) (2024-09-01)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.8.1](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.8.0...@meltwater/engage-ads-tiktok-services@2.8.1) (2024-08-29)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.8.1-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.8.0...@meltwater/engage-ads-tiktok-services@2.8.1-ENGAGE.0) (2024-08-29)

### Bug Fixes

- update GetTikTokInsightsQuery test file to use profileIds instead of profileId ([96f2aa2](https://github.com/meltwater/engage-ads-backend/commit/96f2aa23425c614d10d0b5ea15262de556580796))
- Update GetTikTokInsightsQuery tests to match new profileIds parameter format ([e6d75d0](https://github.com/meltwater/engage-ads-backend/commit/e6d75d073ae6642105b30f241964d17c0f337e01))
- update profileId to profileIds in test file ([0e06656](https://github.com/meltwater/engage-ads-backend/commit/0e0665602c78cf88144701507563b0a6aed250a0))
- Update TikTokInsightsRepository tests to handle profileIds changes ([c6d0fca](https://github.com/meltwater/engage-ads-backend/commit/c6d0fca8d78924ca900fca4dd04bb73278800183))
- update TikTokInsightsRepository tests to include paramsSerializer ([8f7bd99](https://github.com/meltwater/engage-ads-backend/commit/8f7bd992c36f04dcb455985aee33ff321159b224))
- Update TikTokInsightsRepository.spec.ts to handle paramsSerializer ([40bd51f](https://github.com/meltwater/engage-ads-backend/commit/40bd51f7e9b30e9e4ffa5a20fa896f2505702dbe))

### Features

- Add support for multiple profileIds in engageAdsChannelInsights query ([167a6d3](https://github.com/meltwater/engage-ads-backend/commit/167a6d37cdf97ace6a71a2a511974694293d23ec))
- handle multiple tiktok-profiles parameters correctly ([0b970ed](https://github.com/meltwater/engage-ads-backend/commit/0b970ed93bb73ffb446fab6388c7f28a18ac6599))
- update tiktok-profiles query parameter handling ([850bab1](https://github.com/meltwater/engage-ads-backend/commit/850bab1469df8f324ba3e46fbd22b17a61949509))
- Update TikTokInsightsRepository to handle multiple tiktok-profiles ([726fa13](https://github.com/meltwater/engage-ads-backend/commit/726fa1307497840b1e50225c085bd35a053bdc5c))
- Use profileIds parameter in getTikTokInsights and getTikTokTotalCount methods ([ef33f01](https://github.com/meltwater/engage-ads-backend/commit/ef33f01b34286f0b15bf6877e80f02764045474f))

# [2.8.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.7.2...@meltwater/engage-ads-tiktok-services@2.8.0) (2024-08-28)

### Features

- adding support for filtering posts by profile ID ([#274](https://github.com/meltwater/engage-ads-backend/issues/274)) ([09ed22b](https://github.com/meltwater/engage-ads-backend/commit/09ed22bc039b85cf7c15d03b3401d153123804b0))

## [2.7.5-ENGAGE.1](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.7.5-ENGAGE.0...@meltwater/engage-ads-tiktok-services@2.7.5-ENGAGE.1) (2024-08-28)

### Bug Fixes

- addressing linting concerns ([7ee301d](https://github.com/meltwater/engage-ads-backend/commit/7ee301deaad9743077342db5365d221ce1e4fee1))

### Features

- adding support for profileId for getTikTokTotalCount ([df9249c](https://github.com/meltwater/engage-ads-backend/commit/df9249c7a3ae07df986c2c9afeb39f28a7476a4e))

## [2.7.5-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.7.2...@meltwater/engage-ads-tiktok-services@2.7.5-ENGAGE.0) (2024-08-23)

### Bug Fixes

- updating version ([b23d9eb](https://github.com/meltwater/engage-ads-backend/commit/b23d9ebb73700b160d619c6e1842300049db5c72))

## [2.7.2](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.7.0...@meltwater/engage-ads-tiktok-services@2.7.2) (2024-08-22)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.7.2-ENGAGE.6](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.7.2-ENGAGE.5...@meltwater/engage-ads-tiktok-services@2.7.2-ENGAGE.6) (2024-08-21)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.7.2-ENGAGE.5](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.7.2-ENGAGE.4...@meltwater/engage-ads-tiktok-services@2.7.2-ENGAGE.5) (2024-08-21)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.7.2-ENGAGE.4](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.7.2-ENGAGE.3...@meltwater/engage-ads-tiktok-services@2.7.2-ENGAGE.4) (2024-08-21)

### Bug Fixes

- Ads-87 Applies reviews fixes and formatting ([#272](https://github.com/meltwater/engage-ads-backend/issues/272)) ([eb1f812](https://github.com/meltwater/engage-ads-backend/commit/eb1f812142e3976a8c4d119f1afe4d329fe37c49))

## [2.7.2-ENGAGE.3](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.7.0...@meltwater/engage-ads-tiktok-services@2.7.2-ENGAGE.3) (2024-08-20)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

### Features

- adding apollo sandbox get /graphql route for staging environmen… ([#266](https://github.com/meltwater/engage-ads-backend/issues/266)) ([794a66b](https://github.com/meltwater/engage-ads-backend/commit/794a66b74cdc220cb5712872f3c6249238c8f5d8))

## [2.6.48-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.45...@meltwater/engage-ads-tiktok-services@2.6.48-ENGAGE.0) (2024-08-10)

### Bug Fixes

- updating version ([fe62a02](https://github.com/meltwater/engage-ads-backend/commit/fe62a021e34cc1e62215eaec847823a5dc96c190))

## [2.6.45](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.44...@meltwater/engage-ads-tiktok-services@2.6.45) (2024-08-09)

## [2.7.2-ENGAGE.2](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.45...@meltwater/engage-ads-tiktok-services@2.7.2-ENGAGE.2) (2024-08-09)

# [2.7.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.45...@meltwater/engage-ads-tiktok-services@2.7.0) (2024-08-10)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.7.2-ENGAGE.1](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.7.2-ENGAGE.0...@meltwater/engage-ads-tiktok-services@2.7.2-ENGAGE.1) (2024-08-09)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.7.2-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.7.1-ENGAGE.0...@meltwater/engage-ads-tiktok-services@2.7.2-ENGAGE.0) (2024-08-08)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.7.1-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.44...@meltwater/engage-ads-tiktok-services@2.7.1-ENGAGE.0) (2024-08-06)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.44](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.43...@meltwater/engage-ads-tiktok-services@2.6.44) (2024-08-05)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.44-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.43...@meltwater/engage-ads-tiktok-services@2.6.44-ENGAGE.0) (2024-07-30)

### Features

- tiktok profile and account selector ([df8b14a](https://github.com/meltwater/engage-ads-backend/commit/df8b14a68f5e03d2108f7d6a717d5439a7088de7))

## [2.6.43](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.42...@meltwater/engage-ads-tiktok-services@2.6.43) (2024-07-30)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.42](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.41...@meltwater/engage-ads-tiktok-services@2.6.42) (2024-06-29)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.41](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.40...@meltwater/engage-ads-tiktok-services@2.6.41) (2024-06-25)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.40](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.39...@meltwater/engage-ads-tiktok-services@2.6.40) (2024-06-24)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.39](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.36...@meltwater/engage-ads-tiktok-services@2.6.39) (2024-06-20)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.36](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.35...@meltwater/engage-ads-tiktok-services@2.6.36) (2024-06-18)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.35](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.34...@meltwater/engage-ads-tiktok-services@2.6.35) (2024-06-18)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.34](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.33...@meltwater/engage-ads-tiktok-services@2.6.34) (2024-06-18)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.33](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.32...@meltwater/engage-ads-tiktok-services@2.6.33) (2024-06-18)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.32](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.31...@meltwater/engage-ads-tiktok-services@2.6.32) (2024-06-18)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.31](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.30...@meltwater/engage-ads-tiktok-services@2.6.31) (2024-06-18)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.30](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.29...@meltwater/engage-ads-tiktok-services@2.6.30) (2024-06-15)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.29](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.28...@meltwater/engage-ads-tiktok-services@2.6.29) (2024-06-15)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.28](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.27...@meltwater/engage-ads-tiktok-services@2.6.28) (2024-06-15)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.27](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.26...@meltwater/engage-ads-tiktok-services@2.6.27) (2024-06-15)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.26](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.25...@meltwater/engage-ads-tiktok-services@2.6.26) (2024-06-15)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.25](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.24...@meltwater/engage-ads-tiktok-services@2.6.25) (2024-06-15)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.24](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.23...@meltwater/engage-ads-tiktok-services@2.6.24) (2024-06-15)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.23](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.22...@meltwater/engage-ads-tiktok-services@2.6.23) (2024-06-15)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.22](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.21...@meltwater/engage-ads-tiktok-services@2.6.22) (2024-06-15)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.21](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.20...@meltwater/engage-ads-tiktok-services@2.6.21) (2024-06-15)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.20](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.19...@meltwater/engage-ads-tiktok-services@2.6.20) (2024-06-15)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.19](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.18...@meltwater/engage-ads-tiktok-services@2.6.19) (2024-06-15)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.18](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.17...@meltwater/engage-ads-tiktok-services@2.6.18) (2024-06-13)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.17](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.16...@meltwater/engage-ads-tiktok-services@2.6.17) (2024-06-12)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.16](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.15...@meltwater/engage-ads-tiktok-services@2.6.16) (2024-06-12)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.15](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.14...@meltwater/engage-ads-tiktok-services@2.6.15) (2024-06-10)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.14](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.13...@meltwater/engage-ads-tiktok-services@2.6.14) (2024-06-10)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.13](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.12...@meltwater/engage-ads-tiktok-services@2.6.13) (2024-06-04)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.12](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.11...@meltwater/engage-ads-tiktok-services@2.6.12) (2024-06-04)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.11](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.10...@meltwater/engage-ads-tiktok-services@2.6.11) (2024-06-03)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.10](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.9...@meltwater/engage-ads-tiktok-services@2.6.10) (2024-06-02)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.9](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.8...@meltwater/engage-ads-tiktok-services@2.6.9) (2024-06-02)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.8](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.7...@meltwater/engage-ads-tiktok-services@2.6.8) (2024-06-02)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.7](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.6...@meltwater/engage-ads-tiktok-services@2.6.7) (2024-06-02)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.6](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.5...@meltwater/engage-ads-tiktok-services@2.6.6) (2024-06-02)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.5](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.4...@meltwater/engage-ads-tiktok-services@2.6.5) (2024-06-02)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.4](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.3...@meltwater/engage-ads-tiktok-services@2.6.4) (2024-06-02)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.3](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-tiktok-services@2.6.2...@meltwater/engage-ads-tiktok-services@2.6.3) (2024-06-02)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## 2.6.2 (2024-06-01)

**Note:** Version bump only for package @meltwater/engage-ads-tiktok-services

## [2.6.1](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-tiktok-services@2.6.0...@meltwater/grimoire-tiktok-services@2.6.1) (2024-05-31)

**Note:** Version bump only for package @meltwater/grimoire-tiktok-services

# 2.6.0 (2024-05-29)

### Features

- Adding TikTok Lambda and code ([#68](https://github.com/meltwater/grimoire-commons/issues/68)) ([87f0912](https://github.com/meltwater/grimoire-commons/commit/87f09122226ab5c22d68432692dfc680439cb86e))

## 2.5.1-ENGAGE.0 (2024-05-28)

### Bug Fixes

- Adding TikTok Lambda and code ([e043c9a](https://github.com/meltwater/grimoire-lambdas/commit/e043c9a0ddcf6a648c79a42d722932a772108232))
- Adding TikTok Lambda and code ([f84f6cc](https://github.com/meltwater/grimoire-lambdas/commit/f84f6cca96a727b9e2b356f750ed0ede563d6e74))
