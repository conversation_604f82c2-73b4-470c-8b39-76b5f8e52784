import type { HttpClient } from "@meltwater/engage-ads-commons";
import { <PERSON>da<PERSON><PERSON><PERSON>, LambdaLogger } from "@meltwater/lambda-monitoring";
import type {
  LinkedInTextAdParams,
  CreateLinkedInCampaignResponse,
  CreateLinkedInCreativeResponse,
} from "./types/linkedin-text-ads";
import type { LinkedInImageAdParams } from "./types/linkedin-image-ads";
import type { LinkedInVideoAdParams } from "./types/linkedin-video-ads";
import type { LinkedInDocumentAdParams } from "./types/linkedin-document-ads";

const tracer = LambdaTracer.getInstance();
const logger = LambdaLogger.getInstance();

export class LinkedInBoostAdRepository {
  /** LinkedIn Marketing API base URL */
  private static readonly API_BASE = "https://api.linkedin.com";

  constructor(private readonly httpClient: HttpClient) {
    // Ensure the base URL is correct
    if (!httpClient) {
      throw new Error("HttpClient instance is required");
    }
  }

  /**
   * Validates and returns the supplied existing campaign. New campaign creation
   * is no longer supported from this repository – callers must provide a
   * valid campaignId.
   */
  @tracer.captureMethod({ subSegmentName: "LinkedInBoostRepository:createCampaign" })
  public async createCampaign(
    payload: LinkedInTextAdParams | LinkedInImageAdParams | LinkedInVideoAdParams | LinkedInDocumentAdParams,
  ): Promise<CreateLinkedInCampaignResponse> {
    const { campaign } = payload;

    // Existing campaign is mandatory; throw if missing
    if (!campaign.campaignId) {
      throw new Error("campaignId is required to boost LinkedIn ads using this endpoint.");
    }
    logger.info("Using existing LinkedIn campaign", { campaignId: campaign.campaignId });
    return {
      campaignId: campaign.campaignId,
      adAccountId: campaign.adAccountId,
    };
  }

  /**
   * Create a basic text ad creative for the supplied campaign.
   */
  @tracer.captureMethod({ subSegmentName: "LinkedInBoostRepository:createCreative" })
  public async createCreative(
    payload: LinkedInTextAdParams & { campaignId: string },
  ): Promise<CreateLinkedInCreativeResponse> {
    const { creative } = payload;

    const endpoint = `/rest/adAccounts/${payload.campaign.adAccountId}/creatives`;

    // Validate field lengths according to LinkedIn limits
    if (creative.adName.length > 25) {
      throw new Error("LinkedIn headline (adName) must be 25 characters or fewer.");
    }
    if (creative.description.length > 75) {
      throw new Error("LinkedIn text ad description must be 75 characters or fewer.");
    }

    const creativeBody = {
      campaign: `urn:li:sponsoredCampaign:${payload.campaignId}`,
      name: creative.adName,
      intendedStatus: "ACTIVE",
      content: {
        textAd: {
          landingPage: creative.destinationUrl,
          headline: creative.adName,
          description: creative.description,
        },
      },
    } as Record<string, unknown>;

    logger.info("Creating LinkedIn text ad creative", { endpoint, body: creativeBody });

    const clientWithHeaders = this.httpClient as HttpClient & {
      postWithHeaders<T, R = T>(
        url: string,
        data?: T,
        config?: Record<string, unknown>,
      ): Promise<{ data: R; headers: Record<string, unknown> }>;
    };

    const { headers, data } = await clientWithHeaders.postWithHeaders<Record<string, unknown>, Record<string, unknown>>(
      endpoint,
      creativeBody,
    );

    // LinkedIn returns the newly created URN in the `x-restli-id` header, e.g. "urn:li:sponsoredCreative:123456".
    let creativeId: string | undefined;

    const restliId = headers["x-restli-id"] as string | undefined;
    if (restliId) {
      const parts = restliId.split(":");
      creativeId = parts.length > 0 ? parts[parts.length - 1] : restliId;
    } else if (data && typeof (data as { id?: string }).id === "string") {
      creativeId = (data as { id?: string }).id;
    }

    if (!creativeId) {
      logger.warn("Unable to determine LinkedIn creativeId from response", { headers, data });
    }

    return {
      creativeId,
      creativeName: creative.adName,
    };
  }

  @tracer.captureMethod({ subSegmentName: "LinkedInBoostRepository:createImageCreative" })
  public async createImageCreative(
    payload: LinkedInImageAdParams & { campaignId: string },
  ): Promise<CreateLinkedInCreativeResponse> {
    const { creative } = payload;

    const endpoint = `/rest/adAccounts/${payload.campaign.adAccountId}/creatives`;

    // Validate field lengths according to LinkedIn limits
    if (creative.adName.length > 25) {
      throw new Error("LinkedIn headline (adName) must be 25 characters or fewer.");
    }

    // Set up HTTP client with headers
    const clientWithHeaders = this.httpClient as HttpClient & {
      postWithHeaders<T, R = T>(
        url: string,
        data?: T,
        config?: Record<string, unknown>,
      ): Promise<{ data: R; headers: Record<string, unknown> }>;
    };

    // Process the post ID for use in the creative reference
    if (!payload.postId) {
      throw new Error("postId is required to create LinkedIn image ads using this endpoint.");
    }

    // Use the existing post ID directly as the reference
    let postReference = payload.postId;

    if (!postReference.startsWith("urn:li:")) {
      postReference = `urn:li:share:${postReference}`;
    } else if (postReference.startsWith("urn:li:ugcPost:")) {
      const postId = postReference.replace("urn:li:ugcPost:", "");
      postReference = `urn:li:share:${postId}`;
      logger.info("Converting ugcPost to share format for creative reference", {
        originalReference: payload.postId,
        convertedReference: postReference,
      });
    }

    logger.info("Using existing LinkedIn post", { postReference });

    const creativeBody = {
      campaign: `urn:li:sponsoredCampaign:${payload.campaignId}`,
      name: creative.adName,
      intendedStatus: "ACTIVE",
      content: {
        reference: postReference,
      },
    } as Record<string, unknown>;

    logger.info("Creating LinkedIn image ad creative", { endpoint, body: creativeBody });

    const { headers, data } = await clientWithHeaders.postWithHeaders<Record<string, unknown>, Record<string, unknown>>(
      endpoint,
      creativeBody,
    );

    // LinkedIn returns the newly created URN in the `x-restli-id` header, e.g. "urn:li:sponsoredCreative:123456".
    let creativeId: string | undefined;

    const restliId = headers["x-restli-id"] as string | undefined;
    if (restliId) {
      const parts = restliId.split(":");
      creativeId = parts.length > 0 ? parts[parts.length - 1] : restliId;
    } else if (data && typeof (data as { id?: string }).id === "string") {
      creativeId = (data as { id?: string }).id;
    }

    if (!creativeId) {
      logger.warn("Unable to determine LinkedIn creativeId from response", { headers, data });
    }

    return {
      creativeId,
      creativeName: creative.adName,
    };
  }

  /**
   * Create a video ad creative for the supplied campaign.
   */
  @tracer.captureMethod({ subSegmentName: "LinkedInBoostRepository:createVideoCreative" })
  public async createVideoCreative(
    payload: LinkedInVideoAdParams & { campaignId: string },
  ): Promise<CreateLinkedInCreativeResponse> {
    const { creative } = payload;

    const endpoint = `/rest/adAccounts/${payload.campaign.adAccountId}/creatives`;

    // Validate field lengths according to LinkedIn limits
    if (creative.adName.length > 25) {
      throw new Error("LinkedIn headline (adName) must be 25 characters or fewer.");
    }

    // Set up HTTP client with headers
    const clientWithHeaders = this.httpClient as HttpClient & {
      postWithHeaders<T, R = T>(
        url: string,
        data?: T,
        config?: Record<string, unknown>,
      ): Promise<{ data: R; headers: Record<string, unknown> }>;
    };

    // Process the post ID for use in the creative reference
    if (!payload.postId) {
      throw new Error("postId is required to create LinkedIn video ads using this endpoint.");
    }

    // Use the existing post ID directly as the reference
    let postReference = payload.postId;

    if (!postReference.startsWith("urn:li:")) {
      postReference = `urn:li:ugcPost:${postReference}`;
    }

    // Keep the original format if it's already a URN
    logger.info("Using post reference format", {
      postReference,
      originalReference: payload.postId,
    });

    logger.info("Using existing LinkedIn post", { postReference });

    const creativeBody = {
      campaign: `urn:li:sponsoredCampaign:${payload.campaignId}`,
      name: creative.adName,
      intendedStatus: "ACTIVE",
      content: {
        reference: postReference,
      },
    } as Record<string, unknown>;

    logger.info("Creating LinkedIn video ad creative", { endpoint, body: creativeBody });

    const { headers, data } = await clientWithHeaders.postWithHeaders<Record<string, unknown>, Record<string, unknown>>(
      endpoint,
      creativeBody,
    );

    // LinkedIn returns the newly created URN in the `x-restli-id` header, e.g. "urn:li:sponsoredCreative:123456".
    let creativeId: string | undefined;

    const restliId = headers["x-restli-id"] as string | undefined;
    if (restliId) {
      const parts = restliId.split(":");
      creativeId = parts.length > 0 ? parts[parts.length - 1] : restliId;
    } else if (data && typeof (data as { id?: string }).id === "string") {
      creativeId = (data as { id?: string }).id;
    }

    if (!creativeId) {
      logger.warn("Unable to determine LinkedIn creativeId from response", { headers, data });
    }

    return {
      creativeId,
      creativeName: creative.adName,
    };
  }

  /**
   * Create a document ad creative for the supplied campaign.
   */
  @tracer.captureMethod({ subSegmentName: "LinkedInBoostRepository:createDocumentCreative" })
  public async createDocumentCreative(
    payload: LinkedInDocumentAdParams & { campaignId: string },
  ): Promise<CreateLinkedInCreativeResponse> {
    const { creative } = payload;

    const endpoint = `/rest/adAccounts/${payload.campaign.adAccountId}/creatives`;

    // Validate field lengths according to LinkedIn limits
    if (creative.adName.length > 25) {
      throw new Error("LinkedIn headline (adName) must be 25 characters or fewer.");
    }

    // Set up HTTP client with headers
    const clientWithHeaders = this.httpClient as HttpClient & {
      postWithHeaders<T, R = T>(
        url: string,
        data?: T,
        config?: Record<string, unknown>,
      ): Promise<{ data: R; headers: Record<string, unknown> }>;
    };

    // Extract post reference from postId
    let postReference: string;
    if (payload.postId) {
      // Use the postId directly as the reference
      postReference = payload.postId;
    } else {
      throw new Error("postId is required for LinkedIn document ad creative");
    }

    logger.info("Using existing LinkedIn post", { postReference });

    const creativeBody = {
      campaign: `urn:li:sponsoredCampaign:${payload.campaignId}`,
      name: creative.adName,
      intendedStatus: "ACTIVE",
      content: {
        reference: postReference,
      },
    } as Record<string, unknown>;

    logger.info("Creating LinkedIn document ad creative", { endpoint, body: creativeBody });

    const { headers, data } = await clientWithHeaders.postWithHeaders<Record<string, unknown>, Record<string, unknown>>(
      endpoint,
      creativeBody,
    );

    // LinkedIn returns the newly created URN in the `x-restli-id` header, e.g. "urn:li:sponsoredCreative:123456".
    let creativeId: string | undefined;

    const restliId = headers["x-restli-id"] as string | undefined;
    if (restliId) {
      const parts = restliId.split(":");
      creativeId = parts.length > 0 ? parts[parts.length - 1] : restliId;
    } else if (data && typeof (data as { id?: string }).id === "string") {
      creativeId = (data as { id?: string }).id;
    }

    if (!creativeId) {
      logger.warn("Unable to determine LinkedIn creativeId from response", { headers, data });
    }

    return {
      creativeId,
      creativeName: creative.adName,
    };
  }
}
