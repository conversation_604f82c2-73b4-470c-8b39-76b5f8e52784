import { LambdaTracer, LambdaLogger } from "@meltwater/lambda-monitoring";
import type { ICommand } from "@meltwater/cqrs";
import type { LinkedInBoostAdRepository } from "../linkedin-boost-ad-repository";
import type { LinkedInVideoAdParams, CreateLinkedInVideoAdResponse } from "../types/linkedin-video-ads";

const tracer = LambdaTracer.getInstance();
const logger = LambdaLogger.getInstance();

export class CreateLinkedInVideoAdCommand implements ICommand<LinkedInVideoAdParams, CreateLinkedInVideoAdResponse> {
  constructor(private readonly repository: LinkedInBoostAdRepository) {}

  @tracer.captureMethod({ subSegmentName: "CreateLinkedInVideoAdCommand:execute" })
  public async execute(params: LinkedInVideoAdParams): Promise<CreateLinkedInVideoAdResponse> {
    try {
      logger.info("Executing CreateLinkedInVideoAdCommand", { params: JSON.stringify(params) });

      const campaign = await this.repository.createCampaign(params);

      const creative = await this.repository.createVideoCreative({ ...params, campaignId: campaign.campaignId! });

      return {
        campaign,
        creative,
      };
    } catch (error) {
      logger.error("Failed to execute CreateLinkedInVideoAdCommand", {
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }
}
