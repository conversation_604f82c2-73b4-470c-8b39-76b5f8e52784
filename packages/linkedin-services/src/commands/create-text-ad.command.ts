import { Lambda<PERSON>racer, LambdaLogger } from "@meltwater/lambda-monitoring";
import type { ICommand } from "@meltwater/cqrs";
import type { LinkedInBoostAdRepository } from "../linkedin-boost-ad-repository";
import type { LinkedInTextAdParams, CreateLinkedInTextAdResponse } from "../types/linkedin-text-ads";

const tracer = LambdaTracer.getInstance();
const logger = LambdaLogger.getInstance();

export class CreateLinkedInTextAdCommand implements ICommand<LinkedInTextAdParams, CreateLinkedInTextAdResponse> {
  constructor(private readonly repository: LinkedInBoostAdRepository) {}

  @tracer.captureMethod({ subSegmentName: "CreateLinkedInTextAdCommand:execute" })
  public async execute(params: LinkedInTextAdParams): Promise<CreateLinkedInTextAdResponse> {
    try {
      logger.info("Executing CreateLinkedInTextAdCommand", { params: JSON.stringify(params) });

      // 1. Create or reuse campaign
      const campaign = await this.repository.createCampaign(params);

      // 2. Create creative under the campaign
      const creative = await this.repository.createCreative({ ...params, campaignId: campaign.campaignId! });

      return {
        campaign,
        creative,
      };
    } catch (error) {
      logger.error("Failed to execute CreateLinkedInTextAdCommand", {
        error: error instanceof Error ? error.message : String(error),
      });
      return {
        metadata: {
          error: {
            name: error instanceof Error ? error.name : "UnknownError",
            message: error instanceof Error ? error.message : String(error),
          },
        },
      };
    }
  }
}
