import { Lambda<PERSON>race<PERSON>, LambdaLogger } from "@meltwater/lambda-monitoring";
import type { ICommand } from "@meltwater/cqrs";
import type { LinkedInBoostAdRepository } from "../linkedin-boost-ad-repository";
import type { LinkedInImageAdParams, CreateLinkedInImageAdResponse } from "../types/linkedin-image-ads";

const tracer = LambdaTracer.getInstance();
const logger = LambdaLogger.getInstance();

export class CreateLinkedInImageAdCommand implements ICommand<LinkedInImageAdParams, CreateLinkedInImageAdResponse> {
  constructor(private readonly repository: LinkedInBoostAdRepository) {}

  @tracer.captureMethod({ subSegmentName: "CreateLinkedInImageAdCommand:execute" })
  public async execute(params: LinkedInImageAdParams): Promise<CreateLinkedInImageAdResponse> {
    try {
      logger.info("Executing CreateLinkedInImageAdCommand", { params: JSON.stringify(params) });

      // 1. Create or reuse campaign
      const campaign = await this.repository.createCampaign(params);

      // 2. Create creative under the campaign
      const creative = await this.repository.createImageCreative({ ...params, campaignId: campaign.campaignId! });

      return {
        campaign,
        creative,
      };
    } catch (error) {
      logger.error("Failed to execute CreateLinkedInImageAdCommand", {
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }
}
