import { Lambda<PERSON>racer, LambdaLogger } from "@meltwater/lambda-monitoring";
import type { ICommand } from "@meltwater/cqrs";
import type { LinkedInBoostAdRepository } from "../linkedin-boost-ad-repository";
import type { LinkedInDocumentAdParams, CreateLinkedInDocumentAdResponse } from "../types/linkedin-document-ads";

const tracer = LambdaTracer.getInstance();
const logger = LambdaLogger.getInstance();

export class CreateLinkedInDocumentAdCommand
  implements ICommand<LinkedInDocumentAdParams, CreateLinkedInDocumentAdResponse>
{
  constructor(private readonly repository: LinkedInBoostAdRepository) {}

  @tracer.captureMethod({ subSegmentName: "CreateLinkedInDocumentAdCommand:execute" })
  public async execute(params: LinkedInDocumentAdParams): Promise<CreateLinkedInDocumentAdResponse> {
    try {
      logger.info("Executing CreateLinkedInDocumentAdCommand", { params: JSON.stringify(params) });

      // 1. Create or reuse campaign
      const campaign = await this.repository.createCampaign(params);

      // 2. Create creative under the campaign
      const creative = await this.repository.createDocumentCreative({ ...params, campaignId: campaign.campaignId! });

      return {
        campaign,
        creative,
      };
    } catch (error) {
      logger.error("Failed to execute CreateLinkedInDocumentAdCommand", {
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }
}
