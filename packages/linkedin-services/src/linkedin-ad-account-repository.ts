import type { HttpClient } from "@meltwater/engage-ads-commons";
import { LambdaTracer } from "@meltwater/lambda-monitoring";
import { LambdaLogger } from "@meltwater/lambda-monitoring";
import type {
  LinkedInAdAccount,
  LinkedInAdAccountQueryInput,
  GraphQLRequest,
  GraphQLResponseWrapper,
  LinkedInAdAccountResponse,
  HttpClientResponse,
  AssociatedAccountsQueryInput,
  AssociatedAccountsResponse,
  AssociatedAccountsGraphQLResponseWrapper,
} from "./types/linkedin-ad-account";

const tracer = LambdaTracer.getInstance();
const logger = LambdaLogger.getInstance();

export class LinkedInAdAccountRepository {
  constructor(private readonly httpClient: HttpClient) {}

  @tracer.captureMethod({ subSegmentName: "LinkedInAdAccountRepository:getLinkedInAdAccounts" })
  async getLinkedInAdAccounts(input: LinkedInAdAccountQueryInput, accessToken: string): Promise<LinkedInAdAccount[]> {
    try {
      const graphqlRequest: GraphQLRequest = {
        query: `
          query CompanyCredentialsFilteredQuery($query: FilteredCompanyCredentials) {
            companyCredentialsFilteredQuery(query: $query) {
              targetPageName
              socialAccountId
              channelName
              credentialId
              targetPageLogoUrl
              tokenId
              tokenDetails {
                token
              }
            }
          }
        `,
        variables: {
          query: input,
        },
      };

      const headers = {
        Authorization: `${accessToken}`,
      };

      const response: HttpClientResponse<GraphQLResponseWrapper> = await this.httpClient.post(
        "/graphql",
        graphqlRequest,
        { headers },
      );

      if (!response || !response.data || !response.data.companyCredentialsFilteredQuery) {
        logger.error("Invalid response structure", { "http.response.body.content.text": JSON.stringify(response) });
        throw new Error("Invalid response structure");
      }

      const adAccounts = response.data.companyCredentialsFilteredQuery.map(
        (account: LinkedInAdAccountResponse): LinkedInAdAccount => ({
          credentialId: account.credentialId,
          targetPageName: account.targetPageName,
          targetPageLogoUrl: account.targetPageLogoUrl,
          socialAccountId: account.socialAccountId,
          channelName: account.channelName,
          tokenId: account.tokenId,
          tokenDetails: account.tokenDetails,
        }),
      );

      logger.debug(`Processed LinkedIn ad accounts: ${adAccounts.length}`);

      return adAccounts;
    } catch (error) {
      logger.error("Error fetching LinkedIn ad accounts:", {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        input,
        accessTokenLength: accessToken.length,
      });
      throw error;
    }
  }

  @tracer.captureMethod({ subSegmentName: "LinkedInAdAccountRepository:getAssociatedAccounts" })
  async getAssociatedAccounts(
    input: AssociatedAccountsQueryInput,
    accessToken: string,
  ): Promise<AssociatedAccountsResponse[]> {
    try {
      const graphqlRequest = {
        query: `query AssociatedAccountsQuery($query: AssociatedAccountsQuery) {
                  associatedAccountsQuery(query: $query) {
                    socialAccountId
                    targetPageName
                    credentialId
                    channelName
                    targetPageLogoUrl
                  }
              }`,
        variables: {
          query: input,
        },
        operationName: "AssociatedAccountsQuery",
      };

      const headers = {
        Authorization: `${accessToken}`,
        "Content-Type": "application/json",
        Accept: "*/*",
        "Accept-Language": "en-US,en;q=0.9",
      };

      const response: HttpClientResponse<AssociatedAccountsGraphQLResponseWrapper> = await this.httpClient.post(
        "/graphql",
        graphqlRequest,
        { headers },
      );

      logger.debug("LinkedIn associated accounts API request/response", {
        "http.response.body.content.text": JSON.stringify(response),
        credentialId: input.credentialId,
        query: graphqlRequest.query,
        variables: JSON.stringify(graphqlRequest.variables),
      });

      if (!response || !response.data) {
        logger.error("Invalid response structure for associated accounts", {
          "http.response.body.content.text": JSON.stringify(response),
        });
        throw new Error("Invalid response structure for associated accounts");
      }

      // Handle null response (no associated accounts) as valid empty result
      if (!response.data.associatedAccountsQuery) {
        logger.debug("No associated accounts found for credential", { credentialId: input.credentialId });
        return [];
      }

      const associatedAccounts = response.data.associatedAccountsQuery.map((account: AssociatedAccountsResponse) => ({
        socialAccountId: account.socialAccountId || account.credentialId?.toString() || "", // Use socialAccountId if available, fallback to credentialId
        targetPageName: account.targetPageName,
        credentialId: account.credentialId,
        channelName: account.channelName,
        targetPageLogoUrl: account.targetPageLogoUrl,
      }));

      logger.debug(`Processed LinkedIn associated accounts: ${associatedAccounts.length}`);

      return associatedAccounts;
    } catch (error) {
      logger.error("Error fetching LinkedIn associated accounts:", {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        input,
        accessTokenLength: accessToken.length,
      });
      throw error;
    }
  }
}
