export interface LinkedInVideoAdCampaign {
  /** Existing campaign ID to boost. Mandatory for our flow */
  campaignId: string;

  /** LinkedIn Ad Account URN id portion */
  adAccountId: string;

  /* The following fields are retained as optional for backward-compatibility
     but are NOT used when an existing campaignId is supplied. */
  objectiveType?: string;
  campaignName?: string;
  dailyBudget?: string;
  currencyCode?: string;
  campaignGroupId?: string;
  startTimestamp?: string;
  endTimestamp?: string;
}

export interface LinkedInCreativeVideoAd {
  adName: string;
}

export interface LinkedInVideoAdParams {
  /** Company ID in Meltwater format */
  companyId: string;
  /** Credential ID for the LinkedIn account */
  credentialId: number;
  /** LinkedIn Ad Account social account ID (advertiser) */
  advertiserId: string;
  /** Optional post URN/ID to associate with the ad for DB mapping */
  postId?: string;

  campaign: LinkedInVideoAdCampaign;
  creative: LinkedInCreativeVideoAd;
}

export interface CreateLinkedInVideoAdResponse {
  campaign?: {
    campaignId?: string;
    campaignName?: string;
    adAccountId?: string;
  };
  creative?: {
    creativeId?: string;
    creativeName?: string;
  };
  metadata?: {
    error?: {
      name: string;
      message: string;
    };
  };
}
