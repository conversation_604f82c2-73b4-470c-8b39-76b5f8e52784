export interface LinkedInTextAdCampaign {
  /** Existing campaign ID to boost. Mandatory for our flow */
  campaignId: string;

  /** LinkedIn Ad Account URN id portion */
  adAccountId: string;

  /* The following fields are retained as optional for backward-compatibility
     but are NOT used when an existing campaignId is supplied. */
  objectiveType?: string;
  campaignName?: string;
  dailyBudget?: string;
  currencyCode?: string;
  campaignGroupId?: string;
  startTimestamp?: string;
  endTimestamp?: string;
}

export interface LinkedInCreativeTextAd {
  adName: string;
  destinationUrl: string;
  description: string;
  callToAction?: string;
}

export interface LinkedInTextAdParams {
  companyId: string;
  credentialId: number;
  /** LinkedIn Ad Account social account ID (advertiser) */
  advertiserId: string;
  /** Optional post URN/ID to associate with the ad for DB mapping */
  postId?: string;

  campaign: LinkedInTextAdCampaign;
  creative: LinkedInCreativeTextAd;
}

export interface CreateLinkedInCampaignResponse {
  campaignId?: string;
  campaignName?: string;
  adAccountId?: string;
}

export interface CreateLinkedInCreativeResponse {
  creativeId?: string;
  creativeName?: string;
}

export interface CreateLinkedInTextAdResponse {
  campaign?: CreateLinkedInCampaignResponse;
  creative?: CreateLinkedInCreativeResponse;
  metadata?: {
    error?: {
      name: string;
      message: string;
    };
  };
}
