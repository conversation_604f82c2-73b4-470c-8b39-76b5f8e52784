export interface LinkedInDocumentAdCampaign {
  campaignId: string;
  objectiveType?: string;
  campaignName?: string;
  dailyBudget?: string;
  currencyCode?: string;
  adAccountId: string;
  campaignGroupId?: string;
  startTimestamp?: string;
  endTimestamp?: string;
}

export interface LinkedInCreativeDocumentAd {
  adName: string;
}

export interface LinkedInDocumentAdParams {
  /** Company ID in Meltwater format */
  companyId: string;
  /** Credential ID for the LinkedIn account */
  credentialId: number;
  /** LinkedIn Ad Account social account ID (advertiser) */
  advertiserId: string;
  /** Optional post URN/ID to associate with the ad for DB mapping */
  postId?: string;

  campaign: LinkedInDocumentAdCampaign;
  creative: LinkedInCreativeDocumentAd;
}

export interface CreateLinkedInCampaignResponse {
  campaignId?: string;
  campaignName?: string;
  adAccountId?: string;
}

export interface CreateLinkedInCreativeResponse {
  creativeId?: string;
  creativeName?: string;
}

export interface CreateLinkedInDocumentAdResponse {
  campaign?: CreateLinkedInCampaignResponse;
  creative?: CreateLinkedInCreativeResponse;
}
