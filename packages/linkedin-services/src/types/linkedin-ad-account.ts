export interface LinkedInAdAccount {
  credentialId: number;
  targetPageName: string;
  targetPageLogoUrl: string | null;
  socialAccountId: string;
  channelName: string;
  tokenId?: string;
  tokenDetails?: {
    token: string;
  };
}

export interface LinkedInAdAccountQueryInput {
  applicationCompanyId: string;
  userId: string;
  activeInd: number;
  statusInd: string[];
  channels: string[];
}

export interface GraphQLResponseWrapper {
  companyCredentialsFilteredQuery: Array<LinkedInAdAccountResponse>;
}

export interface HttpClientResponse<T> {
  data: T;
  status?: number;
  headers?: {
    Authorization: string;
    [key: string]: string;
  };
}

export interface LinkedInAdAccountResponse {
  credentialId: number;
  targetPageName: string;
  targetPageLogoUrl: string | null;
  socialAccountId: string;
  channelName: string;
  tokenId?: string;
  tokenDetails?: {
    token: string;
  };
}

export interface GraphQLRequest {
  query: string;
  variables: {
    query: LinkedInAdAccountQueryInput;
  };
}

export interface ExtendedLinkedInAdAccountQueryInput extends LinkedInAdAccountQueryInput {
  accessToken: string;
}

// Types for Associated Accounts Query
export interface AssociatedAccountsQueryInput {
  credentialId: number;
  companyId: string;
}

export interface AssociatedAccountsResponse {
  socialAccountId: string;
  targetPageName: string;
  credentialId: number;
  channelName: string;
  targetPageLogoUrl: string | null;
}

export interface AssociatedAccountsGraphQLRequest {
  query: string;
  variables: {
    query: AssociatedAccountsQueryInput;
  };
}

export interface AssociatedAccountsGraphQLResponseWrapper {
  associatedAccountsQuery: AssociatedAccountsResponse[];
}

export interface ExtendedAssociatedAccountsQueryInput extends AssociatedAccountsQueryInput {
  accessToken: string;
}
