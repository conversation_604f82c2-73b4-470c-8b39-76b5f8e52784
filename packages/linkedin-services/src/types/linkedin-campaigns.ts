export interface LinkedInCampaign {
  id: number;
  name: string;
  status: string;
  type?: string;
  dailyBudget?: {
    amount: string;
    currencyCode: string;
  };
  unitCost?: {
    amount: string;
    currencyCode: string;
  };
}

export interface GetLinkedInCampaignsParams {
  adAccountId: string;
  status?: string; // e.g., "ACTIVE"
  type?: string; // e.g., "SPONSORED_UPDATES"
}

export interface LinkedInCampaignsApiResponse {
  elements: LinkedInCampaign[];
  metadata?: {
    nextPageToken?: string;
  };
}

export interface ExtendedGetLinkedInCampaignsParams extends GetLinkedInCampaignsParams {
  accessToken: string;
}
