export interface GetLinkedInInsightsQueryParams {
  start_date: string;
  end_date: string;
  size?: number;
  order?: string;
  start_from?: number;
  sort_by?: string;
  profileIds?: string[];
  match?: { captionKeyword?: string; postId?: string };
  sortByMetric?: boolean;
}

export interface LinkedInInsights {
  id: string;
  document_id?: string;
  page_id: string;
  externalId: string;
  created_time: string;
  message: string;
  permalink_url: string;
  thumbnail_url?: string;
  mimeType?: string;
  company_id: string;
  likes_count: number;
  comments_count: number;
  shares_count: number;
  engagement_rate: number;
  post_impressions: number;
  post_clicks: number;
  post_reactions: number;
  post_engagement_rate: number;
  post_video_views: number;
  post_video_view_time: number;
  post_video_completion_rate: number;
  schedule_end_time?: string | null;
  post_type?: string;
  download_url?: string;
  media_urn?: string;
}

export interface WildcardsSource {
  id: string;
  org_id?: string; // Organization ID from LinkedIn API (preferred for page_id mapping)
  author?: string; // Author field from LinkedIn API (fallback for page_id mapping)
  created_at?: string;
  distribution?: {
    feedDistribution?: string;
    thirdPartyDistributionChannels?: string[];
  };
  lifecycleState?: string;
  post_type?: string;
  specificContent?: {
    "com.linkedin.ugc.ShareContent"?: {
      shareCommentary?: {
        text?: string;
      };
      media?: Array<{
        recipes?: string[];
        media?: string;
        title?: {
          text?: string;
        };
        downloadUrl?: string;
        thumbnails?: Array<{
          url?: string;
        }>;
      }>;
    };
  };
  statistics?: {
    clickCount?: number;
    commentCount?: number;
    engagement?: number;
    impressionCount?: number;
    likeCount?: number;
    shareCount?: number;
  };
  visibility?: {
    "com.linkedin.ugc.MemberNetworkVisibility"?: string;
  };
  isAd?: boolean;

  // Legacy fields for compatibility
  page_id?: string;
  created_time?: string;
  message?: string;
  permalink_url?: string;
  likes_count?: number;
  comments_count?: number;
  shares_count?: number;
  engagement_rate?: number;
  post_impressions?: number;
  post_clicks?: number;
  post_reactions?: number;
  post_engagement_rate?: number;
  post_video_views?: number;
  post_video_view_time?: number;
  post_video_completion_rate?: number;
  attachments?: {
    data?: Array<{
      media?: {
        mime_type?: string;
        image?: {
          height?: string;
          width?: string;
          src?: string;
        };
      };
      target?: {
        id?: string;
        url?: string;
      };
    }>;
  };
}

export interface WildcardsResponse {
  data: Array<{
    _source: WildcardsSource;
  }>;
}

export interface GetLinkedInInsightsQueryResponse {
  insights: LinkedInInsights[];
  totalCount: number;
}

export interface LinkedInInsightsQuery {
  "document.body.publishDate.date": {
    $gte: number;
    $lte: number;
  };
  channel: "linkedin";
  "document.metaData.source.id"?: {
    $in: string[];
  };
  "document.body.content.text"?: {
    $regex: string;
    $options: string;
  };
  "document.id"?: string;
}
