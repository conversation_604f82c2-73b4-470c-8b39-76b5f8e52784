import type { IQuery } from "@meltwater/cqrs";
import { LambdaTracer } from "@meltwater/lambda-monitoring";
import type { LinkedInAdAccountRepository } from "../linkedin-ad-account-repository";
import type { LinkedInAdAccount, ExtendedLinkedInAdAccountQueryInput } from "../types/linkedin-ad-account";

const tracer = LambdaTracer.getInstance();

export class GetLinkedInAdAccountsQuery implements IQuery<ExtendedLinkedInAdAccountQueryInput, LinkedInAdAccount[]> {
  constructor(private readonly linkedInAdAccountRepository: LinkedInAdAccountRepository) {}

  @tracer.captureMethod({ subSegmentName: "GetLinkedInAdAccountsQuery:execute" })
  public async execute(query: ExtendedLinkedInAdAccountQueryInput): Promise<LinkedInAdAccount[]> {
    const { accessToken, ...input } = query;
    return this.linkedInAdAccountRepository.getLinkedInAdAccounts(input, accessToken);
  }
}
