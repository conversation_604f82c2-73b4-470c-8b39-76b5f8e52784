import type { IQuery } from "@meltwater/cqrs";
import { LambdaTracer } from "@meltwater/lambda-monitoring";
import type { LinkedInAdAccountRepository } from "../linkedin-ad-account-repository";
import type { ExtendedAssociatedAccountsQueryInput, AssociatedAccountsResponse } from "../types/linkedin-ad-account";

const tracer = LambdaTracer.getInstance();

export class GetLinkedInAssociatedAccountsQuery
  implements IQuery<ExtendedAssociatedAccountsQueryInput, AssociatedAccountsResponse[]>
{
  constructor(private readonly linkedInAdAccountRepository: LinkedInAdAccountRepository) {}

  @tracer.captureMethod({ subSegmentName: "GetLinkedInAssociatedAccountsQuery:execute" })
  public async execute(query: ExtendedAssociatedAccountsQueryInput): Promise<AssociatedAccountsResponse[]> {
    const { accessToken, ...input } = query;
    return this.linkedInAdAccountRepository.getAssociatedAccounts(input, accessToken);
  }
}
