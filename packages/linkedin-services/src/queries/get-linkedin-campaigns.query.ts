import type { IQuery } from "@meltwater/cqrs";
import { LambdaTracer } from "@meltwater/lambda-monitoring";
import type { LinkedInCampaignRepository } from "../linkedin-campaign-repository";
import type { LinkedInCampaign, ExtendedGetLinkedInCampaignsParams } from "../types/linkedin-campaigns";

const tracer = LambdaTracer.getInstance();

export class GetLinkedInCampaignsQuery implements IQuery<ExtendedGetLinkedInCampaignsParams, LinkedInCampaign[]> {
  constructor(private readonly repository: LinkedInCampaignRepository) {}

  @tracer.captureMethod({ subSegmentName: "GetLinkedInCampaignsQuery:execute" })
  public async execute(params: ExtendedGetLinkedInCampaignsParams): Promise<LinkedInCampaign[]> {
    const { accessToken, ...input } = params;
    return this.repository.getCampaigns(input, accessToken);
  }
}
