import { Lambda<PERSON>race<PERSON>, LambdaLogger } from "@meltwater/lambda-monitoring";
import type { GetLinkedInInsightsQueryParams, GetLinkedInInsightsQueryResponse } from "../types/linkedin-insights";
import type { LinkedInInsightsRepository } from "../linkedin-insights-repository";

const tracer = LambdaTracer.getInstance();

export class GetLinkedInInsightsQuery {
  constructor(private readonly repository: LinkedInInsightsRepository) {}

  @tracer.captureMethod({ subSegmentName: "GetLinkedInInsightsQuery:execute" })
  public async execute(params: GetLinkedInInsightsQueryParams): Promise<GetLinkedInInsightsQueryResponse> {
    if (params.sortByMetric) {
      LambdaLogger.getInstance().info(
        "Executing GetLinkedInInsightsQuery with metric sorting (DB First with API Metrics)",
      );
      const result = await this.repository.getLinkedInInsightsSorted(params);
      return { insights: result.insights, totalCount: result.totalCount };
    } else {
      LambdaLogger.getInstance().info("Executing GetLinkedInInsightsQuery with default sorting (DB First)");
      const result = await this.repository.getLinkedInInsights(params);
      return { insights: result.insights, totalCount: result.totalCount };
    }
  }
}
