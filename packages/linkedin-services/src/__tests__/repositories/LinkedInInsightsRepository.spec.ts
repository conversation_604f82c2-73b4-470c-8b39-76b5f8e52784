import { LinkedInInsightsRepository } from "../../linkedin-insights-repository";
import { HttpClient } from "@meltwater/engage-ads-commons";
import type { GetLinkedInInsightsQueryParams, WildcardsResponse } from "../../types/linkedin-insights";

// Mock the database connection
jest.mock("@meltwater/engage-ads-db-sdk", () => {
  const mockFindChain = {
    sort: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    limit: jest.fn().mockResolvedValue([]),
  };

  return {
    connectToDatabase: jest.fn().mockResolvedValue({}),
    WarpzoneModel: {
      find: jest.fn().mockReturnValue(mockFindChain),
      countDocuments: jest.fn().mockResolvedValue(2),
    },
  };
});

// Mock the HTTP client
jest.mock("@meltwater/engage-ads-commons", () => {
  return {
    HttpClient: jest.fn().mockImplementation(() => ({
      get: jest.fn().mockResolvedValue({ data: [] }),
    })),
  };
});

// Mock the lambda monitoring
jest.mock("@meltwater/lambda-monitoring", () => ({
  LambdaTracer: {
    getInstance: () => ({
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      captureMethod: () => (_: unknown, __: string, descriptor: PropertyDescriptor) => descriptor,
    }),
  },
  LambdaLogger: {
    getInstance: () => ({
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
    }),
  },
}));

describe("LinkedInInsightsRepository", () => {
  let repository: LinkedInInsightsRepository;
  let httpClientMock: jest.Mocked<HttpClient>;

  const defaultParams: GetLinkedInInsightsQueryParams = {
    start_date: "2023-01-01",
    end_date: "2023-01-31",
    size: 10,
    order: "desc",
    start_from: 0,
    profileIds: ["urn:li:organization:2414183"],
  };

  const mockDbPost1 = {
    document: {
      id: "urn:li:ugcPost:7260287075084582912",
      documentId: "doc123",
      externalId: "ext123",
      metaData: {
        source: { id: "urn:li:organization:2414183" },
        url: "https://linkedin.com/post/7260287075084582912",
      },
      body: {
        publishDate: { date: new Date("2023-01-01").getTime() },
        content: { text: "Test LinkedIn post" },
      },
      attachments: [
        {
          link: "https://example.com/thumbnail.jpg",
          mimeType: "image/jpeg",
        },
      ],
    },
    companyId: "company123",
  };

  const mockApiMetrics1 = {
    id: "urn:li:ugcPost:7260287075084582912",
    author: "urn:li:organization:2414183",
    created_at: "2023-01-01T00:00:00.000Z",
    permalink_url: "https://linkedin.com/post/7260287075084582912",
    specificContent: {
      "com.linkedin.ugc.ShareContent": {
        shareCommentary: {
          text: "Test LinkedIn post",
        },
        media: [
          {
            thumbnails: [
              {
                url: "https://example.com/api-thumbnail.jpg",
              },
            ],
          },
        ],
      },
    },
    statistics: {
      likeCount: 100,
      commentCount: 50,
      shareCount: 25,
      engagement: 2.5,
      impressionCount: 5000,
      clickCount: 200,
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    httpClientMock = new HttpClient({
      baseURL: "https://example.com",
    }) as jest.Mocked<HttpClient>;
    repository = new LinkedInInsightsRepository(httpClientMock);
  });

  describe("getLinkedInInsightsSorted", () => {
    it("should map sort fields correctly for API call", async () => {
      // Setup mocks
      // Import the mocked module directly
      // eslint-disable-next-line @typescript-eslint/no-var-requires
      const { WarpzoneModel } = require("@meltwater/engage-ads-db-sdk");

      // Set up the mock implementations
      WarpzoneModel.countDocuments.mockResolvedValue(2);

      const mockFindChain = {
        sort: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockResolvedValue([mockDbPost1]),
      };
      WarpzoneModel.find.mockReturnValue(mockFindChain);

      httpClientMock.get.mockResolvedValue({
        data: [{ _source: mockApiMetrics1 }],
      } as WildcardsResponse);

      const params: GetLinkedInInsightsQueryParams = {
        ...defaultParams,
        sortByMetric: true,
        sort_by: "likes_count",
      };

      await repository.getLinkedInInsightsSorted(params);

      // Verify API call has the correct sort-by parameter
      expect(httpClientMock.get).toHaveBeenCalledWith(
        "/linkedin/v1/post/top-docs",
        expect.objectContaining({
          params: expect.objectContaining({
            "sort-by": "statistics.likeCount",
          }),
        }),
      );
    });
  });
});
