import { LinkedInAdAccountRepository } from "../../linkedin-ad-account-repository";
import { HttpClient } from "@meltwater/engage-ads-commons";
import type { LinkedInAdAccountQueryInput } from "../../types/linkedin-ad-account";

jest.mock("@meltwater/engage-ads-commons", () => ({
  HttpClient: jest.fn().mockImplementation((_config) => ({
    post: jest.fn(),
  })),
}));

describe("LinkedInAdAccountRepository", () => {
  let linkedInAdAccountRepository: LinkedInAdAccountRepository;
  let httpClientMock: jest.Mocked<HttpClient>;

  beforeEach(() => {
    jest.clearAllMocks();
    httpClientMock = new HttpClient({
      baseURL: "https://example.com",
    }) as jest.Mocked<HttpClient>;
    linkedInAdAccountRepository = new LinkedInAdAccountRepository(httpClientMock);
  });

  it("should fetch LinkedIn ad accounts", async () => {
    const mockResponse = {
      data: {
        companyCredentialsFilteredQuery: [
          {
            credentialId: 1,
            targetPageName: "Test Page",
            targetPageLogoUrl: "http://example.com/logo.png",
            socialAccountId: "social_account_123",
            channelName: "linkedin",
            tokenId: "token123",
            tokenDetails: {
              token: "abc123",
            },
          },
        ],
      },
    };

    httpClientMock.post.mockResolvedValueOnce(mockResponse);

    const input: LinkedInAdAccountQueryInput = {
      applicationCompanyId: "test-company-id",
      userId: "test-user-id",
      activeInd: 1,
      statusInd: ["VALID"],
      channels: ["linkedin"],
    };

    const accessToken = "mock-access-token";

    const adAccounts = await linkedInAdAccountRepository.getLinkedInAdAccounts(input, accessToken);
    expect(adAccounts).toHaveLength(1);
    expect(adAccounts[0]).toEqual({
      credentialId: 1,
      targetPageName: "Test Page",
      targetPageLogoUrl: "http://example.com/logo.png",
      socialAccountId: "social_account_123",
      channelName: "linkedin",
      tokenId: "token123",
      tokenDetails: {
        token: "abc123",
      },
    });
    expect(httpClientMock.post).toHaveBeenCalledWith("/graphql", expect.any(Object), {
      headers: { Authorization: `${accessToken}` },
    });
  });

  it("should handle errors when fetching LinkedIn ad accounts", async () => {
    const errorMessage = "Network error";
    httpClientMock.post.mockRejectedValueOnce(new Error(errorMessage));

    const input: LinkedInAdAccountQueryInput = {
      applicationCompanyId: "test-company-id",
      userId: "test-user-id",
      activeInd: 1,
      statusInd: ["VALID"],
      channels: ["linkedin"],
    };

    const accessToken = "mock-access-token";

    try {
      await linkedInAdAccountRepository.getLinkedInAdAccounts(input, accessToken);
      // If we reach here, the test should fail
      expect(true).toBe(false);
    } catch (error) {
      if (error instanceof Error) {
        expect(error.message).toBe(errorMessage);
      } else {
        fail("Expected error to be an instance of Error");
      }
    }
  });

  it("should throw an error when response structure is invalid", async () => {
    const mockResponse = {
      data: null,
    };

    httpClientMock.post.mockResolvedValueOnce(mockResponse);

    const input: LinkedInAdAccountQueryInput = {
      applicationCompanyId: "test-company-id",
      userId: "test-user-id",
      activeInd: 1,
      statusInd: ["VALID"],
      channels: ["linkedin"],
    };

    const accessToken = "mock-access-token";

    try {
      await linkedInAdAccountRepository.getLinkedInAdAccounts(input, accessToken);
      // If we reach here, the test should fail
      expect(true).toBe(false);
    } catch (error) {
      if (error instanceof Error) {
        expect(error.message).toBe("Invalid response structure");
      } else {
        fail("Expected error to be an instance of Error");
      }
    }
  });
});
