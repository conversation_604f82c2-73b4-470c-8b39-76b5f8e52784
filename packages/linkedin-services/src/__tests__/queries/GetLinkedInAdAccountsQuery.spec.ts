import { LinkedInAdAccountRepository } from "../../linkedin-ad-account-repository";
import type { LinkedInAdAccount, ExtendedLinkedInAdAccountQueryInput } from "../../types/linkedin-ad-account";
import { GetLinkedInAdAccountsQuery } from "../../queries/get-linkedin-ad-accounts.query";
import { HttpClient } from "@meltwater/engage-ads-commons";

jest.mock("../../linkedin-ad-account-repository");
const LinkedInAdAccountRepositoryMock = LinkedInAdAccountRepository as jest.MockedClass<
  typeof LinkedInAdAccountRepository
>;

jest.mock("@meltwater/engage-ads-commons", () => ({
  HttpClient: jest.fn().mockImplementation((_config) => ({
    post: jest.fn(),
  })),
}));

describe("GetLinkedInAdAccountsQuery", () => {
  let getLinkedInAdAccountsQuery: GetLinkedInAdAccountsQuery;
  let linkedInAdAccountRepositoryMock: jest.Mocked<LinkedInAdAccountRepository>;

  beforeEach(() => {
    jest.clearAllMocks();
    const httpClient = new HttpClient({ baseURL: "https://example.com" });
    linkedInAdAccountRepositoryMock = new LinkedInAdAccountRepositoryMock(
      httpClient,
    ) as jest.Mocked<LinkedInAdAccountRepository>;
    getLinkedInAdAccountsQuery = new GetLinkedInAdAccountsQuery(linkedInAdAccountRepositoryMock);
  });

  it("should execute the query and return LinkedIn ad accounts", async () => {
    const mockAdAccounts: LinkedInAdAccount[] = [
      {
        credentialId: 1,
        targetPageName: "Test Page",
        targetPageLogoUrl: "http://example.com/logo.png",
        socialAccountId: "social_account_123",
        channelName: "LinkedIn",
        tokenId: "token123",
        tokenDetails: {
          token: "abc123",
        },
      },
    ];

    linkedInAdAccountRepositoryMock.getLinkedInAdAccounts.mockResolvedValueOnce(mockAdAccounts);

    const input: ExtendedLinkedInAdAccountQueryInput = {
      applicationCompanyId: "test-company-id",
      userId: "test-user-id",
      activeInd: 1,
      statusInd: ["VALID"],
      channels: ["linkedin"],
      accessToken: "test-access-token",
    };

    const result = await getLinkedInAdAccountsQuery.execute(input);

    expect(result).toEqual(mockAdAccounts);
    expect(linkedInAdAccountRepositoryMock.getLinkedInAdAccounts).toHaveBeenCalledWith(
      {
        applicationCompanyId: "test-company-id",
        userId: "test-user-id",
        activeInd: 1,
        statusInd: ["VALID"],
        channels: ["linkedin"],
      },
      "test-access-token",
    );
  });
});
