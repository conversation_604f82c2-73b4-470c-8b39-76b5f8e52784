import type { HttpClient } from "@meltwater/engage-ads-commons";
import { LinkedInInsightsRepository } from "../../linkedin-insights-repository";
import { GetLinkedInInsightsQuery } from "../../queries/get-linkedin-insights.query";
import type { GetLinkedInInsightsQueryParams } from "../../types/linkedin-insights";

const mockRepository = {
  getLinkedInInsights: jest.fn(),
  getLinkedInInsightsSorted: jest.fn(),
};

jest.mock("../../linkedin-insights-repository", () => ({
  LinkedInInsightsRepository: jest.fn().mockImplementation(() => mockRepository),
}));

jest.mock("@meltwater/lambda-monitoring", () => ({
  LambdaTracer: {
    getInstance: () => ({
      captureMethod: () => (target: unknown, propertyKey: string, descriptor: PropertyDescriptor) => descriptor,
    }),
  },
  LambdaLogger: {
    getInstance: () => ({
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
    }),
  },
}));

describe("GetLinkedInInsightsQuery", () => {
  let query: GetLinkedInInsightsQuery;

  const mockResponse = {
    insights: [
      {
        id: "urn:li:ugcPost:7260287075084582912",
        document_id: "doc123",
        externalId: "ext123",
        page_id: "urn:li:organization:2414183",
        created_time: "2023-01-01T00:00:00.000Z",
        message: "Test LinkedIn post",
        permalink_url: "https://linkedin.com/post/7260287075084582912",
        thumbnail_url: "https://example.com/thumbnail.jpg",
        mimeType: "image/jpeg",
        company_id: "company123",
        likes_count: 100,
        comments_count: 50,
        shares_count: 25,
        engagement_rate: 2.5,
        post_impressions: 5000,
        post_clicks: 200,
        post_reactions: 100,
        post_engagement_rate: 2.5,
        post_video_views: 0,
        post_video_view_time: 0,
        post_video_completion_rate: 0,
        schedule_end_time: null,
      },
    ],
    totalCount: 1,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    query = new GetLinkedInInsightsQuery(new LinkedInInsightsRepository({} as HttpClient));
    mockRepository.getLinkedInInsights.mockResolvedValue(mockResponse);
    mockRepository.getLinkedInInsightsSorted.mockResolvedValue(mockResponse);
  });

  it("should execute query with default sorting and return insights", async () => {
    const params: GetLinkedInInsightsQueryParams = {
      start_date: "2023-01-01",
      end_date: "2023-01-31",
      size: 10,
      order: "desc",
      start_from: 0,
      sortByMetric: false,
    };

    const result = await query.execute(params);

    expect(mockRepository.getLinkedInInsights).toHaveBeenCalledWith(params);
    expect(mockRepository.getLinkedInInsightsSorted).not.toHaveBeenCalled();
    expect(result).toEqual(mockResponse);
  });

  it("should execute query with metric sorting and return insights", async () => {
    const params: GetLinkedInInsightsQueryParams = {
      start_date: "2023-01-01",
      end_date: "2023-01-31",
      size: 10,
      order: "desc",
      start_from: 0,
      sortByMetric: true,
      sort_by: "likes_count",
    };

    const result = await query.execute(params);

    expect(mockRepository.getLinkedInInsightsSorted).toHaveBeenCalledWith(params);
    expect(mockRepository.getLinkedInInsights).not.toHaveBeenCalled();
    expect(result).toEqual(mockResponse);
  });

  it("should handle errors from repository", async () => {
    const params: GetLinkedInInsightsQueryParams = {
      start_date: "2023-01-01",
      end_date: "2023-01-31",
      size: 10,
      order: "desc",
      start_from: 0,
      sortByMetric: true,
      sort_by: "likes_count",
    };

    const error = new Error("Repository error");
    mockRepository.getLinkedInInsightsSorted.mockRejectedValueOnce(error);

    await expect(query.execute(params)).rejects.toThrow("Repository error");
  });
});
