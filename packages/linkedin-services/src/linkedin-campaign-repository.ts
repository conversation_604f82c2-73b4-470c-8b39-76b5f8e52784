import type { HttpClient } from "@meltwater/engage-ads-commons";
import { LambdaTracer, LambdaLogger } from "@meltwater/lambda-monitoring";
import type {
  LinkedInCampaign,
  GetLinkedInCampaignsParams,
  LinkedInCampaignsApiResponse,
} from "./types/linkedin-campaigns";

const tracer = LambdaTracer.getInstance();
const logger = LambdaLogger.getInstance();

export class LinkedInCampaignRepository {
  constructor(private readonly httpClient: HttpClient) {}

  @tracer.captureMethod({ subSegmentName: "LinkedInCampaignRepository:getCampaigns" })
  public async getCampaigns(params: GetLinkedInCampaignsParams, accessToken: string): Promise<LinkedInCampaign[]> {
    const { adAccountId, status, type } = params;

    const endpoint = `/rest/adAccounts/${adAccountId}/adCampaigns`;

    const searchParts: string[] = [];
    if (type) searchParts.push(`type:(values:List(${type}))`);
    if (status) searchParts.push(`status:(values:List(${status}))`);

    const searchQuery = searchParts.length > 0 ? `(${searchParts.join(",")})` : undefined;

    const requestParams: Record<string, string> = { q: "search" };
    if (searchQuery) requestParams["search"] = searchQuery;
    // Provide default DESCENDING sort order like LinkedIn sample
    requestParams["sortOrder"] = "DESCENDING";

    logger.info("Fetching LinkedIn campaigns", { endpoint, requestParams, hasToken: !!accessToken });

    const response = await this.httpClient.get<LinkedInCampaignsApiResponse>(endpoint, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "X-Restli-Protocol-Version": "2.0.0",
        "Linkedin-Version": process.env.LINKEDIN_VERSION || "202504",
      },
      params: requestParams,
    });

    if (!response || !response.elements) {
      logger.error("Invalid LinkedIn campaign response", {
        response: JSON.stringify(response),
      });
      return [];
    }

    return response.elements;
  }
}
