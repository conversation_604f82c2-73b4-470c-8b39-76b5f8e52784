import { <PERSON><PERSON><PERSON>ogger } from "@meltwater/lambda-monitoring";
import type { HttpClient } from "../clients";
import type { StreamUploadResult } from "./interfaces";
import type { ChunkUploader } from "./interfaces/chunk-uploader";

const logger = LambdaLogger.getInstance();

export async function streamUpload<S>(
  httpClient: HttpClient,
  startParams: S,
  uploader: ChunkUploader<S>,
  downloadUrl: string,
  chunkSize: number = 10 * 1024 * 1024, // 10MB
  timeoutMinutes: number = 10, // default timeout of 10 minutes
  resumeConfiguration?: { sessionId: string; resumeOffset: number; fileSize: number },
): Promise<StreamUploadResult> {
  const startTime = Date.now();
  const sessionId = resumeConfiguration?.sessionId ?? (await uploader.startUpload(startParams));
  const finalOffset = resumeConfiguration?.fileSize ?? uploader.getFileSize();

  let startOffset = resumeConfiguration?.resumeOffset ?? 0;

  logger.info(resumeConfiguration ? "Resuming upload session" : "Starting new upload session", {
    sessionId,
    startParams: resumeConfiguration ? undefined : startParams,
    startOffset,
    finalOffset,
  });

  while (startOffset < finalOffset) {
    const endTime = (Date.now() - startTime) / 60000;
    if (endTime > timeoutMinutes) {
      logger.info("Timeout exceeded, preparing to resume upload", { startOffset, sessionId });
      return { sessionId, resumeOffset: startOffset, data: uploader.getData() };
    }

    const endRange = Math.min(startOffset + chunkSize, finalOffset) - 1;
    const rangeHeader = `bytes=${startOffset}-${endRange}`;
    const stream = await httpClient.downloadStream(downloadUrl, { headers: { Range: rangeHeader } });

    const buffer = await new Promise<Buffer>((resolve, reject) => {
      const chunks: Buffer[] = [];
      stream.on("data", (chunk) => chunks.push(chunk));
      stream.on("end", () => resolve(Buffer.concat(chunks)));
      stream.on("error", reject);
    });

    try {
      logger.info("Uploading chunk", { startOffset, size: buffer.length });
      await uploader.uploadChunk(sessionId, buffer, startOffset);
      startOffset += buffer.length;
    } catch (error) {
      logger.error("Error uploading chunk", { error, startOffset, size: buffer.length });
      throw error;
    }
  }

  const response = await uploader.completeUpload(sessionId);

  return { sessionId, data: response };
}
