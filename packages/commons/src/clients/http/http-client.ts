import axios from "axios";
import FormData from "form-data";
import type { Readable } from "stream";

import type { AxiosInstance, AxiosRequestConfig, AxiosError, AxiosResponse } from "axios";
import type { FieldValue, HttpClientConfig, RetryConfig } from "./types";

import { LambdaLogger, LambdaTracer } from "@meltwater/lambda-monitoring";
import { EngageHttpError } from "@meltwater/engage-ads-types";

const tracer = LambdaTracer.getInstance();
const logger = LambdaLogger.getInstance();

interface ExtendedAxiosRequestConfig extends AxiosRequestConfig {
  __retryCount?: number;
}

interface ApiErrorResponse {
  message: string;
}

export class HttpClient {
  private axiosInstance: AxiosInstance;
  private retryConfig: RetryConfig;

  constructor(config: HttpClientConfig) {
    this.axiosInstance = axios.create({
      baseURL: config.baseURL,
      headers: config.defaultHeaders,
    });

    this.retryConfig = {
      attempts: config.retryConfig?.attempts ?? 3,
      backoffType: config.retryConfig?.backoffType ?? "linear",
      initialDelay: config.retryConfig?.initialDelay ?? 1000,
    };

    if (config.authToken) {
      this.setAuthToken(config.authToken, config.tokenType);
    }

    this.initializeInterceptors();
  }

  private initializeInterceptors() {
    // Add request interceptor to log headers
    this.axiosInstance.interceptors.request.use((config) => {
      logger.info("Outgoing request headers:", {
        url: config.url,
        method: config.method,
        headers: config.headers,
        defaultHeaders: this.axiosInstance.defaults.headers.common,
      });
      return config;
    });

    this.axiosInstance.interceptors.response.use(this.handleResponse, this.handleErrorWithRetry);
  }

  private handleResponse = (response: AxiosResponse) => response;

  private handleErrorWithRetry = async (error: AxiosError) => {
    if (!error.config) {
      return Promise.reject(this.formatError(error));
    }

    const config = error.config as ExtendedAxiosRequestConfig;
    config.__retryCount = config.__retryCount ?? 0;
    if (config.__retryCount >= this.retryConfig.attempts) {
      return Promise.reject(this.formatError(error));
    }
    config.__retryCount += 1;

    // Calculate delay for retry
    let delay = this.retryConfig.initialDelay;
    if (this.retryConfig.backoffType === "exponential") {
      delay *= Math.pow(2, config.__retryCount - 1);
    }

    // Retry with delay
    return new Promise((resolve) => setTimeout(() => resolve(this.axiosInstance(config)), delay));
  };

  private formatError(error: AxiosError<unknown, unknown>): Error {
    let message = error.message;

    if (error.response) {
      logger.error("Error in HTTP request", {
        error: {
          request: {
            method: error.config?.method,
            baseUrl: error.config?.baseURL,
            url: error.config?.url,
            headers:
              error.response.status !== 401
                ? Object.fromEntries(
                    Object.entries(error.config?.headers ?? {}).filter(([key]) => key !== "Authorization"),
                  ) // We don't want to log the auth token
                : error.config?.headers,
            data: error.config?.data ?? "N/A",
            params: error.config?.params ?? "N/A",
          },
          response: {
            data: error.response.data,
            headers: error.response.headers,
            status: error.response.status,
          },
        },
      });

      const response = error.response;

      if (response.data && (response.data as ApiErrorResponse).message) {
        message = (response.data as ApiErrorResponse).message;
      } else {
        message = `${response.status} ${response.statusText}`;
      }

      throw new EngageHttpError(response.status, message, response.data, response.headers);
    }

    return new Error(`${message} (AxiosError: ${error.message})`);
  }

  public setAuthToken(token: string, tokenType: string | undefined) {
    if (tokenType === "Bearer") {
      this.axiosInstance.defaults.headers.common["Authorization"] = `${tokenType} ${token}`;
    } else if (tokenType === "Access-Token") {
      this.axiosInstance.defaults.headers.common["Access-Token"] = `${token}`;
    } else {
      this.axiosInstance.defaults.headers.common["Authorization"] = token;
    }
  }

  @tracer.captureMethod({
    captureResponse: true,
  })
  public async head<T>(url: string, config?: AxiosRequestConfig): Promise<{ data: T; headers: Headers }> {
    tracer.putMetadata("Input Params", { url, config });
    logger.info(`GET: ${url}`);

    const response = await this.axiosInstance.head<T, { data: T; headers: Headers }>(url, config);
    return {
      data: response.data,
      headers: response.headers,
    };
  }

  @tracer.captureMethod({
    captureResponse: true,
  })
  public async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    tracer.putMetadata("Input Params", { url, config });
    logger.info(`GET: ${url}`);

    const response = await this.axiosInstance.get<T>(url, config);
    return response.data;
  }

  @tracer.captureMethod({
    captureResponse: true,
  })
  public async post<T, R = T>(url: string, data?: T, config?: AxiosRequestConfig): Promise<R> {
    tracer.putMetadata("Input Params", { url, config, data });
    const response = await this.axiosInstance.post<R>(url, data, config);
    return response.data;
  }

  /**
   * Sends a multipart/form-data POST request.
   * @param url The endpoint URL.
   * @param fields Fields to be sent as form data. Each field can be a direct value or an object with value and options.
   * @param config Optional Axios request configuration.
   */
  @tracer.captureMethod({
    captureResponse: true,
  })
  public async postFormData<T, F extends Record<string, FieldValue>>(
    url: string,
    fields: F,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    const formData = new FormData();

    for (const [key, value] of Object.entries(fields)) {
      if (typeof value === "object" && "value" in value && "options" in value) {
        formData.append(key, value.value, value.options);
      } else {
        formData.append(key, value as Blob | Buffer | NodeJS.ReadableStream | string | number);
      }
    }

    const formDataHeaders = formData.getHeaders();
    const finalConfig = {
      ...config,
      headers: {
        ...config?.headers,
        ...formDataHeaders,
      },
      method: "POST",
      data: formData,
    };

    return (await this.axiosInstance.post<T>(url, formData, finalConfig)).data;
  }

  @tracer.captureMethod({
    subSegmentName: "http-client:graphql",
    captureResponse: true,
  })
  public async graphql<T, V = undefined>(url: string, query: string, variables?: V): Promise<T> {
    const data = {
      query,
      variables,
    };

    logger.info(`GRAPHQL: ${url}`, {
      query: query.replace(/^\s+|\s+$/g, "").replace(/\s+/g, " "),
      variables,
    });

    const response = await this.axiosInstance.post<{ data: T }>(url, data);
    return response.data.data;
  }

  public async patch<T, R = T>(url: string, data?: T, config?: AxiosRequestConfig): Promise<R> {
    logger.info(`PATCH: ${url}`, { data });
    const response = await this.axiosInstance.patch<R>(url, data, config);
    return response.data;
  }

  @tracer.captureMethod({
    subSegmentName: "http-client:downloadStream",
    captureResponse: false,
  })
  public async downloadStream(url: string, config?: AxiosRequestConfig): Promise<Readable> {
    const response = await this.axiosInstance({
      url,
      method: "GET",
      responseType: "stream",
      ...config,
    });

    return response.data;
  }

  /**
   * Executes a POST request and returns both response data and headers. Useful when
   * important metadata (e.g., LinkedIn IDs in `x-restli-id` header) is returned
   * outside the body.
   */
  @tracer.captureMethod({
    captureResponse: true,
  })
  public async postWithHeaders<T, R = T>(
    url: string,
    data?: T,
    config?: AxiosRequestConfig,
  ): Promise<{ data: R; headers: Record<string, unknown> }> {
    tracer.putMetadata("Input Params", { url, config, data });
    const response = await this.axiosInstance.post<R>(url, data, config);
    return { data: response.data, headers: response.headers as Record<string, unknown> };
  }
}
