{"name": "@meltwater/engage-ads-commons", "version": "2.9.2", "author": "Team Grimoire <<EMAIL>>", "dependencies": {"@meltwater/engage-ads-types": "^2.5.3", "@meltwater/lambda-monitoring": "^0.2.5", "axios": "^1.8.4", "form-data": "^4.0.0", "stream-to-blob": "^2.0.1"}, "devDependencies": {"@types/stream-to-blob": "^2.0.0", "typescript": "^5.3.3"}, "files": ["dist"], "license": "MIT", "main": "dist/index.js", "scripts": {"build": "tsc", "clean": "rimraf dist tsconfig.tsbuildinfo", "registry:clear-cache": "bash ../../scripts/mpkg-clear-cache.sh $(echo $npm_package_name | cut -d '/' -f 2) $npm_package_version"}, "types": "dist/index.d.ts", "publishConfig": {"access": "restricted", "registry": "https://registry.npmjs.org/"}, "mpkg": ["dist/**/*"], "gitHead": "e97bf6a5ffadcb86a96997235fff752d5bc4ebd8"}