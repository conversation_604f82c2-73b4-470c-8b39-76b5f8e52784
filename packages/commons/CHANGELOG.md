# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [2.6.7](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-commons@2.6.6...@meltwater/engage-ads-commons@2.6.7) (2024-06-25)

**Note:** Version bump only for package @meltwater/engage-ads-commons

## [2.6.6](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-commons@2.6.5...@meltwater/engage-ads-commons@2.6.6) (2024-06-18)

**Note:** Version bump only for package @meltwater/engage-ads-commons

## [2.6.5](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-commons@2.6.4...@meltwater/engage-ads-commons@2.6.5) (2024-06-13)

**Note:** Version bump only for package @meltwater/engage-ads-commons

## [2.6.4](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-commons@2.6.3...@meltwater/engage-ads-commons@2.6.4) (2024-06-12)

**Note:** Version bump only for package @meltwater/engage-ads-commons

## [2.6.3](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-commons@2.6.2...@meltwater/engage-ads-commons@2.6.3) (2024-06-10)

**Note:** Version bump only for package @meltwater/engage-ads-commons

## 2.6.2 (2024-06-01)

**Note:** Version bump only for package @meltwater/engage-ads-commons

## [2.6.1](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-commons@2.6.0...@meltwater/grimoire-commons@2.6.1) (2024-05-31)

**Note:** Version bump only for package @meltwater/grimoire-commons

# [2.6.0](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-commons@2.5.1...@meltwater/grimoire-commons@2.6.0) (2024-05-29)

### Features

- Adding TikTok Lambda and code ([#68](https://github.com/meltwater/grimoire-commons/issues/68)) ([87f0912](https://github.com/meltwater/grimoire-commons/commit/87f09122226ab5c22d68432692dfc680439cb86e))

## [2.5.2-ENGAGE.0](https://github.com/meltwater/grimoire-lambdas/compare/@meltwater/grimoire-commons@2.5.1...@meltwater/grimoire-commons@2.5.2-ENGAGE.0) (2024-05-28)

### Bug Fixes

- Adding TikTok Lambda and code ([e043c9a](https://github.com/meltwater/grimoire-lambdas/commit/e043c9a0ddcf6a648c79a42d722932a772108232))

## [2.5.1](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-commons@2.5.0...@meltwater/grimoire-commons@2.5.1) (2024-05-28)

### Bug Fixes

- switch to section 31 graphql credential endpoint ([#60](https://github.com/meltwater/grimoire-commons/issues/60)) ([ba3186c](https://github.com/meltwater/grimoire-commons/commit/ba3186ceeca20ee8317c1dc2021c802090acac0a))

## [2.5.1-ENGAGE.1](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-commons@2.5.1-ENGAGE.0...@meltwater/grimoire-commons@2.5.1-ENGAGE.1) (2024-05-15)

### Bug Fixes

- env var for s31 graphql key ([fb1d329](https://github.com/meltwater/grimoire-commons/commit/fb1d3296aaf2ef23e012b220a08270a3d6c0862c))

## [2.5.1-ENGAGE.0](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-commons@2.5.0...@meltwater/grimoire-commons@2.5.1-ENGAGE.0) (2024-05-15)

### Bug Fixes

- destroy offset if no more chunks are left ([0ee87e0](https://github.com/meltwater/grimoire-commons/commit/0ee87e06430a71924b491cfe3df6f6830d5d2335))
- file size is needed to finish resumption flow ([490aeec](https://github.com/meltwater/grimoire-commons/commit/490aeeca87c33a00f0ea8c5f0dc9bad0efab6029))
- incorrect import ([d9b6945](https://github.com/meltwater/grimoire-commons/commit/d9b69455c820a90cdf75711be2a3b355f239d7ab))
- resumable update ([3781add](https://github.com/meltwater/grimoire-commons/commit/3781add140458ec18dde47780a7d835b4944580e))
- resuming upload ([a57fb10](https://github.com/meltwater/grimoire-commons/commit/a57fb10c1951466801b256b0fd487c03467359f0))
- update handler to handle both sqs triggered events and direct invocations ([00977d9](https://github.com/meltwater/grimoire-commons/commit/00977d946b49980aeae565ea4ffd9da2d3ac30b2))
- use range header to only fetch the chunk to upload ([210a93c](https://github.com/meltwater/grimoire-commons/commit/210a93c9d8d0ad0299f7b30047c15ff362c9882e))
- use range header to start from resume offset when resuming download ([87922d0](https://github.com/meltwater/grimoire-commons/commit/87922d0b05c68cc7bee8b7fdbd7a5803da4a918a))

### Features

- add resumable upload ([ba0f3b3](https://github.com/meltwater/grimoire-commons/commit/ba0f3b3d630a2e995cb795637dc69305e4b01e80))
- resumable upload ([22c03cd](https://github.com/meltwater/grimoire-commons/commit/22c03cdb687d4672f920be3bc422f9bb00e56576))
- use resumable upload to publish videos to facebook ([effc472](https://github.com/meltwater/grimoire-commons/commit/effc472d9d2c3fcb14e064ab7243862e7e4bc88e))

# [2.5.0](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-commons@2.4.0...@meltwater/grimoire-commons@2.5.0) (2024-05-15)

### Features

- add resumable upload ([#57](https://github.com/meltwater/grimoire-commons/issues/57)) ([5f949fb](https://github.com/meltwater/grimoire-commons/commit/5f949fb0c3dcf8a36b696c49356b314a133e2a23))

## [2.4.2-ENGAGE.9](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-commons@2.4.2-ENGAGE.8...@meltwater/grimoire-commons@2.4.2-ENGAGE.9) (2024-05-15)

**Note:** Version bump only for package @meltwater/grimoire-commons

## [2.4.2-ENGAGE.8](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-commons@2.4.2-ENGAGE.7...@meltwater/grimoire-commons@2.4.2-ENGAGE.8) (2024-05-14)

### Bug Fixes

- facebook video thumbnail upload ([f9ca4de](https://github.com/meltwater/grimoire-commons/commit/f9ca4debce75097c13ff08d5471d3deae7b15b43))

## [2.4.2-ENGAGE.7](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-commons@2.4.2-ENGAGE.6...@meltwater/grimoire-commons@2.4.2-ENGAGE.7) (2024-05-13)

### Bug Fixes

- use range header to only fetch the chunk to upload ([210a93c](https://github.com/meltwater/grimoire-commons/commit/210a93c9d8d0ad0299f7b30047c15ff362c9882e))

## [2.4.2-ENGAGE.6](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-commons@2.4.2-ENGAGE.5...@meltwater/grimoire-commons@2.4.2-ENGAGE.6) (2024-05-13)

### Bug Fixes

- destroy offset if no more chunks are left ([0ee87e0](https://github.com/meltwater/grimoire-commons/commit/0ee87e06430a71924b491cfe3df6f6830d5d2335))

## [2.4.2-ENGAGE.5](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-commons@2.4.2-ENGAGE.4...@meltwater/grimoire-commons@2.4.2-ENGAGE.5) (2024-05-13)

### Bug Fixes

- file size is needed to finish resumption flow ([490aeec](https://github.com/meltwater/grimoire-commons/commit/490aeeca87c33a00f0ea8c5f0dc9bad0efab6029))

## [2.4.2-ENGAGE.4](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-commons@2.4.2-ENGAGE.3...@meltwater/grimoire-commons@2.4.2-ENGAGE.4) (2024-05-13)

### Bug Fixes

- resuming upload ([a57fb10](https://github.com/meltwater/grimoire-commons/commit/a57fb10c1951466801b256b0fd487c03467359f0))

## [2.4.2-ENGAGE.3](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-commons@2.4.2-ENGAGE.2...@meltwater/grimoire-commons@2.4.2-ENGAGE.3) (2024-05-13)

### Bug Fixes

- use range header to start from resume offset when resuming download ([87922d0](https://github.com/meltwater/grimoire-commons/commit/87922d0b05c68cc7bee8b7fdbd7a5803da4a918a))

## [2.4.2-ENGAGE.2](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-commons@2.4.2-ENGAGE.1...@meltwater/grimoire-commons@2.4.2-ENGAGE.2) (2024-05-09)

### Bug Fixes

- update handler to handle both sqs triggered events and direct invocations ([00977d9](https://github.com/meltwater/grimoire-commons/commit/00977d946b49980aeae565ea4ffd9da2d3ac30b2))

## [2.4.2-ENGAGE.1](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-commons@2.4.2-ENGAGE.0...@meltwater/grimoire-commons@2.4.2-ENGAGE.1) (2024-05-09)

### Bug Fixes

- incorrect import ([d9b6945](https://github.com/meltwater/grimoire-commons/commit/d9b69455c820a90cdf75711be2a3b355f239d7ab))
- resumable update ([3781add](https://github.com/meltwater/grimoire-commons/commit/3781add140458ec18dde47780a7d835b4944580e))

### Features

- resumable upload ([22c03cd](https://github.com/meltwater/grimoire-commons/commit/22c03cdb687d4672f920be3bc422f9bb00e56576))

## [2.4.2-ENGAGE.0](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-commons@2.4.1-ENGAGE.0...@meltwater/grimoire-commons@2.4.2-ENGAGE.0) (2024-05-03)

**Note:** Version bump only for package @meltwater/grimoire-commons

## [2.4.1](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-commons@2.4.1-ENGAGE.0...@meltwater/grimoire-commons@2.4.1) (2024-05-03)

**Note:** Version bump only for package @meltwater/grimoire-commons

## [2.4.1-ENGAGE.0](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-commons@2.4.0...@meltwater/grimoire-commons@2.4.1-ENGAGE.0) (2024-05-02)

### Features

- add resumable upload ([ba0f3b3](https://github.com/meltwater/grimoire-commons/commit/ba0f3b3d630a2e995cb795637dc69305e4b01e80))
- use resumable upload to publish videos to facebook ([effc472](https://github.com/meltwater/grimoire-commons/commit/effc472d9d2c3fcb14e064ab7243862e7e4bc88e))

# [2.4.0](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-commons@2.2.0...@meltwater/grimoire-commons@2.4.0) (2024-04-09)

### Bug Fixes

- add IG Collaborators & update names of packages to be associated with our team on NPM ([#48](https://github.com/meltwater/grimoire-commons/issues/48)) ([2a3dcda](https://github.com/meltwater/grimoire-commons/commit/2a3dcda4d00ff19baec286c7c8ec5fdcf3bc7d6b))

### Features

- Adding Facebook Publish Lambda ([#53](https://github.com/meltwater/grimoire-commons/issues/53)) ([6a512bf](https://github.com/meltwater/grimoire-commons/commit/6a512bf0be3b6316084ecd898a502cd8b7c9a480))

## [2.3.1-ENGAGE.3](https://github.com/meltwater/grimoire-lambdas/compare/@meltwater/grimoire-commons@2.3.1-ENGAGE.2...@meltwater/grimoire-commons@2.3.1-ENGAGE.3) (2024-04-08)

**Note:** Version bump only for package @meltwater/grimoire-commons

## [2.3.1-ENGAGE.2](https://github.com/meltwater/grimoire-lambdas/compare/@meltwater/grimoire-commons@2.3.1-ENGAGE.1...@meltwater/grimoire-commons@2.3.1-ENGAGE.2) (2024-04-08)

**Note:** Version bump only for package @meltwater/grimoire-commons

## [2.3.1-ENGAGE.1](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-commons@2.3.1-ENGAGE.0...@meltwater/grimoire-commons@2.3.1-ENGAGE.1) (2024-04-08)

### Bug Fixes

- builds and versions and packages ([76c5a8a](https://github.com/meltwater/grimoire-commons/commit/76c5a8a9e8e19360a401048691e9bb3e7559b50a))

## [2.3.1-ENGAGE.0](https://github.com/meltwater/grimoire-lambdas/compare/@meltwater/grimoire-commons@2.2.0...@meltwater/grimoire-commons@2.3.1-ENGAGE.0) (2024-04-08)

### Bug Fixes

- add IG Collaborators & update names of packages to be associated with our team on NPM ([#48](https://github.com/meltwater/grimoire-lambdas/issues/48)) ([2a3dcda](https://github.com/meltwater/grimoire-lambdas/commit/2a3dcda4d00ff19baec286c7c8ec5fdcf3bc7d6b))
- adding video publish for facebook using non resumable api. ([62e33f7](https://github.com/meltwater/grimoire-lambdas/commit/62e33f73eb25df04ba620474eb06bbbfdae5dc01))
- defect related to setting credentials ([8960f01](https://github.com/meltwater/grimoire-lambdas/commit/8960f018b03b1bfeed4a1d9d169e3a422a02c8e0))
- missed renaming @meltwater/grimoire-lambda-monitoring ([2513d48](https://github.com/meltwater/grimoire-lambdas/commit/2513d48b9f10d1fdbc266b5c7ef9a8868bb3bc58))
- renaming bunch of packages back to the way it was ([106cc7b](https://github.com/meltwater/grimoire-lambdas/commit/106cc7be6aa046fb45b537a71014816d5244f593))

### Features

- Adding Facebook Publish Lambda ([261ee73](https://github.com/meltwater/grimoire-lambdas/commit/261ee7361fe1f8ae114840c398908d918dab185c))

# 2.3.0 (2024-04-08)

### Bug Fixes

- add IG Collaborators & update names of packages to be associated with our team on NPM ([#48](https://github.com/meltwater/grimoire-commons/issues/48)) ([2a3dcda](https://github.com/meltwater/grimoire-commons/commit/2a3dcda4d00ff19baec286c7c8ec5fdcf3bc7d6b))
- add more product tagging fields ([#38](https://github.com/meltwater/grimoire-commons/issues/38)) ([7457990](https://github.com/meltwater/grimoire-commons/commit/7457990c416155963df83707fe2f443a141b41db))
- add skuId to message schema ([#41](https://github.com/meltwater/grimoire-commons/issues/41)) ([548cf12](https://github.com/meltwater/grimoire-commons/commit/548cf123a24a67ff79e8bcfb521aa96eee6f5271))

### Features

- Instagram Product tagging ([#42](https://github.com/meltwater/grimoire-commons/issues/42)) ([1efac54](https://github.com/meltwater/grimoire-commons/commit/1efac54b934f6cfabd5616cd04b025f86e6c397f))
- instagram publish ([#24](https://github.com/meltwater/grimoire-commons/issues/24)) ([e1f2f2a](https://github.com/meltwater/grimoire-commons/commit/e1f2f2a035f33302edca266e22310e9b1c344e75))
- instagram publisher ([#19](https://github.com/meltwater/grimoire-commons/issues/19)) ([3e13113](https://github.com/meltwater/grimoire-commons/commit/3e13113971da30fe1d27d4fe418ac8b76ab24bc5))

## 2.2.1-ENGAGE.2 (2024-03-27)

### Bug Fixes

- add more product tagging fields ([#38](https://github.com/meltwater/grimoire-commons/issues/38)) ([7457990](https://github.com/meltwater/grimoire-commons/commit/7457990c416155963df83707fe2f443a141b41db))
- add skuId to message schema ([#41](https://github.com/meltwater/grimoire-commons/issues/41)) ([548cf12](https://github.com/meltwater/grimoire-commons/commit/548cf123a24a67ff79e8bcfb521aa96eee6f5271))
- bump monitoring ([083008a](https://github.com/meltwater/grimoire-commons/commit/083008aa5d6853c29eebe1343c91ed000590a3fe))
- monitoring does not require types ([9b8143b](https://github.com/meltwater/grimoire-commons/commit/9b8143bfdc65520fa93cc8b50fe1465c7ad07df8))
- **monitoring:** rename package ([dd41794](https://github.com/meltwater/grimoire-commons/commit/dd417948f4a074d486406ba7c13369f11f2e9ce6))
- rename packages for publishing to work ([3c240c1](https://github.com/meltwater/grimoire-commons/commit/3c240c139bec3298a8d111e5e8981c498162a940))

### Features

- Instagram Product tagging ([#42](https://github.com/meltwater/grimoire-commons/issues/42)) ([1efac54](https://github.com/meltwater/grimoire-commons/commit/1efac54b934f6cfabd5616cd04b025f86e6c397f))
- instagram publish ([#24](https://github.com/meltwater/grimoire-commons/issues/24)) ([e1f2f2a](https://github.com/meltwater/grimoire-commons/commit/e1f2f2a035f33302edca266e22310e9b1c344e75))
- instagram publisher ([#19](https://github.com/meltwater/grimoire-commons/issues/19)) ([3e13113](https://github.com/meltwater/grimoire-commons/commit/3e13113971da30fe1d27d4fe418ac8b76ab24bc5))

## [2.2.1-ENGAGE.1](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-common@2.2.1-ENGAGE.0...@meltwater/grimoire-common@2.2.1-ENGAGE.1) (2024-03-27)

### Bug Fixes

- bump monitoring ([083008a](https://github.com/meltwater/grimoire-commons/commit/083008aa5d6853c29eebe1343c91ed000590a3fe))
- monitoring does not require types ([9b8143b](https://github.com/meltwater/grimoire-commons/commit/9b8143bfdc65520fa93cc8b50fe1465c7ad07df8))
- **monitoring:** rename package ([dd41794](https://github.com/meltwater/grimoire-commons/commit/dd417948f4a074d486406ba7c13369f11f2e9ce6))

## [2.2.1-ENGAGE.0](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-common@2.2.0...@meltwater/grimoire-common@2.2.1-ENGAGE.0) (2024-03-18)

**Note:** Version bump only for package @meltwater/grimoire-common

# [2.2.0](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-common@2.1.2...@meltwater/grimoire-common@2.2.0) (2024-02-23)

### Features

- Instagram Product tagging ([#42](https://github.com/meltwater/grimoire-commons/issues/42)) ([1efac54](https://github.com/meltwater/grimoire-commons/commit/1efac54b934f6cfabd5616cd04b025f86e6c397f))

## [2.1.3-ENGAGE.0](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-common@2.1.2...@meltwater/grimoire-common@2.1.3-ENGAGE.0) (2024-02-23)

**Note:** Version bump only for package @meltwater/grimoire-common

## [2.1.2](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-common@2.1.1...@meltwater/grimoire-common@2.1.2) (2024-02-23)

### Bug Fixes

- add skuId to message schema ([#41](https://github.com/meltwater/grimoire-commons/issues/41)) ([548cf12](https://github.com/meltwater/grimoire-commons/commit/548cf123a24a67ff79e8bcfb521aa96eee6f5271))

## [2.1.2-ENGAGE.0](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-common@2.1.1...@meltwater/grimoire-common@2.1.2-ENGAGE.0) (2024-02-22)

**Note:** Version bump only for package @meltwater/grimoire-common

## [2.1.1](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-common@2.1.0...@meltwater/grimoire-common@2.1.1) (2024-02-15)

### Bug Fixes

- add more product tagging fields ([#38](https://github.com/meltwater/grimoire-commons/issues/38)) ([7457990](https://github.com/meltwater/grimoire-commons/commit/7457990c416155963df83707fe2f443a141b41db))

## [2.1.1-ENGAGE.0](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-common@2.1.0...@meltwater/grimoire-common@2.1.1-ENGAGE.0) (2024-02-15)

**Note:** Version bump only for package @meltwater/grimoire-common

# 2.1.0 (2024-02-08)

### Features

- instagram publish ([#24](https://github.com/meltwater/grimoire-commons/issues/24)) ([e1f2f2a](https://github.com/meltwater/grimoire-commons/commit/e1f2f2a035f33302edca266e22310e9b1c344e75))
- instagram publisher ([#19](https://github.com/meltwater/grimoire-commons/issues/19)) ([3e13113](https://github.com/meltwater/grimoire-commons/commit/3e13113971da30fe1d27d4fe418ac8b76ab24bc5))

## 2.0.1-ENGAGE.1 (2024-02-08)

### Features

- instagram publish ([#24](https://github.com/meltwater/grimoire-commons/issues/24)) ([e1f2f2a](https://github.com/meltwater/grimoire-commons/commit/e1f2f2a035f33302edca266e22310e9b1c344e75))
- instagram publisher ([#19](https://github.com/meltwater/grimoire-commons/issues/19)) ([3e13113](https://github.com/meltwater/grimoire-commons/commit/3e13113971da30fe1d27d4fe418ac8b76ab24bc5))

## 2.0.1-ENGAGE.0 (2024-02-08)

### Features

- instagram publish ([#24](https://github.com/meltwater/grimoire-commons/issues/24)) ([e1f2f2a](https://github.com/meltwater/grimoire-commons/commit/e1f2f2a035f33302edca266e22310e9b1c344e75))
- instagram publisher ([#19](https://github.com/meltwater/grimoire-commons/issues/19)) ([3e13113](https://github.com/meltwater/grimoire-commons/commit/3e13113971da30fe1d27d4fe418ac8b76ab24bc5))

## [1.3.4-ENGAGE.4](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-common@1.3.4-ENGAGE.3...@meltwater/grimoire-common@1.3.4-ENGAGE.4) (2024-02-07)

### Bug Fixes

- add better debug logs ([509943e](https://github.com/meltwater/grimoire-commons/commit/509943e63d617f94fe628a7a441ba07df43ea389))

## [1.3.4-ENGAGE.3](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-common@1.3.4-ENGAGE.2...@meltwater/grimoire-common@1.3.4-ENGAGE.3) (2024-02-05)

### Bug Fixes

- build before pushing tags ([8a52b30](https://github.com/meltwater/grimoire-commons/commit/8a52b304e37f54052245338b61c625deb8a25e37))

## [1.3.4-ENGAGE.2](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-common@1.3.4-ENGAGE.1...@meltwater/grimoire-common@1.3.4-ENGAGE.2) (2024-02-05)

### Bug Fixes

- publish test ([f472532](https://github.com/meltwater/grimoire-commons/commit/f4725323c646a3cb801f35bba804b8618f50e1c1))

## [1.3.4-ENGAGE.1](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-common@1.3.4-ENGAGE.0...@meltwater/grimoire-common@1.3.4-ENGAGE.1) (2024-02-05)

### Bug Fixes

- cleanup dependencies & unused files ([60016c7](https://github.com/meltwater/grimoire-commons/commit/60016c71d23395f7055c738eced181776ea4fd50))

## [1.3.4-ENGAGE.0](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-common@1.3.3...@meltwater/grimoire-common@1.3.4-ENGAGE.0) (2024-02-05)

### Bug Fixes

- clear cache for published packages ([59df6e8](https://github.com/meltwater/grimoire-commons/commit/59df6e812636d1009d61f92293a0d9c41353c6e1))
- dependency ([3984022](https://github.com/meltwater/grimoire-commons/commit/3984022fe1f76f2b081c3ef5869620003a6526fc))

## [1.3.3](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-common@1.3.2...@meltwater/grimoire-common@1.3.3) (2024-02-05)

### Bug Fixes

- remove grimoire-decorators ([14d5f6b](https://github.com/meltwater/grimoire-commons/commit/14d5f6bd88bd667c5655d8120c18663669867c52))

## [1.3.2](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-common@1.3.1...@meltwater/grimoire-common@1.3.2) (2024-02-05)

### Bug Fixes

- add mpkg property ([2d611ad](https://github.com/meltwater/grimoire-commons/commit/2d611adda64da333058834df5eaffe0c3d43ddb5))

## [1.3.1](https://github.com/meltwater/grimoire-commons/compare/@meltwater/grimoire-common@1.3.0...@meltwater/grimoire-common@1.3.1) (2024-02-05)

### Bug Fixes

- private:true is not needed for packages intended to be published ([24b4376](https://github.com/meltwater/grimoire-commons/commit/24b43769179002149ce0fc6be14987b10e1b532c))

# 1.3.0 (2024-02-05)

### Bug Fixes

- add publish-config ([8d0f0ec](https://github.com/meltwater/grimoire-commons/commit/8d0f0ecb79fb4a2bf27031dbae8b90da24f5eacc))
- version numbers ([f14ca69](https://github.com/meltwater/grimoire-commons/commit/f14ca697f45cc999e0111e744107a1bdad762c22))

### Features

- instagram publisher ([#19](https://github.com/meltwater/grimoire-commons/issues/19)) ([3e13113](https://github.com/meltwater/grimoire-commons/commit/3e13113971da30fe1d27d4fe418ac8b76ab24bc5))

# 1.2.0 (2024-02-05)

### Bug Fixes

- version numbers ([f14ca69](https://github.com/meltwater/grimoire-commons/commit/f14ca697f45cc999e0111e744107a1bdad762c22))

### Features

- instagram publisher ([#19](https://github.com/meltwater/grimoire-commons/issues/19)) ([3e13113](https://github.com/meltwater/grimoire-commons/commit/3e13113971da30fe1d27d4fe418ac8b76ab24bc5))

# 1.1.0 (2024-02-01)

### Bug Fixes

- all packages should be private ([72f78ee](https://github.com/meltwater/grimoire-commons/commit/72f78ee3b11aeb9908f945427450463750808384))
- imports ([93eeddd](https://github.com/meltwater/grimoire-commons/commit/93eeddd0af544eaf25393b70bb4ea4c324f34243))
- **instagram:** add x-client-name header to clients ([f502267](https://github.com/meltwater/grimoire-commons/commit/f5022678e75240999d160f6417a9433b02e855bc))
- logger & tracer usage ([#16](https://github.com/meltwater/grimoire-commons/issues/16)) ([ff4aca7](https://github.com/meltwater/grimoire-commons/commit/ff4aca7ce65e9f34744b049c880b99fb911d5bea))

### Features

- **instagram:** publish reels, mixed media & story ([5cb01f5](https://github.com/meltwater/grimoire-commons/commit/5cb01f5547f51c70ada1305d09e6e86c08196cc4))
- **monitoring:** add tracing & monitoring ([8f033a6](https://github.com/meltwater/grimoire-commons/commit/8f033a6347d042b7e24beb6e016e72066114d8e6))
