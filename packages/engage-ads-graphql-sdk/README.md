# Engage Ads GraphQL SDK

The Engage Ads GraphQL SDK is a package specifically designed for the Engage Ads backend. It provides a set of functions to generate GraphQL queries and mutations compatible with the Engage Ads GraphQL Lambda.

## Overview

This SDK simplifies the process of creating GraphQL queries and mutations for both frontend and backend applications interacting with the Engage Ads GraphQL API. It offers a consistent way to structure your GraphQL operations, making it easier to maintain and update your code.

## Installation

```bash
npm install @meltwater/engage-ads-graphql-sdk
```

## Usage

### Frontend Examples

#### Using Axios

```typescript
import axios from "axios";
import { getTikTokInsights } from "@meltwater/engage-ads-graphql-sdk";

const API_URL = "https://api.engage-ads.meltwater.net/graphql";

async function fetchTikTokInsights() {
  const payload = getTikTokInsights(
    {
      filter: {
        startDate: "2023-01-01",
        endDate: "2023-12-31",
        channels: ["tiktok"],
        size: 10,
      },
    },
    {
      postId: true,
      channel: true,
      createTime: true,
      likes: true,
      scheduleEndTime: true,
      metrics: {
        videoViews: true,
        engagementRate: true,
      },
    },
  );

  try {
    const response = await axios.post(API_URL, payload);
    console.log(response.data);
  } catch (error) {
    console.error("Error fetching TikTok insights:", error);
  }
}
```

#### Using Fetch

```typescript
import { createTiktokSparkAd } from "@meltwater/engage-ads-graphql-sdk";

const API_URL = "https://api.engage-ads.meltwater.net/graphql";

async function createTikTokAd() {
  const payload = createTiktokSparkAd(
    {
      params: {
        adName: "My New Ad",
        adgroupName: "My Ad Group",
        objectiveType: "REACH",
        videoId: "video123",
        adText: "Check out our new product!",
        campaignName: "Summer Campaign",
        budget: 1000,
        advertiserId: "adv123",
      },
    },
    {
      id: true,
      status: true,
    },
  );

  try {
    const response = await fetch(API_URL, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(payload),
    });
    const data = await response.json();
    console.log(data);
  } catch (error) {
    console.error("Error creating TikTok ad:", error);
  }
}
```

### Backend Examples

#### Lambda Tester Script

The SDK is already integrated with the lambda-tester script in the engage-ads-backend repository. This script is used to test the GraphQL Lambda handler during development.

```typescript
import { getTikTokInsights } from "@meltwater/engage-ads-graphql-sdk";

// In your lambda-tester.ts file
const sdkResult = getTikTokInsights(
  {
    filter: {
      startDate: "2022-01-01",
      endDate: "2022-10-25",
      size: 25,
      order: "desc",
      aggregationType: "sum",
      startFrom: 0,
      channels: ["tiktok"],
      match: {
        captionKeyword: "test UP with SPLIT OFF",
      },
      sortBy: "likes",
    },
  },
  {
    postId: true,
    channel: true,
    createTime: true,
    caption: true,
    likes: true,
    comments: true,
    metrics: {
      videoViews: true,
      engagementRate: true,
    },
  },
);

// Use sdkResult directly in your Lambda handler test
```

#### Preparing Payloads for Tests

```typescript
import { getTikTokAdAccounts } from "@meltwater/engage-ads-graphql-sdk";

describe("TikTok Ad Accounts Query", () => {
  it("should fetch TikTok ad accounts", async () => {
    const payload = getTikTokAdAccounts(
      {
        input: {
          ApplicationCompanyId: "5d1cc992767990d40422e42e",
          ChannelId: 11,
          ActiveInd: 1,
        },
      },
      {
        credentialId: true,
        targetPageName: true,
        targetPageLogoUrl: true,
        associatedProfiles: {
          credentialId: true,
          targetPageName: true,
          socialAccountId: true,
          targetPageLogoUrl: true,
        },
      },
    );

    // Use the entire payload in your test setup
    const response = await testGraphQLQuery(payload);
    expect(response).toHaveProperty("data.tikTokAdAccounts");
    // ... more assertions
  });
});
```

## Note

This SDK is specific to the Engage Ads backend and provides queries and mutations relevant to the GraphQL Lambda for Engage Ads. It is not intended for use with other GraphQL APIs or services.

## References

- For more details on available queries and mutations, refer to the SDK source code or TypeScript definitions.
- The engage-ads-backend repository already integrates this SDK in its lambda-tester script for testing the GraphQL Lambda handler during development.
- When adding new queries or mutations to the SDK, make sure to update the corresponding tests and documentation.
