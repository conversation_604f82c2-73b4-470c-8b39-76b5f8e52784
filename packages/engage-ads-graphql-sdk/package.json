{"name": "@meltwater/engage-ads-graphql-sdk", "version": "1.42.0", "author": "Team Area51", "description": "SDK for Engage Ads GraphQL queries and mutations", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "clean": "rimraf dist tsconfig.tsbuildinfo", "test": "jest"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/node": "^20.14.2", "jest": "^29.7.0", "node-fetch": "^2.6.1", "ts-jest": "^29.1.4", "typescript": "^5.3.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "moduleFileExtensions": ["ts", "js"], "transform": {"^.+\\.ts$": "ts-jest"}, "testMatch": ["**/__tests__/**/*.spec.ts"]}, "files": ["dist"], "license": "MIT", "publishConfig": {"access": "restricted", "registry": "https://registry.npmjs.org/"}}