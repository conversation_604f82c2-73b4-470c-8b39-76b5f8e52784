export const generateFields = <T extends Record<string, boolean | Record<string, unknown> | undefined>>(
  fields: T,
): string => {
  return Object.entries(fields)
    .map(([key, value]) => {
      if (typeof value === "object" && value !== null) {
        return `${key} { ${generateFields(value as Record<string, boolean | Record<string, unknown>>)} }`;
      }
      return key;
    })
    .join(" ");
};
