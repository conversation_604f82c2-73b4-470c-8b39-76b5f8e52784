import { createTiktokSparkAd } from "../mutations";

describe("Mutation functions", () => {
  describe("createTiktokSparkAd", () => {
    it("should return the correct mutation structure with mandatory fields", () => {
      const result = createTiktokSparkAd({
        params: {
          campaign: {
            objectiveType: "VIDEO_VIEWS",
            campaignName: "test29",
            budget: 50,
            advertiserId: "7392521205472903176",
            budgetMode: "BUDGET_MODE_TOTAL",
          },
          adGroup: {
            advertiserId: "7392521205472903176",
            adgroupName: "test28",
            scheduleStartTime: "2028-01-01 00:00:00",
            scheduleType: "SCHEDULE_START_END",
            billingEvent: "CPV",
            pacing: "PACING_MODE_SMOOTH",
            budget: 20,
            budgetMode: "BUDGET_MODE_TOTAL",
            locationIds: ["6252001"],
            placements: ["PLACEMENT_TIKTOK"],
            scheduleEndTime: "2028-01-02 00:00:00",
            optimizationGoal: "ENGAGED_VIEW",
            bidPrice: 1,
          },
          ad: {
            advertiserId: "7392521205472903176",
            creatives: [
              {
                adName: "test28",
                identityType: "TT_USER",
                identityId: "7392521205472903176",
                adFormat: "SINGLE_VIDEO",
                videoId: "123",
              },
            ],
          },
          companyId: "5660905e4497f67c28e12edc",
          credentialId: 11,
        },
      });
      expect(result.operationName).toBeNull();
      expect(result.variables.params?.ad.creatives[0]).toHaveProperty("adName");
      expect(result.variables.params?.adGroup).toHaveProperty("adgroupName");
      expect(result.variables.params?.campaign).toHaveProperty("objectiveType");
      expect(result.variables.params?.ad.creatives[0]).toHaveProperty("videoId");
      expect(result.variables.params?.ad.creatives[0]).toHaveProperty("adFormat");
      expect(result.variables.params?.campaign).toHaveProperty("campaignName");
      expect(result.variables.params?.campaign).toHaveProperty("budget");
      expect(result.variables.params?.campaign).toHaveProperty("advertiserId");
      expect(result.variables.params).toHaveProperty("companyId");
      expect(result.variables.params).toHaveProperty("credentialId");
      expect(result.query).toContain("engageAdsCreateTiktokSparkAd");
      expect(result.query).toContain("campaignId");
    });

    it("should include specified fields", () => {
      const fields = {
        campaignId: true,
      };
      const result = createTiktokSparkAd(
        {
          params: {
            campaign: {
              objectiveType: "VIDEO_VIEWS",
              campaignName: "test29",
              budget: 50,
              advertiserId: "7392521205472903176",
              budgetMode: "BUDGET_MODE_TOTAL",
            },
            adGroup: {
              advertiserId: "7392521205472903176",
              adgroupName: "test28",
              scheduleStartTime: "2028-01-01 00:00:00",
              scheduleType: "SCHEDULE_START_END",
              billingEvent: "CPV",
              pacing: "PACING_MODE_SMOOTH",
              budget: 20,
              budgetMode: "BUDGET_MODE_TOTAL",
              locationIds: ["6252001"],
              placements: ["PLACEMENT_TIKTOK"],
              scheduleEndTime: "2028-01-02 00:00:00",
              optimizationGoal: "ENGAGED_VIEW",
              bidPrice: 1,
            },
            ad: {
              advertiserId: "7392521205472903176",
              creatives: [
                {
                  adName: "test28",
                  identityType: "TT_USER",
                  identityId: "7392521205472903176",
                  adFormat: "SINGLE_VIDEO",
                  videoId: "123",
                },
              ],
            },
            companyId: "5660905e4497f67c28e12edc",
            credentialId: 11,
          },
        },
        fields,
      );
      expect(result.query).toContain("campaignId");
    });
  });
});
