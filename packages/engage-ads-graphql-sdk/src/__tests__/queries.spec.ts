import { getTikTokInsights, getTikTokAdAccounts, getTikTokRegions } from "../queries";

describe("Query functions", () => {
  describe("getTikTokInsights", () => {
    it("should return the correct query structure with mandatory fields", () => {
      const result = getTikTokInsights({
        filter: {
          startDate: "2022-01-01",
          endDate: "2022-10-25",
          size: 25,
          order: "desc",
          aggregationType: "sum",
          channels: ["tiktok"],
        },
      });
      expect(result.operationName).toBeNull();
      expect(result.variables.filter).toHaveProperty("startDate");
      expect(result.variables.filter).toHaveProperty("endDate");
      expect(result.variables.filter).toHaveProperty("size");
      expect(result.variables.filter).toHaveProperty("order");
      expect(result.variables.filter).toHaveProperty("aggregationType");
      expect(result.variables.filter).toHaveProperty("channels");
      expect(result.query).toContain("engageAdsChannelInsights");
    });

    it("should include specified fields", () => {
      const fields = {
        postId: true,
        channel: true,
        companyId: true,
        profileId: true,
        media: {
          type: true,
          url: true,
          mimeType: true,
        },
        metrics: {
          videoViews: true,
          engagementRate: true,
          impressionSources: {
            percentage: true,
            impressionSource: true,
          },
        },
      };
      const result = getTikTokInsights(
        {
          filter: {
            startDate: "2022-01-01",
            endDate: "2022-10-25",
            size: 25,
            order: "desc",
            aggregationType: "sum",
            channels: ["tiktok"],
          },
        },
        fields,
      );
      expect(result.query).toContain("postId");
      expect(result.query).toContain("channel");
      expect(result.query).toContain("companyId");
      expect(result.query).toContain("profileId");
      expect(result.query).toMatch(/media\s*{\s*type\s*url\s*mimeType\s*}/);
      expect(result.query).toContain(
        "metrics { videoViews engagementRate impressionSources { percentage impressionSource } }",
      );
    });

    it("should include all fields when no fields are specified", () => {
      const result = getTikTokInsights({
        filter: {
          startDate: "2022-01-01",
          endDate: "2022-10-25",
          size: 25,
          order: "desc",
          aggregationType: "sum",
          channels: ["tiktok"],
        },
      });
      const expectedFields = [
        "postId",
        "channel",
        "companyId",
        "profileId",
        "createTime",
        "mimeType",
        "caption",
        "likes",
        "comments",
        "averageWatchTime",
        "videoLength",
        "engagements",
        "engagementRate",
        "reach",
        "videoViews",
        "fullVideoWatchRate",
        "totalTimeWatched",
        "shares",
      ];
      expectedFields.forEach((field) => {
        expect(result.query).toMatch(new RegExp(field));
      });
      expect(result.query).toMatch(/media\s*{\s*type\s*url\s*}/);
      expect(result.query).toMatch(/metrics\s*{/);
      expect(result.query).toMatch(/impressionSources\s*{\s*percentage\s*impressionSource\s*}/);
    });

    it("should include isBoosted field in default fields", () => {
      const result = getTikTokInsights({
        filter: {
          startDate: "2022-01-01",
          endDate: "2022-10-25",
          size: 25,
          channels: ["tiktok"],
        },
      });
      expect(result.query).toContain("isBoosted");
    });

    it("should include scheduleEndTime field when specified", () => {
      const fields = {
        postId: true,
        scheduleEndTime: true,
      };
      const result = getTikTokInsights(
        {
          filter: {
            startDate: "2022-01-01",
            endDate: "2022-10-25",
            size: 25,
            channels: ["tiktok"],
          },
        },
        fields,
      );
      expect(result.query).toContain("scheduleEndTime");
    });
  });

  describe("getTikTokAdAccounts", () => {
    it("should return the correct query structure with mandatory fields", () => {
      const result = getTikTokAdAccounts({
        input: {
          ApplicationCompanyId: "5d1cc992767990d40422e42e",
          ChannelId: 11,
          ActiveInd: 1,
        },
      });
      expect(result.operationName).toBeNull();
      expect(result.variables.input).toHaveProperty("ApplicationCompanyId");
      expect(result.variables.input).toHaveProperty("ChannelId");
      expect(result.variables.input).toHaveProperty("ActiveInd");
      expect(result.query).toContain("tikTokAdAccounts");
    });

    it("should include specified fields including socialAccountId", () => {
      const fields = {
        credentialId: true,
        targetPageName: true,
        socialAccountId: true, // Include the new field
        associatedProfiles: {
          credentialId: true,
          targetPageName: true,
          socialAccountId: true,
          targetPageLogoUrl: true,
        },
      };
      const result = getTikTokAdAccounts(
        {
          input: {
            ApplicationCompanyId: "5d1cc992767990d40422e42e",
            ChannelId: 11,
            ActiveInd: 1,
          },
        },
        fields,
      );
      expect(result.query).toContain("credentialId");
      expect(result.query).toContain("targetPageName");
      expect(result.query).toContain("socialAccountId"); // Check for the new field
      expect(result.query).toContain(
        "associatedProfiles { credentialId targetPageName socialAccountId targetPageLogoUrl }",
      );
    });
  });

  describe("getTikTokRegions", () => {
    it("should return the correct query structure with mandatory fields", () => {
      const result = getTikTokRegions({
        input: {
          companyId: "5d1cc992767990d40422e42e",
          credentialId: 11,
          advertiserId: "123",
          placements: [""],
          objectiveType: "VIDEO_VIEWS",
          level: "TO_COUNTRY",
        },
      });
      expect(result.operationName).toBeNull();
      expect(result.variables.input).toHaveProperty("companyId");
      expect(result.variables.input).toHaveProperty("credentialId");
      expect(result.variables.input).toHaveProperty("placements");
      expect(result.query).toContain("GetTiktokRegions");
    });

    it("should include specified fields", () => {
      const fields = {
        locationId: true,
        name: true,
      };
      const result = getTikTokRegions(
        {
          input: {
            companyId: "5d1cc992767990d40422e42e",
            credentialId: 11,
            advertiserId: "123",
            placements: [""],
            objectiveType: "VIDEO_VIEWS",
            level: "TO_COUNTRY",
          },
        },
        fields,
      );
      expect(result.query).toContain("locationId");
      expect(result.query).toContain("name");
    });
  });
});
