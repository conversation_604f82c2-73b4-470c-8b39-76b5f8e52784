import * as sdk from "../index";
import type { TikTokSparkAdsParams } from "../types/tiktok-spark-ad";

describe("Engage Ads GraphQL SDK", () => {
  it("should export query functions", () => {
    expect(sdk.getTikTokInsights).toBeDefined();
    expect(sdk.getTikTokAdAccounts).toBeDefined();
  });

  it("should export mutation functions", () => {
    expect(sdk.createTiktokSparkAd).toBeDefined();
  });

  it("getTikTokInsights should return the correct query", () => {
    const result = sdk.getTikTokInsights({
      filter: { startDate: "2023-01-01", endDate: "2023-01-31", channels: ["tiktok"] },
    });
    expect(result.operationName).toBeNull();
    expect(result.variables.filter.channels).toEqual(["tiktok"]);
    expect(result.query).toContain("engageAdsChannelInsights");
  });

  it("createTiktokSparkAd should return the correct mutation", () => {
    const result = sdk.createTiktokSparkAd({ params: { campaign: { campaignName: "test" } } as TikTokSparkAdsParams });
    expect(result.operationName).toBeNull();
    expect(result.variables.params?.campaign.campaignName).toBe("test");
    expect(result.query).toContain("engageAdsCreateTiktokSparkAd");
  });

  it("should include scheduleEndTime in fields when specified", () => {
    const result = sdk.getTikTokInsights(
      {
        filter: {
          startDate: "2023-01-01",
          endDate: "2023-01-31",
          channels: ["tiktok"],
        },
      },
      {
        postId: true,
        scheduleEndTime: true,
      },
    );
    expect(result.query).toContain("scheduleEndTime");
  });

  it("should include scheduleEndTime in default fields", () => {
    const result = sdk.getTikTokInsights({
      filter: {
        startDate: "2023-01-01",
        endDate: "2023-01-31",
        channels: ["tiktok"],
      },
    });
    expect(result.query).toContain("scheduleEndTime");
  });
});
