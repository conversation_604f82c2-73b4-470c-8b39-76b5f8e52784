export interface TikTokSparkAdsParams {
  campaign: TiktokCampaign;
  adGroup: TiktokAdGroup;
  ad: TiktokAd;
  companyId: string;
  credentialId: number;
}

export interface TiktokCampaign {
  campaignId?: string;
  objectiveType: string;
  campaignName: string;
  budget: number;
  advertiserId: string;
  budgetMode: string;
}

export interface TiktokAdGroup {
  advertiserId: string;
  adgroupName: string;
  scheduleStartTime: string;
  scheduleType: string;
  billingEvent: string;
  pacing: string;
  budget: number;
  budgetMode: string;
  locationIds: string[];
  placements: string[];
  scheduleEndTime: string;
  optimizationGoal: string;
  bidPrice: number;
  frequency?: number;
  frequencySchedule?: number;
  conversionBidPrice?: number;
  promotionType?: string;
}

export interface TiktokCreative {
  adName: string;
  identityType: string;
  identityId: string;
  adFormat: string;
  videoId: string;
  callToAction?: string;
  landingPageUrl?: string;
  identityAuthorizedBcId?: string;
}

export interface TiktokAd {
  advertiserId: string;
  creatives: TiktokCreative[];
}

export type CreateTikTokSparkAdVariables = {
  params: TikTokSparkAdsParams;
};

export interface CreateTikTokSparkAdFields {
  campaignId?: boolean;
  metadata?: {
    error?: {
      name?: boolean;
      message: boolean;
    };
  };
}

export interface CreateTikTokSparkAdResponse {
  campaignId?: string;
  metadata?: {
    error?: {
      name?: string;
      message: string;
    };
  };
}
