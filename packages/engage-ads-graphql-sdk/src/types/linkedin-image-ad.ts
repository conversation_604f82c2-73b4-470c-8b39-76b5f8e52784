export interface LinkedInImageAdCampaign {
  campaignId: string;
  objectiveType?: string;
  campaignName?: string;
  dailyBudget?: string;
  currencyCode?: string;
  adAccountId: string;
  campaignGroupId?: string;
  startTimestamp?: string;
  endTimestamp?: string;
}

export interface LinkedInCreativeImageAd {
  adName: string;
}

export interface LinkedInImageAdParams {
  companyId: string;
  credentialId: number;
  advertiserId: string;
  postId?: string;
  campaign: LinkedInImageAdCampaign;
  creative: LinkedInCreativeImageAd;
}

export type CreateLinkedInImageAdVariables = {
  params: LinkedInImageAdParams;
};

export type CreateLinkedInImageAdFields = {
  campaignId?: boolean;
  creativeId?: boolean;
  metadata?: {
    error?: {
      name?: boolean;
      message?: boolean;
    };
  };
};
