export interface LinkedInVideoAdCampaign {
  campaignId: string;
  objectiveType?: string;
  campaignName?: string;
  dailyBudget?: string;
  currencyCode?: string;
  adAccountId: string;
  campaignGroupId?: string;
  startTimestamp?: string;
  endTimestamp?: string;
}

export interface LinkedInCreativeVideoAd {
  adName: string;
}

export interface LinkedInVideoAdParams {
  companyId: string;
  credentialId: number;
  advertiserId: string;
  postId?: string;
  campaign: LinkedInVideoAdCampaign;
  creative: LinkedInCreativeVideoAd;
}

export type CreateLinkedInVideoAdVariables = {
  params: LinkedInVideoAdParams;
};

export interface CreateLinkedInVideoAdFields {
  campaignId?: boolean;
  creativeId?: boolean;
  metadata?: {
    error?: {
      name?: boolean;
      message?: boolean;
    };
  };
}

export interface CreateLinkedInVideoAdResponse {
  campaignId?: string;
  creativeId?: string;
  metadata?: {
    error?: {
      name?: string;
      message?: string;
    };
  };
}
