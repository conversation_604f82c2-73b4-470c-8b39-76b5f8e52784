export interface LinkedInDocumentAdCampaign {
  campaignId: string;
  objectiveType?: string;
  campaignName?: string;
  dailyBudget?: string;
  currencyCode?: string;
  adAccountId: string;
  campaignGroupId?: string;
  startTimestamp?: string;
  endTimestamp?: string;
}

export interface LinkedInCreativeDocumentAd {
  adName: string;
}

export interface LinkedInDocumentAdParams {
  companyId: string;
  credentialId: number;
  advertiserId: string;
  postId?: string;
  campaign: LinkedInDocumentAdCampaign;
  creative: LinkedInCreativeDocumentAd;
}

export type CreateLinkedInDocumentAdVariables = {
  params: LinkedInDocumentAdParams;
};

export type CreateLinkedInDocumentAdFields = {
  campaignId?: boolean;
  creativeId?: boolean;
  metadata?: {
    error?: {
      name?: boolean;
      message?: boolean;
    };
  };
};
