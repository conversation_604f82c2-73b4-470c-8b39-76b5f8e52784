export interface GetTiktokRegionParams {
  companyId: string;
  credentialId: number;
  advertiserId: string;
  placements: string[];
  objectiveType: string;
  level: string;
}

export interface GetTiktokRegionResponse {
  data: {
    locationId: string;
    name: string;
  }[];
}

export interface TikTokRegionVariables {
  input: GetTiktokRegionParams;
}

export interface TikTokRegionFields {
  locationId?: boolean;
  name?: boolean;
}
