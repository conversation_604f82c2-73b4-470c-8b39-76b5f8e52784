export interface TikTokInsightsFilter {
  startDate: string;
  endDate: string;
  size?: number;
  order?: string;
  aggregationType?: string;
  startFrom?: number;
  channels?: string[];
  match?: {
    captionKeyword?: string;
  };
  sortBy?: string;
  sortByMetric?: boolean;
  profileIds?: string[];
}

export interface TikTokInsightsVariables {
  filter: TikTokInsightsFilter;
}

export interface TikTokInsightsFields {
  postId?: boolean;
  documentId?: boolean;
  channel?: boolean;
  companyId?: boolean;
  profileId?: boolean;
  createTime?: boolean;
  media?: {
    type?: boolean;
    url?: boolean;
  };
  mimeType?: boolean;
  caption?: boolean;
  likes?: boolean;
  comments?: boolean;
  isBoosted?: boolean;
  scheduleEndTime?: boolean;
  postType?: boolean;
  downloadUrl?: boolean;
  mediaUrn?: boolean;
  metrics?: {
    averageWatchTime?: boolean;
    videoLength?: boolean;
    engagements?: boolean;
    engagementRate?: boolean;
    reach?: boolean;
    videoViews?: boolean;
    fullVideoWatchRate?: boolean;
    totalTimeWatched?: boolean;
    shares?: boolean;
    impressionSources?: {
      percentage?: boolean;
      impressionSource?: boolean;
    };
  };
}
