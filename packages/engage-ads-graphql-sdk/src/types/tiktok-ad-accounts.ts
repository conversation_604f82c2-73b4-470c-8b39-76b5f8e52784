export interface TikTokAdAccountQueryInput {
  ApplicationCompanyId: string;
  ChannelId: number;
  ActiveInd: number;
}

export interface TikTokAdAccountVariables {
  input: TikTokAdAccountQueryInput;
}

export interface TikTokAdAccountFields {
  credentialId?: boolean;
  targetPageName?: boolean;
  targetPageLogoUrl?: boolean;
  socialAccountId?: boolean;
  associatedProfiles?: {
    credentialId?: boolean;
    targetPageName?: boolean;
    socialAccountId?: boolean;
    targetPageLogoUrl?: boolean;
    identityId?: boolean;
    identityType?: boolean;
    identityAuthorizedBcId?: boolean;
  };
}
