export interface LinkedInTextAdCampaign {
  campaignId: string;
  objectiveType?: string;
  campaignName?: string;
  dailyBudget?: string;
  currencyCode?: string;
  adAccountId: string;
  campaignGroupId?: string;
  startTimestamp?: string;
  endTimestamp?: string;
}

export interface LinkedInCreativeTextAd {
  adName: string;
  destinationUrl: string;
  description: string;
  callToAction?: string;
}

export interface LinkedInTextAdParams {
  companyId: string;
  credentialId: number;
  advertiserId: string;
  postId?: string;
  campaign: LinkedInTextAdCampaign;
  creative: LinkedInCreativeTextAd;
}

export type CreateLinkedInTextAdVariables = {
  params: LinkedInTextAdParams;
};

export interface CreateLinkedInTextAdFields {
  campaignId?: boolean;
  creativeId?: boolean;
  metadata?: {
    error?: {
      name?: boolean;
      message?: boolean;
    };
  };
}

export interface CreateLinkedInTextAdResponse {
  campaignId?: string;
  creativeId?: string;
  metadata?: {
    error?: {
      name?: string;
      message?: string;
    };
  };
}
