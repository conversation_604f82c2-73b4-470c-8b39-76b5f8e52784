import type { TikTokInsightsVariables, TikTokInsightsFields } from "./types/tiktok-insights";
import type { TikTokAdAccountVariables, TikTokAdAccountFields } from "./types/tiktok-ad-accounts";
import type { TikTokRegionFields, TikTokRegionVariables } from "./types/tiktok-regions";
import type { TikTokAdvertisersInfoFields, TikTokAdvertisersInfoVariables } from "./types/tiktok-advertisers-info";
import type { CampaignQueryInput, CampaignFields } from "./types/tiktok-campaigns";
import type { FacebookAccountsPagesVariables, FacebookAccountsPagesFields } from "./types/facebook-accounts";
import type { FacebookAdCampaignVariables, FacebookAdCampaignFields } from "./types/facebook-ad-campaigns";
import type { FacebookSavedAudienceVariables, FacebookSavedAudienceFields } from "./types/facebook-saved-audience";
import type { LinkedInCredentialsFilter } from "./types/linkedin-accounts-pages";
import type {
  EngageAdsChannelInsightsVariables,
  EngageAdsChannelInsightsFields,
} from "./types/engage-ads-channel-insights";
import type { LinkedInCampaignQueryInput, LinkedInCampaignFields } from "./types/linkedin-campaigns";
import { generateFields } from "./utils";

export const getFacebookInsights = (
  variables: EngageAdsChannelInsightsVariables,
  fields?: EngageAdsChannelInsightsFields,
) => ({
  operationName: null,
  variables: {
    filter: {
      ...variables.filter,
      channels: ["facebook"],
    },
  },
  query: `query GetFacebookInsights($filter: EngageAdsChannelInsightsFilter!) {
    engageAdsChannelInsights(filter: $filter) {
      insights {
        ${
          fields
            ? generateFields(fields as Record<string, boolean | Record<string, unknown>>)
            : `
          postId
          externalId
          channel
          companyId
          profileId
          createTime
          media {
            type
            url
          }
          mimeType
          caption
          likes
          comments
          isBoosted
          scheduleEndTime
          metrics {
            averageWatchTime
            videoLength
            engagements
            engagementRate
            reach
            videoViews
            fullVideoWatchRate
            totalTimeWatched
            shares
            impressionSources {
              percentage
              impressionSource
            }
          }
        `
        }
      }
      totalCount
    }
  }`,
});

export const getTikTokInsights = (variables: TikTokInsightsVariables, fields?: TikTokInsightsFields) => ({
  operationName: null,
  variables: {
    filter: {
      ...variables.filter,
      channels: ["tiktok"],
    },
  },
  query: `query GetTikTokInsights($filter: EngageAdsChannelInsightsFilter!) {
    engageAdsChannelInsights(filter: $filter) {
      insights {
        ${
          fields
            ? generateFields(fields as Record<string, boolean | Record<string, unknown>>)
            : `
          postId
          documentId
          channel
          companyId
          profileId
          createTime
          media {
            type
            url
          }
          mimeType
          caption
          likes
          comments
          isBoosted
          scheduleEndTime
          metrics {
            averageWatchTime
            videoLength
            engagements
            engagementRate
            reach
            videoViews
            fullVideoWatchRate
            totalTimeWatched
            shares
            impressionSources {
              percentage
              impressionSource
            }
          }
        `
        }
      }
      totalCount
    }
  }`,
});

export const getTikTokAdAccounts = (variables: TikTokAdAccountVariables, fields?: TikTokAdAccountFields) => ({
  operationName: null,
  variables,
  query: `
    query ($input: TikTokAdAccountQueryInput!) {
      tikTokAdAccounts(input: $input) {
        ${
          fields
            ? generateFields(fields as Record<string, boolean | Record<string, unknown>>)
            : `
          credentialId
          targetPageName
          targetPageLogoUrl
          socialAccountId
          associatedProfiles {
            credentialId
            targetPageName
            socialAccountId
            targetPageLogoUrl
          }
        `
        }
      }
    }
  `,
});

export const getTikTokRegions = (variables: TikTokRegionVariables, fields?: TikTokRegionFields) => ({
  operationName: null,
  variables,
  query: `query GetTiktokRegions($input: TikTokRegionQueryInput!) {
    tikTokRegions(input: $input) {
      data {
        ${fields ? generateFields(fields as Record<string, boolean | Record<string, unknown>>) : ""}
      }
    }
  }`,
});

export const getTiktokAdvertisersInfo = (
  variables: TikTokAdvertisersInfoVariables,
  fields?: TikTokAdvertisersInfoFields,
) => ({
  operationName: null,
  variables,
  query: `query GetTiktokAdvertisersInfo($input: TikTokAdvertisersInfoQueryInput!) {
    tikTokAdvertisersInfo(input: $input) {
      data {
        ${fields ? generateFields(fields as Record<string, boolean | Record<string, unknown>>) : ""}
      }
    }
  }`,
});

export const getCampaigns = (variables: { input: CampaignQueryInput }, fields?: CampaignFields) => ({
  operationName: null,
  variables,
  query: `query GetCampaigns($input: CampaignQueryInput!) {
    queryCampaigns(input: $input) {
      ${fields ? generateFields(fields as Record<string, boolean | Record<string, unknown>>) : ""}
    }
  }`,
});

export const getFacebookAccountsPages = (
  variables: FacebookAccountsPagesVariables,
  fields?: FacebookAccountsPagesFields,
) => ({
  operationName: null,
  variables,
  query: `query GetFacebookAccountsPages($input: FacebookCredentialsFilter!) {
    getFacebookAccountsPages(input: $input) {
      ${
        fields
          ? generateFields(fields as Record<string, boolean | Record<string, unknown>>)
          : `channelName
         targetPageName
         socialAccountId
         credentialId
        `
      }
    }
  }`,
});

export const getFacebookAdCampaigns = (variables: FacebookAdCampaignVariables, fields?: FacebookAdCampaignFields) => ({
  operationName: null,
  variables,
  query: `query GetFacebookAdCampaigns($filter: FacebookAdCampaignData) {
    getFacebookAdCampaigns(filter: $filter) {
      ${
        fields
          ? generateFields(fields as Record<string, boolean | Record<string, unknown>>)
          : `id
         name
         status
         bidStrategy
         buyingType
         budget
         budgetType
         objective
        `
      }
    }
  }`,
});

export const getFacebookSavedAudience = (
  variables: FacebookSavedAudienceVariables,
  fields?: FacebookSavedAudienceFields,
) => ({
  operationName: null,
  variables,
  query: `query getFacebookSavedAudience($filter: FacebookSavedAudienceData) {
    getFacebookSavedAudience(filter: $filter) {
      ${
        fields
          ? generateFields(fields as Record<string, boolean>)
          : `id
             name
            `
      }
    }
  }`,
});

export const getFacebookAdAccountMinBudgets = (
  variables: { credentialId: number },
  fields?: {
    currency?: boolean;
    impressionsBudget?: boolean;
    videoBudget?: boolean;
    highFreqBudget?: boolean;
    lowFreqBudget?: boolean;
  },
) => ({
  operationName: null,
  variables,
  query: `query getFacebookAdAccountMinBudgets($credentialId: Int) {
    getFacebookAdAccountMinBudgets(credentialId: $credentialId) {
      ${
        fields
          ? generateFields(fields as Record<string, boolean>)
          : `currency
             impressionsBudget
             videoBudget
             highFreqBudget
             lowFreqBudget
            `
      }
    }
  }`,
});

export const getLinkedInAdAccounts = (
  variables: { input: LinkedInCredentialsFilter },
  fields?: {
    channelName?: boolean;
    targetPageName?: boolean;
    socialAccountId?: boolean;
    credentialId?: boolean;
    targetPageLogoUrl?: boolean;
    tokenId?: boolean;
    tokenDetails?: {
      token?: boolean;
    };
  },
) => ({
  operationName: null,
  variables,
  query: `query GetLinkedInAdAccounts($input: LinkedInCredentialsFilter!) {
    getLinkedInAdAccounts(input: $input) {
      ${
        fields
          ? generateFields(fields as Record<string, boolean | Record<string, unknown>>)
          : `channelName
             targetPageName
             socialAccountId
             credentialId
             targetPageLogoUrl
             tokenId
             tokenDetails {
               token
             }
            `
      }
    }
  }`,
});

export const getLinkedInAssociatedPages = (
  variables: { query: { companyId: string; credentialId: number } },
  fields?: {
    socialAccountId?: boolean;
    targetPageName?: boolean;
    credentialId?: boolean;
    channelName?: boolean;
    targetPageLogoUrl?: boolean;
    tokenId?: boolean;
    tokenDetails?: {
      token?: boolean;
    };
  },
) => ({
  operationName: null,
  variables,
  query: `query GetLinkedInAssociatedPages($query: LinkedInAssociatedPagesQuery!) {
    getLinkedInAssociatedPages(query: $query) {
      ${
        fields
          ? generateFields(fields as Record<string, boolean | Record<string, unknown>>)
          : `socialAccountId
             targetPageName
             credentialId
             channelName
             targetPageLogoUrl
            `
      }
    }
  }`,
});

export const getLinkedInInsights = (
  variables: EngageAdsChannelInsightsVariables,
  fields?: EngageAdsChannelInsightsFields,
) => ({
  operationName: null,
  variables: {
    filter: {
      ...variables.filter,
      channels: ["linkedin"],
    },
  },
  query: `query GetLinkedInInsights($filter: EngageAdsChannelInsightsFilter!) {
    engageAdsChannelInsights(filter: $filter) {
      insights {
        ${
          fields
            ? generateFields(fields as Record<string, boolean | Record<string, unknown>>)
            : `
          postId
          externalId
          channel
          companyId
          profileId
          createTime
          media {
            type
            url
          }
          mimeType
          caption
          likes
          comments
          isBoosted
          scheduleEndTime
          metrics {
            averageWatchTime
            videoLength
            engagements
            engagementRate
            reach
            videoViews
            fullVideoWatchRate
            totalTimeWatched
            shares
            impressionSources {
              percentage
              impressionSource
            }
          }
        `
        }
      }
      totalCount
    }
  }`,
});

export const getLinkedInAdCampaigns = (
  variables: { input: LinkedInCampaignQueryInput },
  fields?: LinkedInCampaignFields,
) => ({
  operationName: null,
  variables,
  query: `query GetLinkedInAdCampaigns($input: LinkedInCampaignQueryInput!) {
    getLinkedInAdCampaigns(input: $input) {
      ${
        fields
          ? generateFields(fields as Record<string, boolean | Record<string, unknown>>)
          : `
        id
        name
        status
        type
        dailyBudget {
          amount
          currencyCode
        }
        unitCost {
          amount
          currencyCode
        }
      `
      }
    }
  }`,
});
