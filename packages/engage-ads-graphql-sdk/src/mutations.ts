import type { CreateTikTokSparkAdVariables, CreateTikTokSparkAdFields } from "./types/tiktok-spark-ad";
import type { FacebookBoostInput, FacebookBoostResponseFields } from "./types/facebook-boost";
import { generateFields } from "./utils";
import type { CreateLinkedInTextAdVariables, CreateLinkedInTextAdFields } from "./types/linkedin-text-ad";
import type { CreateLinkedInImageAdVariables, CreateLinkedInImageAdFields } from "./types/linkedin-image-ad";
import type { CreateLinkedInVideoAdVariables, CreateLinkedInVideoAdFields } from "./types/linkedin-video-ad";
import type { CreateLinkedInDocumentAdVariables, CreateLinkedInDocumentAdFields } from "./types/linkedin-document-ad";

export const createTiktokSparkAd = (variables: CreateTikTokSparkAdVariables, fields?: CreateTikTokSparkAdFields) => ({
  operationName: null,
  variables,
  query: `mutation ($params: EngageAdsCreateTiktokSparkAdParams!) {
    engageAdsCreateTiktokSparkAd(params: $params) {
      ${fields ? generateFields(fields as Record<string, boolean | Record<string, unknown>>) : "campaignId"}
    }
  }`,
});

export const boostFacebookPost = (variables: { input: FacebookBoostInput }, fields?: FacebookBoostResponseFields) => ({
  operationName: null,
  variables,
  query: `mutation BoostFacebookPost($input: FacebookBoostInput!) {
    boostFacebookPost(input: $input) {
      ${
        fields
          ? generateFields(fields as Record<string, boolean>)
          : `boostId
             boostingErrors`
      }
    }
  }`,
});

export const createLinkedInTextAd = (
  variables: CreateLinkedInTextAdVariables,
  fields?: CreateLinkedInTextAdFields,
) => ({
  operationName: null,
  variables,
  query: `mutation ($params: EngageAdsCreateLinkedInTextAdParams!) {
    engageAdsCreateLinkedInTextAd(params: $params) {
      ${
        fields
          ? generateFields(fields as Record<string, boolean | Record<string, unknown>>)
          : `campaignId\n      creativeId`
      }
    }
  }`,
});

export const createLinkedInImageAd = (
  variables: CreateLinkedInImageAdVariables,
  fields?: CreateLinkedInImageAdFields,
) => ({
  operationName: null,
  variables,
  query: `mutation ($params: EngageAdsCreateLinkedInImageAdParams!) {
    engageAdsCreateLinkedInImageAd(params: $params) {
      ${
        fields
          ? generateFields(fields as Record<string, boolean | Record<string, unknown>>)
          : `campaignId\n      creativeId`
      }
    }
  }`,
});

export const createLinkedInVideoAd = (
  variables: CreateLinkedInVideoAdVariables,
  fields?: CreateLinkedInVideoAdFields,
) => ({
  operationName: null,
  variables,
  query: `mutation ($params: EngageAdsCreateLinkedInVideoAdParams!) {
    engageAdsCreateLinkedInVideoAd(params: $params) {
      ${
        fields
          ? generateFields(fields as Record<string, boolean | Record<string, unknown>>)
          : `campaignId\n      creativeId`
      }
    }
  }`,
});

export const createLinkedInDocumentAd = (
  variables: CreateLinkedInDocumentAdVariables,
  fields?: CreateLinkedInDocumentAdFields,
) => ({
  operationName: null,
  variables,
  query: `mutation ($params: EngageAdsCreateLinkedInDocumentAdParams!) {
    engageAdsCreateLinkedInDocumentAd(params: $params) {
      ${
        fields
          ? generateFields(fields as Record<string, boolean | Record<string, unknown>>)
          : `campaignId\n      creativeId`
      }
    }
  }`,
});
