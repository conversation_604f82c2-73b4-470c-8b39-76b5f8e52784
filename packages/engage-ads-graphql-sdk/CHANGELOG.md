# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [1.5.1-ENGAGE.1](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.5.1-ENGAGE.0...@meltwater/engage-ads-graphql-sdk@1.5.1-ENGAGE.1) (2024-11-04)

### Features

- adding support for returning scheduleEndTime for boosted ad ([daa1021](https://github.com/meltwater/engage-ads-backend/commit/daa102117175c8f483ed45c184e61866b69c64f3))

## [1.5.1-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.5.0...@meltwater/engage-ads-graphql-sdk@1.5.1-ENGAGE.0) (2024-11-04)

### Features

- adding support for checking ad boost status ([a56d35d](https://github.com/meltwater/engage-ads-backend/commit/a56d35df66f50539072508278e03b823e2abd82c))

# [1.5.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.4.4...@meltwater/engage-ads-graphql-sdk@1.5.0) (2024-11-03)

**Note:** Version bump only for package @meltwater/engage-ads-graphql-sdk

## [1.4.6-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.4.4...@meltwater/engage-ads-graphql-sdk@1.4.6-ENGAGE.0) (2024-10-29)

**Note:** Version bump only for package @meltwater/engage-ads-graphql-sdk

## [1.4.4](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.4.2...@meltwater/engage-ads-graphql-sdk@1.4.4) (2024-10-16)

**Note:** Version bump only for package @meltwater/engage-ads-graphql-sdk

## [1.4.4-ENGAGE.1](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.4.2...@meltwater/engage-ads-graphql-sdk@1.4.4-ENGAGE.1) (2024-10-15)

### Bug Fixes

- version update ([42f19ea](https://github.com/meltwater/engage-ads-backend/commit/42f19ea813bcd4b2e5870a8df2df946969ad2f90))
- version upgrade ([235bf0f](https://github.com/meltwater/engage-ads-backend/commit/235bf0f440adbf5546340c8ae93592a5a08de2a7))

## [1.4.2](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.4.0...@meltwater/engage-ads-graphql-sdk@1.4.2) (2024-10-14)

**Note:** Version bump only for package @meltwater/engage-ads-graphql-sdk

## [1.4.2-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.4.0...@meltwater/engage-ads-graphql-sdk@1.4.2-ENGAGE.0) (2024-10-14)

**Note:** Version bump only for package @meltwater/engage-ads-graphql-sdk

# [1.4.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.3.0...@meltwater/engage-ads-graphql-sdk@1.4.0) (2024-10-07)

### Features

- adding support for social account ID for root level ([#287](https://github.com/meltwater/engage-ads-backend/issues/287)) ([1436575](https://github.com/meltwater/engage-ads-backend/commit/143657504cdb08af31a4134c1b5cf9633e6bf116))

## [1.3.3-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.3.0...@meltwater/engage-ads-graphql-sdk@1.3.3-ENGAGE.0) (2024-10-04)

### Bug Fixes

- updating version ([77aaed8](https://github.com/meltwater/engage-ads-backend/commit/77aaed8dde522fa02872db080d7c67e55d5cc70f))

### Features

- adding support for social account ID for root level ([5a88704](https://github.com/meltwater/engage-ads-backend/commit/5a887045266c42778d2c273006adc488a7de2073))

# [1.3.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.2.0...@meltwater/engage-ads-graphql-sdk@1.3.0) (2024-10-03)

### Features

- **regions:** ADS-164 Integrate with tiktok regions API ([#282](https://github.com/meltwater/engage-ads-backend/issues/282)) ([49d864b](https://github.com/meltwater/engage-ads-backend/commit/49d864b005cc6475466f0050ff74d751b9628b27))

## [1.2.2-ENGAGE.1](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.2.2-ENGAGE.0...@meltwater/engage-ads-graphql-sdk@1.2.2-ENGAGE.1) (2024-10-03)

**Note:** Version bump only for package @meltwater/engage-ads-graphql-sdk

## [1.2.2-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.2.0...@meltwater/engage-ads-graphql-sdk@1.2.2-ENGAGE.0) (2024-10-03)

**Note:** Version bump only for package @meltwater/engage-ads-graphql-sdk

# [1.2.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.1.2...@meltwater/engage-ads-graphql-sdk@1.2.0) (2024-09-30)

### Features

- adding support for generating token and passing the same to rel… ([#281](https://github.com/meltwater/engage-ads-backend/issues/281)) ([8e60836](https://github.com/meltwater/engage-ads-backend/commit/8e60836ef0358facc58a4cdd0b31b45b272f7600))

## [1.1.3-ENGAGE.1](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.1.3-ENGAGE.0...@meltwater/engage-ads-graphql-sdk@1.1.3-ENGAGE.1) (2024-09-12)

**Note:** Version bump only for package @meltwater/engage-ads-graphql-sdk

## [1.1.3-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.1.2...@meltwater/engage-ads-graphql-sdk@1.1.3-ENGAGE.0) (2024-09-10)

### Features

- adding support for generating token and passing the same to relevant repository and mutation ([6839470](https://github.com/meltwater/engage-ads-backend/commit/68394701a5c83d348f17709fdc63687c910ec5dc))

## [1.1.2](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.1.1...@meltwater/engage-ads-graphql-sdk@1.1.2) (2024-09-01)

**Note:** Version bump only for package @meltwater/engage-ads-graphql-sdk

## [1.1.1](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.1.0...@meltwater/engage-ads-graphql-sdk@1.1.1) (2024-08-29)

**Note:** Version bump only for package @meltwater/engage-ads-graphql-sdk

## [1.1.1-ENGAGE.2](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.1.1-ENGAGE.1...@meltwater/engage-ads-graphql-sdk@1.1.1-ENGAGE.2) (2024-08-29)

### Bug Fixes

- linting concerns ([8386d19](https://github.com/meltwater/engage-ads-backend/commit/8386d19c9e8fc6e003bae233dd2811aff7669506))

## [1.1.1-ENGAGE.1](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.1.1-ENGAGE.0...@meltwater/engage-ads-graphql-sdk@1.1.1-ENGAGE.1) (2024-08-29)

### Features

- Add support for all fields in getTikTokInsights method ([7d04845](https://github.com/meltwater/engage-ads-backend/commit/7d04845eb04fabfe0a15ffb8f3fbd09393d865bc))
- update test to be more flexible in checking query fields ([ffeb46c](https://github.com/meltwater/engage-ads-backend/commit/ffeb46cf9877920ab9c11b728cdb5a7d414bfe7d))

## [1.1.1-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.1.0...@meltwater/engage-ads-graphql-sdk@1.1.1-ENGAGE.0) (2024-08-29)

### Features

- Add support for multiple profileIds in engage-ads-graphql-sdk ([8ff33b7](https://github.com/meltwater/engage-ads-backend/commit/8ff33b7e260e4875e250dad6e63925473686ea6a))

# [1.1.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.0.3...@meltwater/engage-ads-graphql-sdk@1.1.0) (2024-08-28)

### Features

- adding support for filtering posts by profile ID ([#274](https://github.com/meltwater/engage-ads-backend/issues/274)) ([09ed22b](https://github.com/meltwater/engage-ads-backend/commit/09ed22bc039b85cf7c15d03b3401d153123804b0))

## [1.0.6-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.0.3...@meltwater/engage-ads-graphql-sdk@1.0.6-ENGAGE.0) (2024-08-23)

### Bug Fixes

- updating version ([36eea97](https://github.com/meltwater/engage-ads-backend/commit/36eea97d28326ba0a53e93bdbba79f45a5d27eba))

## [1.0.3](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.0.2...@meltwater/engage-ads-graphql-sdk@1.0.3) (2024-08-22)

**Note:** Version bump only for package @meltwater/engage-ads-graphql-sdk

## [1.0.3-ENGAGE.3](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.0.3-ENGAGE.2...@meltwater/engage-ads-graphql-sdk@1.0.3-ENGAGE.3) (2024-08-21)

**Note:** Version bump only for package @meltwater/engage-ads-graphql-sdk

## [1.0.3-ENGAGE.2](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.0.3-ENGAGE.1...@meltwater/engage-ads-graphql-sdk@1.0.3-ENGAGE.2) (2024-08-21)

**Note:** Version bump only for package @meltwater/engage-ads-graphql-sdk

## [1.0.3-ENGAGE.1](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.0.3-ENGAGE.0...@meltwater/engage-ads-graphql-sdk@1.0.3-ENGAGE.1) (2024-08-21)

### Bug Fixes

- Ads-87 Applies reviews fixes and formatting ([#272](https://github.com/meltwater/engage-ads-backend/issues/272)) ([eb1f812](https://github.com/meltwater/engage-ads-backend/commit/eb1f812142e3976a8c4d119f1afe4d329fe37c49))

## [1.0.3-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.0.2...@meltwater/engage-ads-graphql-sdk@1.0.3-ENGAGE.0) (2024-08-20)

**Note:** Version bump only for package @meltwater/engage-ads-graphql-sdk

## [1.0.2](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.0.1...@meltwater/engage-ads-graphql-sdk@1.0.2) (2024-08-16)

**Note:** Version bump only for package @meltwater/engage-ads-graphql-sdk

## [1.0.2-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.0.1...@meltwater/engage-ads-graphql-sdk@1.0.2-ENGAGE.0) (2024-08-16)

### Bug Fixes

- addressing lintting concerns for readme ([96042a9](https://github.com/meltwater/engage-ads-backend/commit/96042a9006138040a22b1ae4824f782fb8159067))
- updating examples for package ([7c4fab0](https://github.com/meltwater/engage-ads-backend/commit/7c4fab0ea82429607d71675da8e214272e4721d2))

## 1.0.1 (2024-08-16)

**Note:** Version bump only for package @meltwater/engage-ads-graphql-sdk

## [1.0.1-ENGAGE.4](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.0.1-ENGAGE.3...@meltwater/engage-ads-graphql-sdk@1.0.1-ENGAGE.4) (2024-08-16)

### Features

- Update README.md to pass complete payload to body when creating event ([764f591](https://github.com/meltwater/engage-ads-backend/commit/764f5911783d94bba01c2af3de454008bbf2f587))

## [1.0.1-ENGAGE.3](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.0.1-ENGAGE.2...@meltwater/engage-ads-graphql-sdk@1.0.1-ENGAGE.3) (2024-08-16)

### Bug Fixes

- addressing linting concerns for docs ([147a229](https://github.com/meltwater/engage-ads-backend/commit/147a229f1530d425d52c3a3b179332f8fe08596a))
- update README.md to reflect correct SDK usage ([c28f3e0](https://github.com/meltwater/engage-ads-backend/commit/c28f3e067e59192d2d2432550e595f169abf2672))

### Features

- Add engage-ads-graphql-sdk package README ([f0d2db1](https://github.com/meltwater/engage-ads-backend/commit/f0d2db1442457f4f59aea9ddc32b56182efc85e5))
- integrating sdk ([b0e819e](https://github.com/meltwater/engage-ads-backend/commit/b0e819e53410c1da9d6a5895f113f6ff86101fa8))

## [1.0.1-ENGAGE.2](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.0.1-ENGAGE.1...@meltwater/engage-ads-graphql-sdk@1.0.1-ENGAGE.2) (2024-08-13)

### Bug Fixes

- refactoring code ([1627b0d](https://github.com/meltwater/engage-ads-backend/commit/1627b0d25a4479d4b720dfc2e455f337c147a2af))
- removing operationName for getTikTokAdAccounts ([fc816c2](https://github.com/meltwater/engage-ads-backend/commit/fc816c2e74ef08cf7673c480f9221cb401ecbdd1))
- Update module import path and remove API key ([df3ae32](https://github.com/meltwater/engage-ads-backend/commit/df3ae32ee8599f91759ec46ee00e6d0fb7b3d7b9))

### Features

- Add SDK usage example ([a0a654c](https://github.com/meltwater/engage-ads-backend/commit/a0a654cec87be8d7cee5ccbe11a72b2f6598fd0d))
- add simple test script using node-fetch ([53b7c16](https://github.com/meltwater/engage-ads-backend/commit/53b7c16fc2b45b328067676f501ca6f095f62507))
- Update tests for queries and mutations ([c6fd6aa](https://github.com/meltwater/engage-ads-backend/commit/c6fd6aa74a90af12459b57ab2f66854aa7486e18))

## [1.0.1-ENGAGE.1](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-graphql-sdk@1.0.1-ENGAGE.0...@meltwater/engage-ads-graphql-sdk@1.0.1-ENGAGE.1) (2024-08-13)

### Bug Fixes

- addressing linting concerns ([be9b0f6](https://github.com/meltwater/engage-ads-backend/commit/be9b0f6c9c445207e6f274d4d827704ec51306e8))

## 1.0.1-ENGAGE.0 (2024-08-13)

### Bug Fixes

- Add missing import statement to index.spec.ts ([a9b1227](https://github.com/meltwater/engage-ads-backend/commit/a9b1227eee2e86149bd7423d2d85a3e8c1441f30))
- addressing linting issues ([f3c990c](https://github.com/meltwater/engage-ads-backend/commit/f3c990c89b69874210493b887f1b3c78f15b5510))
- addressing linting issues ([6e1c9fe](https://github.com/meltwater/engage-ads-backend/commit/6e1c9fe40f76cab74f38cd1897399cc156679115))
- Adjust generateFields function to remove extra newlines ([cf53ffe](https://github.com/meltwater/engage-ads-backend/commit/cf53ffe834217e56d79303fcf94cf2adf1f3f1f8))
- Remove index.spec.ts file ([830a110](https://github.com/meltwater/engage-ads-backend/commit/830a1104517ed7a4f66f5f68fca2bece500b4f70))
- Update generateFields function to accept more flexible types ([8822a70](https://github.com/meltwater/engage-ads-backend/commit/8822a70971220958541fa9b9674c9b5d25ecbeb2))
- Update GraphQL SDK with type imports and formatting improvements ([b7cd666](https://github.com/meltwater/engage-ads-backend/commit/b7cd6666e8aefd8c8368c8c74e046612e12c96b2))
- Update tests to handle required parameters ([36a0ae9](https://github.com/meltwater/engage-ads-backend/commit/36a0ae9a3916fee87570423f950a82b70e3c35a9))

### Features

- Add dynamic variables and fields to GraphQL SDK ([64b90cd](https://github.com/meltwater/engage-ads-backend/commit/64b90cd4106c1be8f3e12e01313b1eb0f81d7105))
- Add support for specifying fields in GraphQL queries and mutations ([46a6906](https://github.com/meltwater/engage-ads-backend/commit/46a6906d03254c039972b0f4fec582ffc392475d))
- Add tests for GraphQL mutations and queries ([caaa507](https://github.com/meltwater/engage-ads-backend/commit/caaa507db70931158e3c62f27c3c6f64fbd2cf08))
- adding jest configuration for package ([bddda1a](https://github.com/meltwater/engage-ads-backend/commit/bddda1aee17d9140633a7a08c681be5e026e0c5c))
- adding types for queries and mutations ([3f656e4](https://github.com/meltwater/engage-ads-backend/commit/3f656e418b34bdfbc17788ff860d289050db08bd))
- Create engage-ads-graphql-sdk package ([178b36e](https://github.com/meltwater/engage-ads-backend/commit/178b36eddda41f7927d1e8acf2337632ec080445))
- Enhance GraphQL SDK for TikTok Spark Ads and Insights ([3a0e79b](https://github.com/meltwater/engage-ads-backend/commit/3a0e79bd187e610f1b3bc09c353e813d56316877))
- Introduce generic type for generateFields utility ([ef1804c](https://github.com/meltwater/engage-ads-backend/commit/ef1804cb58a10ef66ac205d75363fb0e69e4939d))
