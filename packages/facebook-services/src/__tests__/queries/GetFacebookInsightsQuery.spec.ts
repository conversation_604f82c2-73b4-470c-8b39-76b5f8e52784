import type { HttpClient } from "@meltwater/engage-ads-commons";
import { FacebookInsightsRepository } from "../../facebook-insights-repository";
import { GetFacebookInsightsQuery } from "../../queries/GetFacebookInsightsQuery";

const mockRepository = {
  getFacebookInsights: jest.fn(),
};

jest.mock("../../facebook-insights-repository", () => ({
  FacebookInsightsRepository: jest.fn().mockImplementation(() => mockRepository),
}));

describe("GetFacebookInsightsQuery", () => {
  let query: GetFacebookInsightsQuery;

  const mockResponse = {
    insights: [
      {
        id: "1884421084987816",
        page_id: "test_page",
        created_time: "2023-01-01T00:00:00.000Z",
        message: "Test Facebook post",
        permalink_url: "https://facebook.com/post/1884421084987816",
        reactions_count: 100,
        comments_count: 50,
        shares_count: 25,
        engagement_rate: 2.5,
        post_video_views: 1000,
        page_fans_engagement_rate: 1.8,
        post_impressions: 5000,
        post_impressions_organic: 4000,
        post_impressions_paid: 1000,
      },
    ],
    totalCount: 1,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    query = new GetFacebookInsightsQuery(new FacebookInsightsRepository({} as HttpClient));
    mockRepository.getFacebookInsights.mockResolvedValue(mockResponse);
  });

  it("should execute query and return insights", async () => {
    const params = {
      start_date: "2023-01-01",
      end_date: "2023-01-31",
      size: 10,
      order: "desc",
      start_from: 0,
      sort_by: "created_time",
      page_id: "test_page",
    };

    const result = await query.execute(params);

    expect(mockRepository.getFacebookInsights).toHaveBeenCalledWith(params);
    expect(result).toEqual(mockResponse);
  });
});
