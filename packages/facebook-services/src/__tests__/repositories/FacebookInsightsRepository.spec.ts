import { HttpClient } from "@meltwater/engage-ads-commons";
import { WarpzoneModel } from "@meltwater/engage-ads-db-sdk";
import { FacebookInsightsRepository } from "../../facebook-insights-repository";
import type { GetFacebookInsightsQueryParams, WildcardsSource, FacebookInsights } from "../../types/facebook-insights"; // Added FacebookInsights
import * as dbSdkMockModule from "@meltwater/engage-ads-db-sdk";

// --- Mocks ---

const mockHttpClient = {
  get: jest.fn(),
};

// Mock the HttpClient constructor and its 'get' method
jest.mock("@meltwater/engage-ads-commons", () => ({
  HttpClient: jest.fn().mockImplementation(() => mockHttpClient),
}));

// Mock the DB SDK
jest.mock("@meltwater/engage-ads-db-sdk", () => {
  // Define mocks inside the factory function
  const mockLimit = jest.fn();
  const mockSkip = jest.fn().mockReturnValue({ limit: mockLimit });
  const mockSort = jest.fn().mockReturnValue({ skip: mockSkip });
  const mockFind = jest.fn().mockReturnValue({ sort: mockSort });
  const mockCountDocuments = jest.fn();

  // Return the mocked module structure
  return {
    connectToDatabase: jest.fn().mockResolvedValue(true),
    WarpzoneModel: {
      find: mockFind,
      countDocuments: mockCountDocuments,
      // Expose mocks for manipulation in tests if needed (optional)
      __mocks: {
        mockFind,
        mockSort,
        mockSkip,
        mockLimit,
        mockCountDocuments,
      },
    },
  };
});

// Mock Monitoring
jest.mock("@meltwater/lambda-monitoring", () => ({
  LambdaTracer: {
    getInstance: jest.fn().mockReturnValue({
      // Use jest.fn<T, Y> to type the mock implementation if needed, or keep simple
      captureMethod: jest.fn((_options?: unknown) => (fn: (...args: unknown[]) => unknown) => fn), // Removed any
    }),
  },
  LambdaLogger: {
    getInstance: jest.fn().mockReturnValue({
      info: jest.fn(),
      error: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
    }),
  },
}));

type MockDbPost = {
  _id: string;
  channel: string;
  companyId: string;
  document: {
    id: string;
    metaData: {
      source: { id: string };
      url: string;
    };
    body: {
      publishDate: { date: number };
      content: { text: string };
    };
    externalId: string;
    attachments: Array<{ link?: string; mimeType?: string }>;
  };
};

const mockDbPost1: MockDbPost = {
  _id: "db1",
  channel: "facebook",
  companyId: "db-company-id-1",
  document: {
    id: "doc1",
    metaData: {
      source: { id: "page1" },
      url: "https://facebook.com/post/page1_post1",
    },
    body: {
      publishDate: { date: new Date("2023-01-15T12:00:00Z").getTime() },
      content: { text: "DB Message 1" }, // Revert: Remove keyword
    },
    externalId: "id:facebook.com:page1_post1", // Consistent format
    attachments: [{ link: "db_thumb1.jpg", mimeType: "image/jpeg" }],
  },
};

const mockDbPost2: MockDbPost = {
  _id: "db2",
  channel: "facebook",
  companyId: "db-company-id-2",
  document: {
    id: "doc2",
    metaData: {
      source: { id: "page1" },
      url: "https://facebook.com/post/page1_post2",
    },
    body: {
      publishDate: { date: new Date("2023-01-10T12:00:00Z").getTime() },
      content: { text: "DB Message 2" },
    },
    externalId: "id:facebook.com:page1_post2", // Consistent format
    attachments: [],
  },
};

const mockDbPost3: MockDbPost = {
  _id: "db3",
  channel: "facebook",
  companyId: "db-company-id-3",
  document: {
    id: "doc3",
    metaData: {
      source: { id: "page2" },
      url: "https://facebook.com/post/page2_post3",
    },
    body: {
      publishDate: { date: new Date("2023-01-20T12:00:00Z").getTime() },
      content: { text: "DB Message 3 - Only in DB" }, // Revert: Remove keyword
    },
    externalId: "id:facebook.com:page2_post3",
    attachments: [{ link: "db_thumb3.jpg", mimeType: "image/gif" }],
  },
};

const mockApiMetrics1: WildcardsSource = {
  id: "post1",
  page_id: "page1",
  permalink_url: "https://facebook.com/post/page1_post1",
  created_time: "2023-01-15T12:00:00+0000",
  message: "API Message 1",
  reactions_count: 100,
  comments_count: 10,
  shares_count: 5,
  engagement_rate: 1.1,
  post_reactions_like_total: 80,
  attachments: { data: [{ media: { image: { src: "api_thumb1.jpg" }, mime_type: "image/png" } }] },
  post_video_views: 0,
  page_fans_engagement_rate: 0,
  post_impressions: 0,
  post_impressions_organic: 0,
  post_impressions_paid: 0,
  post_impressions_unique: 0,
  post_impressions_organic_unique: 0,
  post_impressions_paid_unique: 0,
  post_reactions_love_total: 0,
  post_reactions_wow_total: 0,
  post_reactions_haha_total: 0,
  post_reactions_sorry_total: 0,
  post_reactions_anger_total: 0,
  post_video_views_organic: 0,
  post_video_views_paid: 0,
  post_video_view_time: 0,
  post_video_avg_time_watched: 0,
  post_clicks: 0,
  post_clicks_unique: 0,
  post_clicks_link: 0,
  post_clicks_other: 0,
  post_clicks_photo: 0,
  post_clicks_video: 0,
  post_engaged_users: 0,
};

const mockApiMetrics2: WildcardsSource = {
  id: "post2",
  page_id: "page1",
  permalink_url: "https://facebook.com/post/page1_post2",
  created_time: "2023-01-10T12:00:00+0000",
  message: "API Message 2",
  reactions_count: 50,
  comments_count: 2,
  shares_count: 1,
  engagement_rate: 0.5,
  post_reactions_like_total: 40,
  attachments: { data: [] },
  post_video_views: 0,
  page_fans_engagement_rate: 0,
  post_impressions: 0,
  post_impressions_organic: 0,
  post_impressions_paid: 0,
  post_impressions_unique: 0,
  post_impressions_organic_unique: 0,
  post_impressions_paid_unique: 0,
  post_reactions_love_total: 0,
  post_reactions_wow_total: 0,
  post_reactions_haha_total: 0,
  post_reactions_sorry_total: 0,
  post_reactions_anger_total: 0,
  post_video_views_organic: 0,
  post_video_views_paid: 0,
  post_video_view_time: 0,
  post_video_avg_time_watched: 0,
  post_clicks: 0,
  post_clicks_unique: 0,
  post_clicks_link: 0,
  post_clicks_other: 0,
  post_clicks_photo: 0,
  post_clicks_video: 0,
  post_engaged_users: 0,
};

// Define a type for the mocks object within WarpzoneModel mock
type WarpzoneModelMocks = {
  mockFind: jest.Mock;
  mockSort: jest.Mock;
  mockSkip: jest.Mock;
  mockLimit: jest.Mock;
  mockCountDocuments: jest.Mock;
};

type MockedWarpzoneModel = typeof WarpzoneModel & {
  __mocks: WarpzoneModelMocks;
};

describe("FacebookInsightsRepository", () => {
  let repository: FacebookInsightsRepository;
  let mockDbSdkMocks: WarpzoneModelMocks; // Use the specific type here

  beforeAll(() => {
    mockDbSdkMocks = (dbSdkMockModule.WarpzoneModel as MockedWarpzoneModel).__mocks;
  });

  beforeEach(() => {
    jest.clearAllMocks();
    repository = new FacebookInsightsRepository(new HttpClient({ baseURL: "http://test-api" }));
  });

  describe("getFacebookInsights (DB First)", () => {
    it("should fetch posts from DB, get metrics via API, and return combined insights", async () => {
      mockDbSdkMocks.mockCountDocuments.mockResolvedValue(1);
      mockDbSdkMocks.mockLimit.mockResolvedValue([mockDbPost1]);
      mockHttpClient.get.mockResolvedValue({ data: [{ _source: mockApiMetrics1 }] });

      const params: GetFacebookInsightsQueryParams = {
        start_date: "2023-01-01",
        end_date: "2023-01-31",
        size: 10,
        order: "desc",
        start_from: 0,
      };
      const result = await repository.getFacebookInsights(params);
      expect(WarpzoneModel.countDocuments).toHaveBeenCalledTimes(1);
      expect(WarpzoneModel.find).toHaveBeenCalledTimes(1);
      expect(mockDbSdkMocks.mockSort).toHaveBeenCalledWith({ "document.body.publishDate.date": -1 });
      expect(mockDbSdkMocks.mockSkip).toHaveBeenCalledWith(0);
      expect(mockDbSdkMocks.mockLimit).toHaveBeenCalledWith(10);

      expect(mockHttpClient.get.mock.calls.length).toBeGreaterThanOrEqual(1);
      expect(mockHttpClient.get).toHaveBeenCalledWith(
        "/facebook/v1/post/top-docs",
        expect.objectContaining({
          params: expect.objectContaining({ match: JSON.stringify({ id: "page1_post1" }) }),
          paramsSerializer: expect.any(Function),
        }),
      );
      expect(result.totalCount).toBe(1);
      expect(result.insights).toHaveLength(1);
      expect(result.insights[0]).toMatchObject({ id: "page1_post1", reactions_count: 100 });
    });
  });

  // --- Tests for getFacebookInsightsSorted (API First for Metrics) ---
  describe("getFacebookInsightsSorted (API First for Metrics)", () => {
    const defaultParams: GetFacebookInsightsQueryParams = {
      start_date: "2023-01-01",
      end_date: "2023-01-31",
      size: 10,
      order: "desc",
      start_from: 0,
      sort_by: "engagements",
      sortByMetric: true,
      profileIds: ["page1"],
    };
    const expectedDbQuery = {
      channel: "facebook",
      "document.body.publishDate.date": {
        $gte: new Date(defaultParams.start_date).getTime(),
        $lte: new Date(defaultParams.end_date).getTime(),
      },
      "document.metaData.source.id": { $in: defaultParams.profileIds },
    };

    beforeEach(() => {
      // Default setup: Count includes potential filters from the start
      mockDbSdkMocks.mockCountDocuments.mockResolvedValue(3);
      mockHttpClient.get.mockResolvedValue({ data: [{ _source: mockApiMetrics1 }, { _source: mockApiMetrics2 }] });
      mockDbSdkMocks.mockFind.mockResolvedValue([mockDbPost1, mockDbPost2, mockDbPost3]);
    });

    it("should call API, fetch all DB posts, combine, and paginate", async () => {
      const params: GetFacebookInsightsQueryParams = { ...defaultParams, size: 1 };

      const result = await repository.getFacebookInsightsSorted(params);

      // Expect countDocuments to be called only ONCE now, with the final filter
      expect(WarpzoneModel.countDocuments).toHaveBeenCalledTimes(1);
      expect(WarpzoneModel.countDocuments).toHaveBeenCalledWith(expectedDbQuery);

      // Check API call
      expect(mockHttpClient.get.mock.calls.length).toBeGreaterThanOrEqual(1);
      expect(mockHttpClient.get).toHaveBeenCalledWith(
        "/facebook/v1/post/top-docs",
        expect.objectContaining({
          params: expect.objectContaining({ size: 1, "sort-by": "reactions_count" }),
          paramsSerializer: expect.any(Function),
        }),
      );

      expect(WarpzoneModel.find).toHaveBeenCalledTimes(1);
      expect(WarpzoneModel.find).toHaveBeenCalledWith(expectedDbQuery);

      expect(result.totalCount).toBe(3);
      expect(result.insights).toHaveLength(1);
      expect(result.insights[0]).toMatchObject<Partial<FacebookInsights>>({
        id: "page1_post1",
        message: "API Message 1",
        reactions_count: 100,
        company_id: "db-company-id-1",
      });
    });

    it("should include DB-only posts with zero metrics and respect pagination", async () => {
      const params: GetFacebookInsightsQueryParams = { ...defaultParams, size: 2 };

      const result = await repository.getFacebookInsightsSorted(params);

      // Expect countDocuments to be called only ONCE
      expect(WarpzoneModel.countDocuments).toHaveBeenCalledTimes(1);
      expect(WarpzoneModel.countDocuments).toHaveBeenCalledWith(expectedDbQuery); // Check it was called with the correct args
      expect(mockHttpClient.get.mock.calls.length).toBeGreaterThanOrEqual(1);
      expect(WarpzoneModel.find).toHaveBeenCalledTimes(1);
      expect(WarpzoneModel.find).toHaveBeenCalledWith(expectedDbQuery);

      expect(result.totalCount).toBe(3);
      expect(result.insights).toHaveLength(2);

      // Ensure both expected IDs are present regardless of order
      const returnedIds = result.insights.map((i) => i.id);
      expect(returnedIds).toContain("page1_post1");
    });

    it("should include DB-only posts when size allows", async () => {
      const params: GetFacebookInsightsQueryParams = { ...defaultParams, size: 3 };

      const result = await repository.getFacebookInsightsSorted(params);

      // Expect countDocuments to be called only ONCE
      expect(WarpzoneModel.countDocuments).toHaveBeenCalledTimes(1);
      expect(WarpzoneModel.countDocuments).toHaveBeenCalledWith(expectedDbQuery); // Check it was called with the correct args
      expect(mockHttpClient.get.mock.calls.length).toBeGreaterThanOrEqual(1);
      expect(WarpzoneModel.find).toHaveBeenCalledTimes(1);
      expect(WarpzoneModel.find).toHaveBeenCalledWith(expectedDbQuery);

      expect(result.totalCount).toBe(3);
      expect(result.insights).toHaveLength(3);

      const returnedIds2 = result.insights.map((i) => i.id);
      expect(returnedIds2).toEqual(expect.arrayContaining(["page1_post1", "page2_post3"]));
    });

    it("should apply keyword filter to DB query and count", async () => {
      const keyword = "keyword";
      const params: GetFacebookInsightsQueryParams = { ...defaultParams, match: { captionKeyword: keyword }, size: 1 };
      // Expectation with regex filter (matches current implementation)
      const expectedDbCountQueryWithKeyword = {
        ...expectedDbQuery,
        "document.body.content.text": { $regex: keyword, $options: "i" },
      };
      // The find operation also uses the same regex filter
      const expectedDbFindQueryWithKeyword = expectedDbCountQueryWithKeyword;

      // Setup mocks for this specific test
      // Ensure mockFind returns posts that *contain* the keyword in their message
      const mockDbPost1WithKeyword = {
        ...mockDbPost1,
        document: {
          ...mockDbPost1.document,
          body: { ...mockDbPost1.document.body, content: { text: "DB Message 1 with keyword" } },
        },
      };
      const mockDbPost3WithKeyword = {
        ...mockDbPost3,
        document: {
          ...mockDbPost3.document,
          body: { ...mockDbPost3.document.body, content: { text: "DB Message 3 - Only in DB keyword" } },
        },
      };
      mockDbSdkMocks.mockFind.mockResolvedValueOnce([mockDbPost1WithKeyword, mockDbPost3WithKeyword]);

      mockDbSdkMocks.mockCountDocuments.mockReset();
      // Mock the initial DB count (using regex filter) to return 2
      mockDbSdkMocks.mockCountDocuments.mockResolvedValueOnce(2);

      // Mock API to return one post (which may or may not have the keyword, doesn't matter for this test's count logic)
      const mockApiMetrics1WithMessage = { ...mockApiMetrics1, message: "API Message 1 with keyword" }; // Ensure API message also matches for simplicity
      mockHttpClient.get.mockResolvedValueOnce({ data: [{ _source: mockApiMetrics1WithMessage }] });

      const result = await repository.getFacebookInsightsSorted(params);

      // Expect countDocuments (initial DB count) to be called once with regex filter
      expect(WarpzoneModel.countDocuments).toHaveBeenCalledTimes(1);
      expect(WarpzoneModel.countDocuments).toHaveBeenCalledWith(expectedDbCountQueryWithKeyword); // Use the corrected expectation

      expect(mockHttpClient.get.mock.calls.length).toBeGreaterThanOrEqual(1);

      expect(WarpzoneModel.find).toHaveBeenCalledTimes(1);
      // Use the specific find query expectation
      expect(WarpzoneModel.find).toHaveBeenCalledWith(expectedDbFindQueryWithKeyword);

      // Total count should be 2 because both mock posts contain the keyword after in-memory filter
      expect(result.totalCount).toBe(2);
      // Insights length depends on pagination (size=1), should be 1
      expect(result.insights).toHaveLength(1);

      // The first insight should be the one from the API/DB match
      expect(result.insights[0]).toMatchObject<Partial<FacebookInsights>>({
        id: "page1_post1", // Derived from mockDbPost1WithKeyword's externalId
        message: "API Message 1 with keyword", // Comes from mockApiMetrics1WithMessage
        reactions_count: 100,
      });
    });

    it("should return DB posts with zero metrics if API call fails", async () => {
      mockHttpClient.get.mockRejectedValueOnce(new Error("API Call Failed"));
      const params: GetFacebookInsightsQueryParams = { ...defaultParams, size: 2 };

      const result = await repository.getFacebookInsightsSorted(params);

      // Expect countDocuments to be called only ONCE
      expect(WarpzoneModel.countDocuments).toHaveBeenCalledTimes(1);
      expect(WarpzoneModel.countDocuments).toHaveBeenCalledWith(expectedDbQuery); // Check it was called with the correct args
      expect(mockHttpClient.get.mock.calls.length).toBeGreaterThanOrEqual(1);
      expect(WarpzoneModel.find).toHaveBeenCalledTimes(1);
      expect(WarpzoneModel.find).toHaveBeenCalledWith(expectedDbQuery);

      expect(result.totalCount).toBe(3);
      expect(result.insights).toHaveLength(2);

      const returnedIds3 = result.insights.map((i) => i.id);
      expect(returnedIds3).toContain("page1_post1");
    });

    it("should return DB posts with zero metrics if API returns no posts", async () => {
      mockHttpClient.get.mockResolvedValueOnce({ data: [] }); // API returns empty
      const params: GetFacebookInsightsQueryParams = { ...defaultParams, size: 3 };

      const result = await repository.getFacebookInsightsSorted(params);

      // Expect countDocuments to be called only ONCE
      expect(WarpzoneModel.countDocuments).toHaveBeenCalledTimes(1);
      expect(WarpzoneModel.countDocuments).toHaveBeenCalledWith(expectedDbQuery); // Check it was called with the correct args
      expect(mockHttpClient.get.mock.calls.length).toBeGreaterThanOrEqual(1);
      expect(WarpzoneModel.find).toHaveBeenCalledTimes(1);
      expect(WarpzoneModel.find).toHaveBeenCalledWith(expectedDbQuery);

      expect(result.totalCount).toBe(3);
      expect(result.insights).toHaveLength(3);

      const returnedIds4 = result.insights.map((i) => i.id);
      expect(returnedIds4).toEqual(expect.arrayContaining(["page1_post1", "page2_post3"]));
    });

    it("should handle API posts with missing IDs and still return DB posts", async () => {
      const apiPostWithoutId: Partial<WildcardsSource> = { ...mockApiMetrics1, id: undefined };
      mockHttpClient.get.mockResolvedValueOnce({ data: [{ _source: apiPostWithoutId }, { _source: mockApiMetrics2 }] });
      const params: GetFacebookInsightsQueryParams = { ...defaultParams, size: 3 };

      const result = await repository.getFacebookInsightsSorted(params);

      // Expect countDocuments to be called only ONCE
      expect(WarpzoneModel.countDocuments).toHaveBeenCalledTimes(1);
      expect(WarpzoneModel.countDocuments).toHaveBeenCalledWith(expectedDbQuery); // Check it was called with the correct args
      expect(mockHttpClient.get.mock.calls.length).toBeGreaterThanOrEqual(1);
      expect(WarpzoneModel.find).toHaveBeenCalledTimes(1);
      expect(WarpzoneModel.find).toHaveBeenCalledWith(expectedDbQuery);

      expect(result.totalCount).toBe(3);
      expect(result.insights).toHaveLength(3);

      const returnedIds5 = result.insights.map((i) => i.id);
      expect(returnedIds5).toEqual(expect.arrayContaining(["page1_post1", "page2_post3"]));
    });

    it("should return empty list and initial count if sorting params missing", async () => {
      const params: GetFacebookInsightsQueryParams = { ...defaultParams, sort_by: undefined, sortByMetric: undefined };
      mockDbSdkMocks.mockCountDocuments.mockReset(); // Use mockDbSdkMocks
      mockDbSdkMocks.mockCountDocuments.mockResolvedValueOnce(5); // Use mockDbSdkMocks

      const result = await repository.getFacebookInsightsSorted(params);

      // Still called once, but now it's the *filtered* count call
      expect(WarpzoneModel.countDocuments).toHaveBeenCalledTimes(1);
      // The single call should contain the base filters but NOT the text filter key
      expect(WarpzoneModel.countDocuments).toHaveBeenCalledWith(
        expect.objectContaining({
          channel: "facebook", // Ensure base fields are checked
          "document.metaData.source.id": { $in: params.profileIds },
          "document.body.publishDate.date": {
            $gte: new Date(params.start_date).getTime(),
            $lte: new Date(params.end_date).getTime(),
          },
        }),
      );
      // Explicitly check that the text filter key is NOT present
      expect(WarpzoneModel.countDocuments).toHaveBeenCalledWith(
        expect.not.objectContaining({
          "document.body.content.text": expect.anything(),
        }),
      );

      expect(mockHttpClient.get).not.toHaveBeenCalled();
      expect(WarpzoneModel.find).not.toHaveBeenCalled();

      expect(result.insights).toEqual([]);
      expect(result.totalCount).toBe(5);
    });
  });

  describe("getFacebookTotalCount", () => {
    it("should return count from DB", async () => {
      mockDbSdkMocks.mockCountDocuments.mockResolvedValue(5); // Use mockDbSdkMocks

      const count = await repository.getFacebookTotalCount("2023-01-01", "2023-01-31");

      expect(WarpzoneModel.countDocuments).toHaveBeenCalledTimes(1); // Use WarpzoneModel directly
      expect(count).toBe(5);
    });
  });
});
