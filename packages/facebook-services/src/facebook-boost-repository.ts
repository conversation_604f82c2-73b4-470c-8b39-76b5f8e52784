import { <PERSON><PERSON><PERSON>race<PERSON>, LambdaLogger } from "@meltwater/lambda-monitoring";
import { HttpClient } from "@meltwater/engage-ads-commons";
import type {
  GetGroupedMessageParams,
  GroupedMessageResult,
  BoostFacebookPostParams,
  BoostFacebookPostResult,
  IFacebookBoostRepository,
} from "./types/facebook-boost";
import type { GraphQLGroupedMessageResponse, GraphQLBoostResponse } from "./types/facebook-boost-repository";

const logger = LambdaLogger.getInstance();
const tracer = LambdaTracer.getInstance();

export class FacebookBoostRepository implements IFacebookBoostRepository {
  private readonly httpClient: HttpClient;
  private readonly socialPublisherApiUrl: string;

  constructor(accessToken: string) {
    if (!accessToken) {
      throw new Error("Access token is required for FacebookBoostRepository");
    }

    this.socialPublisherApiUrl = process.env.SOCIAL_PUBLISHER_API_URL!;
    this.httpClient = new HttpClient({
      baseURL: this.socialPublisherApiUrl,
      defaultHeaders: {
        Authorization: accessToken,
        "Content-Type": "application/json",
      },
    });

    logger.info("FacebookBoostRepository initialized", {
      apiUrl: this.socialPublisherApiUrl,
    });
  }

  @tracer.captureMethod({ subSegmentName: "FacebookBoostRepository:getGroupedMessage" })
  async getGroupedMessage(params: GetGroupedMessageParams): Promise<GroupedMessageResult | null> {
    const { nativeId } = params;
    logger.info("Repository: Attempting getGroupedMessage call to SPAPI", { nativeId });

    const query = `
      query GetGroupedMessage($nativeId: String!) {
        groupedMessage(nativeId: $nativeId) {
          channels {
            __typename
            ... on FacebookChannel {
              channel
              profiles {
                legacyMessageId
                credentialId
                nativeId
              }
            }
          }
        }
      }
    `;

    const variables = { nativeId };

    try {
      const response = await this.httpClient.graphql<GraphQLGroupedMessageResponse, { nativeId: string }>(
        "",
        query,
        variables,
      );

      logger.info("Repository: getGroupedMessage response received", {
        hasData: !!response?.groupedMessage,
      });

      const groupedMessage = response?.groupedMessage;
      if (!groupedMessage) {
        logger.warn("Repository: No grouped message found", { nativeId });
        return null;
      }

      return {
        groupedMessage: {
          channels: groupedMessage.channels || [],
        },
      };
    } catch (error) {
      logger.error("Repository: Error in getGroupedMessage", {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        nativeId,
      });
      throw error;
    }
  }

  @tracer.captureMethod({ subSegmentName: "FacebookBoostRepository:boostFacebookPost" })
  async boostFacebookPost(params: BoostFacebookPostParams): Promise<BoostFacebookPostResult> {
    logger.info("Repository: Attempting boostFacebookPost call to SPAPI", {
      messageId: params.messageId,
      adAccountCredentialId: params.adAccountCredentialId,
      campaignId: params.campaignId,
    });

    const mutation = `
      mutation BoostSPAPIFacebookPost($data: FacebookBoostData!) {
        boostFacebookPost(data: $data) {
          boostId
          boostingErrors
        }
      }
    `;

    const variables = {
      data: {
        messageId: params.messageId,
        campaignId: params.campaignId,
        duration: params.duration,
        budget: params.budget,
        audienceId: params.audienceId,
        credentialId: params.adAccountCredentialId,
      },
    };

    try {
      const response = await this.httpClient.graphql<GraphQLBoostResponse, typeof variables>("", mutation, variables);

      logger.info("Repository: boostFacebookPost response received", {
        hasData: !!response?.boostFacebookPost,
      });

      const boostResult = response?.boostFacebookPost;
      if (!boostResult) {
        throw new Error("No boost result received from SPAPI");
      }

      return {
        boostId: boostResult.boostId,
        boostingErrors: boostResult.boostingErrors || [],
      };
    } catch (error) {
      logger.error("Repository: Error in boostFacebookPost", {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        messageId: params.messageId,
        adAccountCredentialId: params.adAccountCredentialId,
      });
      throw error;
    }
  }
}
