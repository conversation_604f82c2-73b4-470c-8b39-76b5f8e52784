import { <PERSON><PERSON><PERSON><PERSON><PERSON>, LambdaTracer } from "@meltwater/lambda-monitoring";
import type { HttpClient } from "@meltwater/engage-ads-commons";
import type {
  ExtendedFacebookAdAccountsPagesQueryInput,
  S31FilteredCredsRequestBody,
  S31FilteredCredsResponse,
} from "./types/facebook-ad-accounts-pages";

const logger = LambdaLogger.getInstance();
const tracer = LambdaTracer.getInstance();

export class FacebookAdAccountsPagesRepository {
  constructor(private readonly httpClient: HttpClient) {}

  @tracer.captureMethod({ subSegmentName: "FacebookAdAccountsPagesRepository:getFacebookAdAccountsPages" })
  public async getFacebookAdAccountsPages(input: ExtendedFacebookAdAccountsPagesQueryInput): Promise<
    Array<{
      channelName: string;
      targetPageName: string;
      socialAccountId: string;
      credentialId?: number;
      targetPageLogoUrl?: string;
    }>
  > {
    logger.info("Fetching FB accounts/pages from S31", { input });

    const query = `
      query CompanyCredentialsFilteredQuery($query: FilteredCompanyCredentials) {
        companyCredentialsFilteredQuery(query: $query) {
          channelName
          targetPageName
          socialAccountId
          credentialId
          targetPageLogoUrl
        }
      }
    `;

    const variables = {
      query: {
        applicationCompanyId: input.applicationCompanyId,
        userId: input.userId,
        activeInd: input.activeInd,
        statusInd: input.statusInd,
        channels: input.channels,
      },
    };

    const requestBody: S31FilteredCredsRequestBody = {
      query,
      variables,
    };

    const response = await this.httpClient.post<S31FilteredCredsRequestBody, S31FilteredCredsResponse>(
      "/graphql",
      requestBody,
      {
        headers: { Authorization: input.accessToken },
      },
    );

    if (!response.data?.companyCredentialsFilteredQuery) {
      logger.error("Invalid response structure for FB credentials", { response });
      return [];
    }

    return response.data.companyCredentialsFilteredQuery;
  }
}
