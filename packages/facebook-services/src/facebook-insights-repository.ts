import type { HttpClient } from "@meltwater/engage-ads-commons";
import { WarpzoneModel, connectToDatabase } from "@meltwater/engage-ads-db-sdk";
import { LambdaLogger, LambdaTracer } from "@meltwater/lambda-monitoring";
import type {
  GetFacebookInsightsQueryParams,
  FacebookInsights,
  WildcardsResponse,
  FacebookInsightsQuery,
  WildcardsSource,
} from "./types/facebook-insights";

const logger = LambdaLogger.getInstance();
const tracer = LambdaTracer.getInstance();

function mapFacebookSortMetric(metric?: string): string | undefined {
  if (!metric) return undefined;
  switch (metric.toLowerCase()) {
    case "likes":
      return "post_reactions_like_total";
    case "engagement_rate":
      return "engagement_rate";
    case "shares":
      return "shares_count";
    case "comments":
      return "comments_count";
    case "engagements":
      return "reactions_count";
    case "video_views":
      return "post_video_views";
    default:
      logger.warn("Unsupported Facebook sort metric, falling back", { "internal.engage.ads.sort_metric": metric });
      return metric;
  }
}

export class FacebookInsightsRepository {
  constructor(private readonly httpClient: HttpClient) {}

  @tracer.captureMethod({ subSegmentName: "FacebookInsightsRepository:getFacebookInsights (DB First)" })
  async getFacebookInsights({
    start_date,
    end_date,
    size = 10,
    order = "desc",
    start_from = 0,
    profileIds,
    match,
  }: GetFacebookInsightsQueryParams): Promise<{ insights: FacebookInsights[]; totalCount: number }> {
    try {
      await connectToDatabase();

      const query: FacebookInsightsQuery = {
        "document.body.publishDate.date": {
          $gte: new Date(start_date).getTime(),
          $lte: new Date(end_date).getTime(),
        },
        channel: "facebook",
      };

      if (profileIds && profileIds.length > 0) {
        query["document.metaData.source.id"] = { $in: profileIds };
      }

      if (match && match.captionKeyword) {
        query["document.body.content.text"] = { $regex: match.captionKeyword, $options: "i" };
      }

      logger.info("Querying database for Facebook insights (DB First)", {
        "internal.engage.ads": {
          query: JSON.stringify(query),
          date_range: { start_date, end_date },
          pagination: { size, start_from },
          profile_ids: profileIds,
        },
      });

      const totalCount = await WarpzoneModel.countDocuments(query);
      const posts = await WarpzoneModel.find(query)
        .sort({ "document.body.publishDate.date": order === "desc" ? -1 : 1 })
        .skip(start_from)
        .limit(size);

      logger.info("Found posts from database (DB First)", {
        "internal.engage.ads": {
          count: posts.length,
          total_count: totalCount,
          profile_ids: profileIds,
          date_range: { start_date, end_date },
          sort_order: order,
        },
      });

      const insights: FacebookInsights[] = [];
      const postIdMap = new Map();

      for (const post of posts) {
        const externalId = post.document?.externalId || "";
        const postId = externalId.split(":").pop() || "";
        const pageId = post.document?.metaData?.source?.id || "";
        const companyId = post.companyId;

        const logContextBase = {
          "meltwater.company.id": companyId,
          "meltwater.document.external_id": externalId,
          "internal.engage.ads.post_id": postId,
          "internal.engage.ads.page_id": pageId,
        };

        if (!postId || !pageId) {
          logger.warn("Post ID or Page ID missing in DB record", {
            ...logContextBase,
            "internal.engage.ads.external_id": externalId,
          });
          continue;
        }

        if (postIdMap.has(postId)) continue;
        postIdMap.set(postId, true);

        const scheduleEndTime: string | null = null; // Boost status handled centrally in BoostAds.checkBoostStatus

        let metricsSource: Partial<WildcardsSource> = {};

        try {
          const apiQueryParams: Record<string, string | number | undefined> = {
            "start-date": start_date,
            "end-date": end_date,
            size: 1,
            match: JSON.stringify({ id: postId }),
          };

          logger.info("Fetching metrics via API for single post (DB First - using match and facebook-profiles)", {
            ...logContextBase,
            "internal.engage.ads.match": apiQueryParams.match,
            "internal.engage.ads.api_query_params": JSON.stringify(apiQueryParams),
            "internal.engage.ads.endpoint": "/facebook/v1/post/top-docs",
          });

          const singlePostParamsSerializer = (params: Record<string, string | number | undefined>) => {
            const searchParams = new URLSearchParams();
            Object.entries(params).forEach(([key, value]) => {
              if (value !== undefined) {
                searchParams.append(key, String(value));
              }
            });
            searchParams.append("facebook-profiles", pageId);
            if (params.match) {
              searchParams.append("match", String(params.match));
            }
            return searchParams.toString();
          };

          const apiResponse: WildcardsResponse = await this.httpClient.get("/facebook/v1/post/top-docs", {
            params: apiQueryParams,
            headers: {
              "Accept-Language": "en-US,en;q=0.9",
              "Content-Type": "application/json",
            },
            paramsSerializer: (params: Record<string, string | number | undefined>): string =>
              singlePostParamsSerializer(params),
          });
          const relevantSource = apiResponse?.data?.[0]?._source;

          if (relevantSource) {
            metricsSource = relevantSource;
            // No warning needed if metrics are found
          } else {
            // ECS Format: Using nested structure and meltwater fields
            logger.warn("No metrics found in API response for post (DB First - matched by postId)", {
              ...logContextBase, // Spread base context
            });
          }
        } catch (apiError) {
          logger.error("Error fetching metrics via API for post (DB First)", {
            ...logContextBase,
            error: { message: apiError instanceof Error ? apiError.message : String(apiError) },
          });
        }

        const attachments = post.document?.attachments || [];
        const firstAttachment = attachments.length > 0 ? attachments[0] : null;
        const thumbnailUrl = metricsSource.attachments?.data?.[0]?.media?.image?.src || firstAttachment?.link || "";

        insights.push({
          id: postId,
          document_id: post.document?.documentId,
          externalId: externalId,
          page_id: pageId,
          created_time: new Date(post.document?.body?.publishDate?.date || 0).toISOString(),
          message: post.document?.body?.content?.text || "",
          permalink_url: post.document?.metaData?.url || "",
          thumbnail_url: thumbnailUrl,
          mimeType: firstAttachment?.mimeType,
          company_id: companyId || "", // Use captured companyId
          reactions_count: metricsSource.reactions_count || 0,
          comments_count: metricsSource.comments_count || 0,
          shares_count: metricsSource.shares_count || 0,
          engagement_rate: metricsSource.engagement_rate || 0,
          post_video_views: metricsSource.post_video_views || 0,
          page_fans_engagement_rate: metricsSource.page_fans_engagement_rate || 0,
          post_impressions: metricsSource.post_impressions || 0,
          post_impressions_organic: metricsSource.post_impressions_organic || 0,
          post_impressions_paid: metricsSource.post_impressions_paid || 0,
          post_impressions_unique: metricsSource.post_impressions_unique || 0,
          post_impressions_organic_unique: metricsSource.post_impressions_organic_unique || 0,
          post_impressions_paid_unique: metricsSource.post_impressions_paid_unique || 0,
          post_reactions_like_total: metricsSource.post_reactions_like_total || 0,
          post_reactions_love_total: metricsSource.post_reactions_love_total || 0,
          post_reactions_wow_total: metricsSource.post_reactions_wow_total || 0,
          post_reactions_haha_total: metricsSource.post_reactions_haha_total || 0,
          post_reactions_sorry_total: metricsSource.post_reactions_sorry_total || 0,
          post_reactions_anger_total: metricsSource.post_reactions_anger_total || 0,
          post_video_views_organic: metricsSource.post_video_views_organic || 0,
          post_video_views_paid: metricsSource.post_video_views_paid || 0,
          post_video_view_time: metricsSource.post_video_view_time || 0,
          post_video_avg_time_watched: metricsSource.post_video_avg_time_watched || 0,
          post_clicks: metricsSource.post_clicks || 0,
          post_clicks_unique: metricsSource.post_clicks_unique || 0,
          post_clicks_link: metricsSource.post_clicks_link || 0,
          post_clicks_other: metricsSource.post_clicks_other || 0,
          post_clicks_photo: metricsSource.post_clicks_photo || 0,
          post_clicks_video: metricsSource.post_clicks_video || 0,
          post_engaged_users: metricsSource.post_engaged_users || 0,
          schedule_end_time: scheduleEndTime,
        });
      }
      return { insights, totalCount };
    } catch (error) {
      logger.error("Error fetching Facebook insights (DB First)", {
        error: {
          message: error instanceof Error ? error.message : String(error),
          stack_trace: error instanceof Error ? error.stack : undefined,
        },
      });
      throw error;
    }
  }

  @tracer.captureMethod({
    subSegmentName: "FacebookInsightsRepository:getFacebookInsightsSorted (API First for Metrics)",
  })
  async getFacebookInsightsSorted({
    start_date,
    end_date,
    size = 10,
    order = "desc",
    start_from = 0,
    profileIds,
    match,
    sort_by,
    sortByMetric,
  }: GetFacebookInsightsQueryParams): Promise<{ insights: FacebookInsights[]; totalCount: number }> {
    try {
      await connectToDatabase();

      const filteredTotalCount = await this.getFacebookTotalCount(
        start_date,
        end_date,
        match?.captionKeyword,
        profileIds,
      );
      logger.info("Filtered DB count (including keyword filter if present)", {
        "internal.engage.ads.filtered_total_count": filteredTotalCount, // This count uses $text search now
      });

      // Refined Early Exit: Only exit if NO keyword is specified AND the initial DB count is 0.
      // If a keyword IS specified, we must proceed as the API might return posts
      // that match the keyword in the later in-memory filter, even if the DB $text search found 0.
      if (!match?.captionKeyword && filteredTotalCount === 0) {
        logger.info("Exiting early: No keyword specified and initial DB count is 0.");
        return { insights: [], totalCount: 0 };
      }
      // If a keyword *is* present, we continue even if filteredTotalCount (DB $text count) is 0.

      if (sortByMetric && sort_by) {
        const apiSortByField = mapFacebookSortMetric(sort_by);

        const apiQueryParams: Record<string, string | number | undefined> = {
          "start-date": start_date,
          "end-date": end_date,
          size: size,
          "start-from": start_from,
          order: order,
          "aggregation-type": "sum",
          "sort-by": apiSortByField || "created_time",
        };

        const finalApiParams = { ...apiQueryParams };

        let wcPosts: WildcardsResponse["data"] = [];
        try {
          const paramsSerializer = (params: Record<string, string | number | undefined>) => {
            const searchParams = new URLSearchParams();
            Object.entries(params).forEach(([key, value]) => {
              if (value !== undefined) {
                searchParams.append(key, String(value));
              }
            });
            if (Array.isArray(profileIds) && profileIds.length > 0) {
              profileIds.forEach((profileId) => {
                searchParams.append("facebook-profiles", profileId);
              });
            }
            return searchParams.toString();
          };

          logger.info("Fetching sorted page from API (API First for Metrics)", {
            "internal.engage.ads": {
              api_params: finalApiParams,
              profile_ids: profileIds,
              endpoint: "/facebook/v1/post/top-docs",
            },
          });

          const response: WildcardsResponse = await this.httpClient.get("/facebook/v1/post/top-docs", {
            params: finalApiParams,
            headers: { "Accept-Language": "en-US,en;q=0.9", "Content-Type": "application/json" },
            paramsSerializer: (params: Record<string, string | number | undefined>): string => paramsSerializer(params),
          });
          wcPosts = response.data || [];
          logger.info("Sorted page received from API (API First for Metrics)", {
            "internal.engage.ads.count": wcPosts.length,
          });
          logger.info("DEBUG: Raw API Response Order Check", {
            "internal.engage.ads.debug": {
              api_posts: wcPosts.map((p) => ({
                id: p._source?.id,
                permalink: p._source?.permalink_url,
                engagements: p._source?.reactions_count,
              })),
            },
          });
        } catch (apiError) {
          logger.error("Error fetching sorted page from API (API First for Metrics)", {
            "internal.engage.ads": {
              api_params: finalApiParams,
              profile_ids: profileIds,
              endpoint: "/facebook/v1/post/top-docs",
            },
            error: { message: apiError instanceof Error ? apiError.message : String(apiError) },
          });
        }

        const dbQuery: FacebookInsightsQuery = {
          "document.body.publishDate.date": {
            $gte: new Date(start_date).getTime(),
            $lte: new Date(end_date).getTime(),
          },
          channel: "facebook",
        };
        if (profileIds && profileIds.length > 0) {
          dbQuery["document.metaData.source.id"] = { $in: profileIds };
        }
        if (match?.captionKeyword) {
          dbQuery["document.body.content.text"] = { $regex: match.captionKeyword, $options: "i" };
          logger.info("Applying caption keyword REGEX filter to DB query", {
            "internal.engage.ads.caption_keyword": match.captionKeyword,
          });
        }

        logger.info("Querying database for all relevant Facebook posts (Sorted - DB Fetch)", {
          "internal.engage.ads": {
            query: JSON.stringify(dbQuery),
            date_range: { start_date, end_date },
          },
        });
        const allDbPosts = await WarpzoneModel.find(dbQuery);
        logger.info("Found all relevant posts from database (Sorted - DB Fetch)", {
          "internal.engage.ads.count": allDbPosts.length,
        });
        const allDbPostMap = new Map(allDbPosts.map((post) => [post.document?.metaData?.url, post]));
        const allDbPostIdMap = new Map(
          allDbPosts.map((post) => {
            const externalId = post.document?.externalId || "";
            const postId = externalId.split(":").pop() || "";
            return [postId, post];
          }),
        );

        const combinedInsights: FacebookInsights[] = [];
        const processedPostIds = new Set<string>();

        for (const wcPost of wcPosts) {
          const metricsSource = wcPost._source;
          const apiPermalink = metricsSource?.permalink_url;
          const apiPostId = metricsSource?.id || "";
          const apiPageId = metricsSource?.page_id || "";

          let dbPost = apiPermalink ? allDbPostMap.get(apiPermalink) : undefined;
          if (!dbPost && apiPostId && apiPageId) {
            const potentialDbPost = allDbPostIdMap.get(apiPostId);
            if (potentialDbPost?.document?.metaData?.source?.id === apiPageId) {
              dbPost = potentialDbPost;
              logger.warn("Matched API post to DB post using Post ID as fallback", {
                "internal.engage.ads": {
                  api_post_id: apiPostId,
                  api_page_id: apiPageId,
                },
              });
            }
          }

          if (!apiPostId) {
            logger.warn("Skipping API post due to missing post ID", { "internal.engage.ads.api_post_data": wcPost });
            continue;
          }

          if (!dbPost) {
            logger.warn("API post does not have a matching DB record; skipping", {
              "internal.engage.ads": {
                api_post_id: apiPostId,
                api_page_id: apiPageId,
                api_permalink: apiPermalink,
              },
            });
            continue; // Skip posts that aren't present in DB
          }

          // Determine the final postId and externalId based on whether dbPost was found
          let postId: string;
          let externalId: string;
          if (dbPost) {
            // Use DB data as primary source for IDs
            externalId = dbPost.document?.externalId || "";
            // Derive postId from DB externalId, fallback to apiPostId only if DB externalId is malformed/missing
            postId = externalId.split(":").pop() || apiPostId;
          } else {
            // Construct externalId from API data, use apiPostId directly for the main ID
            externalId = `id:facebook.com:${apiPageId}_${apiPostId}`;
            postId = apiPostId; // Use the ID directly from the API response
          }

          const companyId = dbPost?.companyId; // Get companyId if dbPost exists

          const logContextBase = {
            "meltwater.company.id": companyId,
            "meltwater.document.external_id": externalId, // Use the determined externalId
            "internal.engage.ads.post_id": postId, // Use the determined postId
            "internal.engage.ads.page_id": apiPageId || dbPost?.document?.metaData?.source?.id || "",
          };

          // Use the determined postId for the processed set check
          processedPostIds.add(postId);

          // Use the determined pageId from logContextBase
          const pageId = logContextBase["internal.engage.ads.page_id"];

          const apiAttachments = metricsSource?.attachments?.data;
          const dbAttachments = dbPost?.document?.attachments;
          const attachments = apiAttachments && apiAttachments.length > 0 ? apiAttachments : dbAttachments || [];
          const firstAttachment = attachments.length > 0 ? attachments[0] : null;

          let thumbnailUrl = "";
          if (metricsSource?.attachments?.data?.[0]?.media?.image?.src) {
            thumbnailUrl = metricsSource.attachments.data[0].media.image.src;
          } else if (firstAttachment && "link" in firstAttachment && firstAttachment.link) {
            thumbnailUrl = firstAttachment.link;
          }

          let mimeType: string | undefined = undefined;
          if (metricsSource?.attachments?.data?.[0]?.media?.mime_type) {
            mimeType = metricsSource.attachments.data[0].media.mime_type;
          } else if (firstAttachment && "mimeType" in firstAttachment) {
            mimeType = firstAttachment.mimeType;
          }

          const scheduleEndTime: string | null = null; // Boost status handled centrally

          combinedInsights.push({
            id: postId, // Use the explicitly determined postId
            document_id: dbPost?.document?.documentId,
            externalId: externalId, // Use the explicitly determined externalId
            page_id: pageId,
            created_time:
              metricsSource?.created_time || new Date(dbPost?.document?.body?.publishDate?.date || 0).toISOString(),
            message: metricsSource?.message || dbPost?.document?.body?.content?.text || "",
            permalink_url: apiPermalink || dbPost?.document?.metaData?.url || "",
            thumbnail_url: thumbnailUrl,
            mimeType: mimeType,
            company_id: companyId || "",
            reactions_count: metricsSource?.reactions_count || 0,
            comments_count: metricsSource?.comments_count || 0,
            shares_count: metricsSource?.shares_count || 0,
            engagement_rate: metricsSource?.engagement_rate || 0,
            post_video_views: metricsSource?.post_video_views || 0,
            page_fans_engagement_rate: metricsSource?.page_fans_engagement_rate || 0,
            post_impressions: metricsSource?.post_impressions || 0,
            post_impressions_organic: metricsSource?.post_impressions_organic || 0,
            post_impressions_paid: metricsSource?.post_impressions_paid || 0,
            post_impressions_unique: metricsSource?.post_impressions_unique || 0,
            post_impressions_organic_unique: metricsSource?.post_impressions_organic_unique || 0,
            post_impressions_paid_unique: metricsSource?.post_impressions_paid_unique || 0,
            post_reactions_like_total: metricsSource?.post_reactions_like_total || 0,
            post_reactions_love_total: metricsSource?.post_reactions_love_total || 0,
            post_reactions_wow_total: metricsSource?.post_reactions_wow_total || 0,
            post_reactions_haha_total: metricsSource?.post_reactions_haha_total || 0,
            post_reactions_sorry_total: metricsSource?.post_reactions_sorry_total || 0,
            post_reactions_anger_total: metricsSource?.post_reactions_anger_total || 0,
            post_video_views_organic: metricsSource?.post_video_views_organic || 0,
            post_video_views_paid: metricsSource?.post_video_views_paid || 0,
            post_video_view_time: metricsSource?.post_video_view_time || 0,
            post_video_avg_time_watched: metricsSource?.post_video_avg_time_watched || 0,
            post_clicks: metricsSource?.post_clicks || 0,
            post_clicks_unique: metricsSource?.post_clicks_unique || 0,
            post_clicks_link: metricsSource?.post_clicks_link || 0,
            post_clicks_other: metricsSource?.post_clicks_other || 0,
            post_clicks_photo: metricsSource?.post_clicks_photo || 0,
            post_clicks_video: metricsSource?.post_clicks_video || 0,
            post_engaged_users: metricsSource?.post_engaged_users || 0,
            schedule_end_time: scheduleEndTime,
          });
        }

        const zeroMetricInsights: FacebookInsights[] = [];
        for (const dbPost of allDbPosts) {
          const externalId = dbPost.document?.externalId || "";
          const postId = externalId.split(":").pop() || "";
          const companyId = dbPost.companyId;

          const logContextBase = {
            "meltwater.company.id": companyId,
            "meltwater.document.external_id": externalId,
            "internal.engage.ads.post_id": postId,
            "internal.engage.ads.page_id": dbPost.document?.metaData?.source?.id || "",
          };

          if (!postId || processedPostIds.has(postId)) {
            continue;
          }

          const pageId = logContextBase["internal.engage.ads.page_id"]; // Use value from context
          const attachments = dbPost.document?.attachments || [];
          const firstAttachment = attachments.length > 0 ? attachments[0] : null;
          const thumbnailUrl = (firstAttachment && "link" in firstAttachment ? firstAttachment.link : "") || "";
          const mimeType = firstAttachment && "mimeType" in firstAttachment ? firstAttachment.mimeType : undefined;

          // Attempt to fetch metrics for this DB‑only post via Wildcards (single‑post query)
          let metricsSource: Partial<WildcardsSource> = {};
          try {
            const apiQueryParams: Record<string, string | number | undefined> = {
              "start-date": start_date,
              "end-date": end_date,
              size: 1,
              match: JSON.stringify({ id: postId }),
            };

            const singlePostParamsSerializer = (params: Record<string, string | number | undefined>) => {
              const searchParams = new URLSearchParams();
              Object.entries(params).forEach(([key, value]) => {
                if (value !== undefined) searchParams.append(key, String(value));
              });
              searchParams.append("facebook-profiles", pageId);
              if (params.match) searchParams.append("match", String(params.match));
              return searchParams.toString();
            };

            const apiResponse: WildcardsResponse = await this.httpClient.get("/facebook/v1/post/top-docs", {
              params: apiQueryParams,
              headers: { "Accept-Language": "en-US,en;q=0.9", "Content-Type": "application/json" },
              paramsSerializer: (params: Record<string, string | number | undefined>): string =>
                singlePostParamsSerializer(params),
            });
            const relevantSource = apiResponse?.data?.[0]?._source;
            if (relevantSource) metricsSource = relevantSource;
          } catch (apiErr) {
            logger.error("Error fetching metrics for DB-only post", {
              ...logContextBase,
              error: { message: apiErr instanceof Error ? apiErr.message : String(apiErr) },
            });
          }

          const scheduleEndTime: string | null = null; // Boost status handled centrally

          zeroMetricInsights.push({
            id: postId,
            document_id: dbPost.document?.documentId, // Add document_id here
            externalId: externalId,
            page_id: pageId,
            created_time: new Date(dbPost.document?.body?.publishDate?.date || 0).toISOString(),
            message: dbPost.document?.body?.content?.text || "",
            permalink_url: dbPost.document?.metaData?.url || "",
            thumbnail_url: thumbnailUrl,
            mimeType: mimeType,
            company_id: companyId || "",
            reactions_count: metricsSource.reactions_count || 0,
            comments_count: metricsSource.comments_count || 0,
            shares_count: metricsSource.shares_count || 0,
            engagement_rate: metricsSource.engagement_rate || 0,
            post_video_views: metricsSource.post_video_views || 0,
            page_fans_engagement_rate: metricsSource.page_fans_engagement_rate || 0,
            post_impressions: metricsSource.post_impressions || 0,
            post_impressions_organic: metricsSource.post_impressions_organic || 0,
            post_impressions_paid: metricsSource.post_impressions_paid || 0,
            post_impressions_unique: metricsSource.post_impressions_unique || 0,
            post_impressions_organic_unique: metricsSource.post_impressions_organic_unique || 0,
            post_impressions_paid_unique: metricsSource.post_impressions_paid_unique || 0,
            post_reactions_like_total: metricsSource.post_reactions_like_total || 0,
            post_reactions_love_total: metricsSource.post_reactions_love_total || 0,
            post_reactions_wow_total: metricsSource.post_reactions_wow_total || 0,
            post_reactions_haha_total: metricsSource.post_reactions_haha_total || 0,
            post_reactions_sorry_total: metricsSource.post_reactions_sorry_total || 0,
            post_reactions_anger_total: metricsSource.post_reactions_anger_total || 0,
            post_video_views_organic: metricsSource.post_video_views_organic || 0,
            post_video_views_paid: metricsSource.post_video_views_paid || 0,
            post_video_view_time: metricsSource.post_video_view_time || 0,
            post_video_avg_time_watched: metricsSource.post_video_avg_time_watched || 0,
            post_clicks: metricsSource.post_clicks || 0,
            post_clicks_unique: metricsSource.post_clicks_unique || 0,
            post_clicks_link: metricsSource.post_clicks_link || 0,
            post_clicks_other: metricsSource.post_clicks_other || 0,
            post_clicks_photo: metricsSource.post_clicks_photo || 0,
            post_clicks_video: metricsSource.post_clicks_video || 0,
            post_engaged_users: metricsSource.post_engaged_users || 0,
            schedule_end_time: scheduleEndTime,
          });
        }
        logger.info("Added DB-only posts with zero metrics", {
          "internal.engage.ads.count": zeroMetricInsights.length,
        });

        const combinedAndZeroMetricInsights = combinedInsights.concat(zeroMetricInsights);
        let finalFilteredInsights: FacebookInsights[]; // This will hold the list after potential keyword filtering

        // Apply keyword filter *after* combining all sources, if keyword exists
        if (match?.captionKeyword) {
          const keyword = match.captionKeyword.toLowerCase();
          logger.info("Applying final in-memory keyword filter", {
            "internal.engage.ads.caption_keyword": keyword,
            "internal.engage.ads.count_before_filter": combinedAndZeroMetricInsights.length,
          });
          // Filter the combined list
          finalFilteredInsights = combinedAndZeroMetricInsights.filter((insight) =>
            insight.message?.toLowerCase().includes(keyword),
          );
          logger.info("Applied final in-memory keyword filter", {
            "internal.engage.ads.count_after_filter": finalFilteredInsights.length,
          });
        } else {
          // If no keyword, the final list is simply the combined list
          finalFilteredInsights = combinedAndZeroMetricInsights;
          logger.info("No keyword filter applied", {
            "internal.engage.ads.count_before_pagination": finalFilteredInsights.length,
          });
        }

        // Sort the merged list according to the requested metric/order before pagination
        if (sortByMetric && sort_by) {
          const sortField = mapFacebookSortMetric(sort_by) || "created_time";

          const getMetricValue = (insight: FacebookInsights, field: string): number => {
            const value = (insight as unknown as Record<string, unknown>)[field];
            return typeof value === "number" ? value : 0;
          };
          finalFilteredInsights.sort((a, b) => {
            const aVal = getMetricValue(a, sortField);
            const bVal = getMetricValue(b, sortField);
            return order === "asc" ? aVal - bVal : bVal - aVal;
          });
        }

        // Log results before pagination, using filteredTotalCount as the authoritative count
        logger.info("Final results (Combined + Filtered)", {
          "internal.engage.ads": {
            count_before_pagination: finalFilteredInsights.length,
            final_total_count_reported: filteredTotalCount,
          },
        });

        // Paginate the *final filtered* list
        const paginatedInsights = finalFilteredInsights.slice(start_from, start_from + size);
        logger.info("Applied pagination to final filtered list", {
          "internal.engage.ads": {
            requested_size: size,
            start_from: start_from,
            final_paginated_size: paginatedInsights.length,
            final_total_count_reported: filteredTotalCount, // Log again for clarity
          },
        });

        // *** ADD FINAL LOG BEFORE RETURN ***
        logger.info("Returning from getFacebookInsightsSorted", {
          "internal.engage.ads.returned_insight_count": paginatedInsights.length,
          "internal.engage.ads.returned_total_count": filteredTotalCount, // Log the count being returned
        });

        // Return the paginated insights and the accurate total count *after* all filtering
        return { insights: paginatedInsights, totalCount: filteredTotalCount };
      } else {
        // This case should ideally not happen if routing is correct, but return the initial DB count ($text based) if it does.
        logger.error(
          "getFacebookInsightsSorted called without sortByMetric or sort_by. This indicates a routing issue. Returning empty list.",
          {
            error: { message: "Missing sortByMetric or sort_by parameter" },
            "internal.engage.ads": {
              sort_by_metric: sortByMetric,
              sort_by: sort_by,
            },
          },
        );
        return { insights: [], totalCount: filteredTotalCount };
      }
    } catch (error) {
      logger.error("Error fetching sorted Facebook insights (Revised)", {
        error: {
          message: error instanceof Error ? error.message : String(error),
          stack_trace: error instanceof Error ? error.stack : undefined,
        },
      });
      throw error;
    }
  }

  @tracer.captureMethod({ subSegmentName: "FacebookInsightsRepository:getFacebookTotalCount" })
  async getFacebookTotalCount(
    start_date: string,
    end_date: string,
    captionKeyword?: string,
    profileIds?: string[],
  ): Promise<number> {
    try {
      logger.info("Getting total count directly from DB.", {
        "internal.engage.ads": {
          date_range: { start_date, end_date },
          caption_keyword: captionKeyword,
          profile_ids: profileIds,
        },
      });
      await connectToDatabase();
      const dbQuery: FacebookInsightsQuery = {
        "document.body.publishDate.date": {
          $gte: new Date(start_date).getTime(),
          $lte: new Date(end_date).getTime(),
        },
        channel: "facebook",
      };
      if (profileIds && profileIds.length > 0) {
        dbQuery["document.metaData.source.id"] = { $in: profileIds };
      }
      if (captionKeyword) {
        // Use a case-insensitive regex filter to match the logic in getFacebookInsightsSorted
        dbQuery["document.body.content.text"] = { $regex: captionKeyword, $options: "i" };
      }
      const dbCount = await WarpzoneModel.countDocuments(dbQuery);
      logger.info("DB total count (using regex for keyword if provided)", {
        "internal.engage.ads.db_count": dbCount,
      });
      return dbCount;
    } catch (error) {
      logger.error("Error fetching Facebook count from DB", {
        error: {
          message: error instanceof Error ? error.message : String(error),
          stack_trace: error instanceof Error ? error.stack : undefined,
        },
        "internal.engage.ads": {
          date_range: { start_date, end_date },
          caption_keyword: captionKeyword,
          profile_ids: profileIds,
        },
      });
      return 0;
    }
  }
}
