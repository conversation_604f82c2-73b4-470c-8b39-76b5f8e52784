// root/packages/facebook-services/src/boost-facebook-post.command.ts
import type { ICommand } from "@meltwater/cqrs"; // Assuming you have cqrs core package
import { LambdaTracer } from "@meltwater/lambda-monitoring";
import type {
  BoostFacebookPostParams,
  BoostFacebookPostResult,
  IFacebookBoostRepository,
} from "../types/facebook-boost";

const tracer = LambdaTracer.getInstance();

export class BoostFacebookPostCommand implements ICommand<BoostFacebookPostParams, BoostFacebookPostResult> {
  constructor(private facebookBoostRepository: IFacebookBoostRepository) {}

  @tracer.captureMethod({ subSegmentName: "BoostFacebookPostCommand:execute" })
  public async execute(params: BoostFacebookPostParams): Promise<BoostFacebookPostResult> {
    return this.facebookBoostRepository.boostFacebookPost(params);
  }
}
