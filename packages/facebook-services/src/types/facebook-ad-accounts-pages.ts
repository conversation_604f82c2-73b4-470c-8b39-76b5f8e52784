export interface ExtendedFacebookAdAccountsPagesQueryInput {
  applicationCompanyId: string;
  userId: string;
  activeInd: number;
  statusInd: string[];
  channels: string[];
  accessToken: string;
}

export interface S31FilteredCredsRequestBody {
  query: string;
  variables: {
    query: {
      applicationCompanyId: string;
      userId: string;
      activeInd: number;
      statusInd: string[];
      channels: string[];
    };
  };
}

export interface S31FilteredCredsResponse {
  data?: {
    companyCredentialsFilteredQuery: FacebookAccountsPagesData;
  };
}

export type FacebookAccountsPagesData = Array<{
  channelName: string;
  targetPageName: string;
  socialAccountId: string;
  credentialId?: number;
}>;
