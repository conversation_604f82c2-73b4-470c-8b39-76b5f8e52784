export interface GetFacebookInsightsQueryParams {
  start_date: string;
  end_date: string;
  size?: number;
  order?: string;
  start_from?: number;
  sort_by?: string;
  profileIds?: string[];
  match?: { captionKeyword?: string };
  sortByMetric?: boolean;
}

export interface FacebookInsights {
  id: string;
  document_id?: string;
  page_id: string;
  externalId: string;
  created_time: string;
  message: string;
  permalink_url: string;
  thumbnail_url?: string;
  mimeType?: string;
  company_id: string;
  reactions_count: number;
  comments_count: number;
  shares_count: number;
  engagement_rate: number;
  post_video_views: number;
  page_fans_engagement_rate: number;
  post_impressions: number;
  post_impressions_organic: number;
  post_impressions_paid: number;
  post_impressions_unique: number;
  post_impressions_organic_unique: number;
  post_impressions_paid_unique: number;
  post_reactions_like_total: number;
  post_reactions_love_total: number;
  post_reactions_wow_total: number;
  post_reactions_haha_total: number;
  post_reactions_sorry_total: number;
  post_reactions_anger_total: number;
  post_video_views_organic: number;
  post_video_views_paid: number;
  post_video_view_time: number;
  post_video_avg_time_watched: number;
  post_clicks: number;
  post_clicks_unique: number;
  post_clicks_link: number;
  post_clicks_other: number;
  post_clicks_photo: number;
  post_clicks_video: number;
  post_engaged_users: number;
  schedule_end_time?: string | null;
}

export interface WildcardsSource {
  id: string;
  page_id?: string;
  created_time?: string;
  status_type?: string;
  message?: string;
  permalink_url?: string;
  from?: {
    name?: string;
    id?: string;
  };
  is_published?: boolean;
  promotion_status?: string;
  type?: string;
  comments_count?: number;
  reactions_count?: number;
  shares_count?: number;
  engagement_rate?: number;
  page_fans_engagement_rate?: number;
  post_engaged_users?: number;
  post_impressions?: number;
  post_impressions_organic?: number;
  post_impressions_paid?: number;
  post_impressions_unique?: number;
  post_impressions_organic_unique?: number;
  post_impressions_paid_unique?: number;
  post_reactions_like_total?: number;
  post_reactions_love_total?: number;
  post_reactions_wow_total?: number;
  post_reactions_haha_total?: number;
  post_reactions_sorry_total?: number;
  post_reactions_anger_total?: number;
  post_video_views?: number;
  post_video_view_time?: number;
  post_video_views_organic?: number;
  post_video_views_paid?: number;
  post_video_avg_time_watched?: number;
  post_clicks?: number;
  post_clicks_unique?: number;
  post_clicks_link?: number;
  post_clicks_other?: number;
  post_clicks_photo?: number;
  post_clicks_video?: number;
  updated_time?: string;
  attachments?: {
    data?: Array<{
      media?: {
        mime_type?: string;
        image?: {
          height?: string;
          width?: string;
          src?: string;
        };
      };
      target?: {
        id?: string;
        url?: string;
      };
    }>;
  };
}

export interface WildcardsResponse {
  data: Array<{
    _source: WildcardsSource;
  }>;
}
export interface GetFacebookInsightsQueryResponse {
  insights: FacebookInsights[];
  totalCount: number;
}

export interface FacebookInsightsQuery {
  "document.body.publishDate.date": {
    $gte: number;
    $lte: number;
  };
  channel: "facebook";
  "document.metaData.source.id"?: {
    $in: string[];
  };
  $text?: {
    $search: string;
    $language?: string;
    $caseSensitive?: boolean;
    $diacriticSensitive?: boolean;
  };
  "document.body.content.text"?: {
    $regex: string;
    $options: string;
  };
}
