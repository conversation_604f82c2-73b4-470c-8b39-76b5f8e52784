export interface GetGroupedMessageParams {
  nativeId: string;
}

export interface GroupedMessageProfileResult {
  legacyMessageId?: string;
  credentialId?: number;
}

export interface GroupedMessageChannelResult {
  channel?: string;
  profiles?: GroupedMessageProfileResult[];
}

export interface GroupedMessageResult {
  groupedMessage?: {
    channels?: GroupedMessageChannelResult[];
  };
}

export interface BoostFacebookPostParams {
  messageId: string;
  campaignId: string;
  duration: number;
  budget: number;
  audienceId: string;
  adAccountCredentialId: number;
}

export interface BoostFacebookPostResult {
  boostId?: string;
  boostingErrors?: string[];
}

export interface IFacebookBoostRepository {
  getGroupedMessage(params: GetGroupedMessageParams): Promise<GroupedMessageResult | null>;
  boostFacebookPost(params: BoostFacebookPostParams): Promise<BoostFacebookPostResult>;
}
