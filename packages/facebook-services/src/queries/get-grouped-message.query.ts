// root/packages/facebook-services/src/get-grouped-message.query.ts
import type { IQuery } from "@meltwater/cqrs"; // Assuming you have cqrs core package
import { LambdaTracer } from "@meltwater/lambda-monitoring";
import type { GetGroupedMessageParams, GroupedMessageResult, IFacebookBoostRepository } from "../types/facebook-boost";

const tracer = LambdaTracer.getInstance();

export class GetGroupedMessageQuery implements IQuery<GetGroupedMessageParams, GroupedMessageResult | null> {
  constructor(private facebookBoostRepository: IFacebookBoostRepository) {}

  @tracer.captureMethod({ subSegmentName: "GetGroupedMessageQuery:execute" })
  public async execute(params: GetGroupedMessageParams): Promise<GroupedMessageResult | null> {
    return this.facebookBoostRepository.getGroupedMessage(params);
  }
}
