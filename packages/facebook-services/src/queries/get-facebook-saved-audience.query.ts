import type { IQuery } from "@meltwater/cqrs";
import { LambdaTracer } from "@meltwater/lambda-monitoring";
import type { GetFacebookSavedAudienceParams, FacebookSavedAudience } from "../types/facebook-saved-audience";

const tracer = LambdaTracer.getInstance();

export interface IFacebookSavedAudienceRepository {
  getFacebookSavedAudience(params: GetFacebookSavedAudienceParams): Promise<FacebookSavedAudience[]>;
}

export class GetFacebookSavedAudienceQuery implements IQuery<GetFacebookSavedAudienceParams, FacebookSavedAudience[]> {
  constructor(private facebookSavedAudienceRepository: IFacebookSavedAudienceRepository) {}

  @tracer.captureMethod({ subSegmentName: "GetFacebookSavedAudienceQuery:execute" })
  public async execute(params: GetFacebookSavedAudienceParams): Promise<FacebookSavedAudience[]> {
    return this.facebookSavedAudienceRepository.getFacebookSavedAudience(params);
  }
}
