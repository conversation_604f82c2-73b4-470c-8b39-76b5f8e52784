import type { IQuery } from "@meltwater/cqrs";
import { LambdaTracer } from "@meltwater/lambda-monitoring";
import type { GetFacebookAdCampaignsParams, FacebookAdCampaign } from "../types/facebook-ad-campaigns";

const tracer = LambdaTracer.getInstance();

export interface IFacebookAdCampaignRepository {
  getFacebookAdCampaigns(params: GetFacebookAdCampaignsParams): Promise<FacebookAdCampaign[]>;
}

export class GetFacebookAdCampaignsQuery implements IQuery<GetFacebookAdCampaignsParams, FacebookAdCampaign[]> {
  constructor(private facebookAdCampaignRepository: IFacebookAdCampaignRepository) {}

  @tracer.captureMethod({ subSegmentName: "GetFacebookAdCampaignsQuery:execute" })
  public async execute(params: GetFacebookAdCampaignsParams): Promise<FacebookAdCampaign[]> {
    return this.facebookAdCampaignRepository.getFacebookAdCampaigns(params);
  }
}
