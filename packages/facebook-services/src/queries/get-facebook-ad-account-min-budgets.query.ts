import type { IQuery } from "@meltwater/cqrs";
import { LambdaTracer } from "@meltwater/lambda-monitoring";
import type {
  GetFacebookAdAccountMinBudgetsParams,
  FacebookAdAccountMinBudgets,
} from "../types/facebook-ad-account-min-budgets";

const tracer = LambdaTracer.getInstance();

export interface IFacebookAdAccountMinBudgetsRepository {
  getFacebookAdAccountMinBudgets(params: GetFacebookAdAccountMinBudgetsParams): Promise<FacebookAdAccountMinBudgets>;
}

export class GetFacebookAdAccountMinBudgetsQuery
  implements IQuery<GetFacebookAdAccountMinBudgetsParams, FacebookAdAccountMinBudgets>
{
  constructor(private facebookAdAccountMinBudgetsRepository: IFacebookAdAccountMinBudgetsRepository) {}

  @tracer.captureMethod({ subSegmentName: "GetFacebookAdAccountMinBudgetsQuery:execute" })
  public async execute(params: GetFacebookAdAccountMinBudgetsParams): Promise<FacebookAdAccountMinBudgets> {
    return this.facebookAdAccountMinBudgetsRepository.getFacebookAdAccountMinBudgets(params);
  }
}
