import type { GetFacebookInsightsQueryParams, FacebookInsights } from "../types/facebook-insights";
import type { FacebookInsightsRepository } from "../facebook-insights-repository";
import { LambdaTracer, LambdaLogger } from "@meltwater/lambda-monitoring"; // Added LambdaLogger import

const tracer = LambdaTracer.getInstance();

export class GetFacebookInsightsQuery {
  constructor(private readonly repository: FacebookInsightsRepository) {}

  @tracer.captureMethod({ subSegmentName: "GetFacebookInsightsQuery:execute" })
  public async execute(
    params: GetFacebookInsightsQueryParams,
  ): Promise<{ insights: FacebookInsights[]; totalCount: number }> {
    if (params.sortByMetric) {
      LambdaLogger.getInstance().info("Executing GetFacebookInsightsQuery with metric sorting (API First)");
      const result = await this.repository.getFacebookInsightsSorted(params);
      return { insights: result.insights, totalCount: result.totalCount };
    } else {
      LambdaLogger.getInstance().info("Executing GetFacebookInsightsQuery with default sorting (DB First)");
      const result = await this.repository.getFacebookInsights(params);
      return { insights: result.insights, totalCount: result.totalCount };
    }
  }
}
