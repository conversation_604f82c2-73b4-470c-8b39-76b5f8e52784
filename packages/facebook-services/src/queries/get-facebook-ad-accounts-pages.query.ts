import type { IQuery } from "@meltwater/cqrs";
import { LambdaTracer } from "@meltwater/lambda-monitoring";
import type { FacebookAdAccountsPagesRepository } from "../facebook-ad-accounts-pages-repository";
import type {
  ExtendedFacebookAdAccountsPagesQueryInput,
  FacebookAccountsPagesData,
} from "../types/facebook-ad-accounts-pages";

const tracer = LambdaTracer.getInstance();

export class GetFacebookAdAccountsPagesQuery
  implements IQuery<ExtendedFacebookAdAccountsPagesQueryInput, FacebookAccountsPagesData>
{
  constructor(private readonly fbRepository: FacebookAdAccountsPagesRepository) {}

  @tracer.captureMethod({ subSegmentName: "GetFacebookAdAccountsPagesQuery:execute" })
  public async execute(input: ExtendedFacebookAdAccountsPagesQueryInput): Promise<FacebookAccountsPagesData> {
    return this.fbRepository.getFacebookAdAccountsPages(input);
  }
}
