{"name": "@meltwater/engage-ads-facebook-services", "version": "1.31.3", "author": "Team Area51", "description": "Facebook services package for engage-ads", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "clean": "rimraf dist tsconfig.tsbuildinfo", "registry:clear-cache": "bash ../../scripts/mpkg-clear-cache.sh $(echo $npm_package_name | cut -d '/' -f 2) $npm_package_version", "test": "jest"}, "dependencies": {"@meltwater/cqrs": "^0.0.1", "@meltwater/engage-ads-commons": "^2.9.2", "@meltwater/engage-ads-db-sdk": "^1.22.2", "@meltwater/engage-ads-types": "^2.5.3", "@meltwater/lambda-monitoring": "^0.2.5", "class-validator": "^0.14.0", "date-fns": "^4.1.0"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/node": "^20.14.2", "jest": "^29.7.0", "rimraf": "^5.0.1", "ts-jest": "^29.1.4", "typescript": "^5.3.3"}, "files": ["dist"], "license": "MIT", "publishConfig": {"access": "restricted", "registry": "https://registry.npmjs.org/"}, "mpkg": ["dist/**/*"]}