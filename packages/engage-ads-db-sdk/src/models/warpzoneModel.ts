import mongoose from "mongoose";
import type { Document, Model } from "mongoose";
import type { WarpzonePayload } from "../types/warpzone";
import { LambdaLogger } from "@meltwater/lambda-monitoring";

const logger = LambdaLogger.getInstance();

mongoose.set("debug", (collectionName, methodName, ...methodArgs) => {
  logger.debug("Mongoose operation", {
    "internal.engage.ads.collection": collectionName,
    "internal.engage.ads.operation": methodName,
    "internal.engage.ads.args": JSON.stringify(methodArgs),
  });
});

export interface WarpzoneDocument extends Document, WarpzonePayload {}

interface WarpzoneModel extends Model<WarpzoneDocument> {
  createPayload(data: WarpzonePayload): Promise<WarpzoneDocument>;
}

const authorInfoSchema = new mongoose.Schema(
  {
    rawName: String,
    externalId: String,
  },
  { _id: false },
);

const authorSchema = new mongoose.Schema(
  {
    authorInfo: authorInfoSchema,
  },
  { _id: false },
);

const namedEntitySchema = new mongoose.Schema(
  {
    canonicalName: String,
    name: { type: String, required: true },
    type: { type: String, required: true },
    sentiment: {
      discrete: { type: String, required: true },
    },
    knowledgeGraphId: String,
  },
  { _id: false },
);

const topicSchema = new mongoose.Schema(
  {},
  {
    _id: false,
    strict: false,
    id: false,
  },
);

const attachmentSchema = new mongoose.Schema(
  {
    link: String,
    mimeType: String,
  },
  { _id: false },
);

const keyPhraseSchema = new mongoose.Schema(
  {
    phrase: String,
    sentiment: {
      discrete: String,
    },
  },
  { _id: false },
);

const warpzoneSchema = new mongoose.Schema<WarpzoneDocument>(
  {
    from: { type: String, required: true },
    applicationUserId: { type: String, required: true },
    companyId: { type: String, required: true },
    channel: { type: String, required: true },
    document: {
      id: String,
      body: {
        publishDate: {
          date: Number,
        },
        content: {
          text: String,
          textLength: Number,
        },
        contentTags: [String],
        emojis: [String],
      },
      metaData: {
        fetchingTime: Number,
        url: String,
        source: {
          id: String,
          location: {
            countryCode: String,
          },
          informationType: String,
          socialOriginType: String,
        },
        provider: {
          type: { type: String, required: true },
        },
        mainAuthor: {
          externalId: String,
        },
        authors: [authorSchema],
        mediaType: String,
        discussionType: String,
        applicationTags: [String],
        userTags: [String],
        analyzedUrl: String,
        randomSampling: Number,
        receivedTime: Number,
        indexingTime: Number,
        domains: [String],
      },
      enrichments: {
        sentiment: {
          discrete: String,
        },
        languageCode: String,
        charikarLSH: String,
        nsfw: Boolean,
        namedEntities: [namedEntitySchema],
        keyPhrases: [keyPhraseSchema],
        topLevelEntitySentiment: {
          n: [String],
          p: [String],
          u: [String],
          v: [String],
        },
        emotions: [String],
        topics: [topicSchema],
        conceptsTop: {
          type: [mongoose.Schema.Types.Mixed],
          default: [],
        },
        categories: {
          type: [mongoose.Schema.Types.Mixed],
          default: [],
        },
      },
      attachments: [attachmentSchema],
      externalId: String,
      systemData: {
        policies: {
          storage: {
            privateTo: [String],
          },
        },
      },
      documentId: String,
    },
    credentialId: { type: Number, required: true },
  },
  {
    timestamps: true,
    collection: "warpzone-events",
  },
);

// --- INDEXES ---

// Compound index for common queries (including 'channel').
warpzoneSchema.index({
  channel: 1,
  "document.metaData.source.id": 1,
  "document.body.publishDate.date": 1,
  companyId: 1,
});

// Text index for efficient text search on content.
warpzoneSchema.index({ "document.body.content.text": "text" });

// Unique index to enforce uniqueness of companyId and document.documentId combinations.
warpzoneSchema.index({ companyId: 1, "document.documentId": 1 }, { unique: true });

// Index for efficient querying by externalId.
warpzoneSchema.index({ "document.externalId": 1 });

warpzoneSchema.statics.createPayload = async function (data: WarpzonePayload): Promise<WarpzoneDocument> {
  try {
    const doc = await this.findOne({
      companyId: data.companyId,
      "document.documentId": data.document?.id,
    });
    if (!doc) {
      const payload = new this(data);
      await payload.save();
      logger.info("Warpzone payload created", {
        "meltwater.document.id": data.document?.id,
        "user.id": data.applicationUserId,
        "meltwater.company.id": data.companyId,
        "meltwater.social.credential_id": data.credentialId,
        "meltwater.document.external_id": data.document?.externalId,
      });
      return payload;
    } else {
      logger.warn("Warpzone payload already exists", { "meltwater.document.id": data.document?.id });
      return doc;
    }
  } catch (error) {
    logger.error("Error creating warpzone payload", { error, "meltwater.document.id": data.document?.id });
    throw error;
  }
};

export const WarpzoneModel = mongoose.model<WarpzoneDocument, WarpzoneModel>("WarpzoneEvent", warpzoneSchema);
