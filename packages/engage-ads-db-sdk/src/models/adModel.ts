import { Schema, model } from "mongoose";
import type { Document, Model, Types } from "mongoose";
import type { TikTokAdCreative } from "../types";
import { LambdaLogger } from "@meltwater/lambda-monitoring";

const logger = LambdaLogger.getInstance();

export interface AdDocument extends TikTokAdCreative, Document {
  _id: Types.ObjectId;
  post_id?: string;
  audience_set_id?: string;
  channel: "facebook" | "tiktok" | "linkedin";
  /** LinkedIn only - Campaign Group urn */
  campaign_group?: string;
}

interface AdModelType extends Model<AdDocument> {
  createAd(data: TikTokAdCreative): Promise<AdDocument>;
  findByVideoId(videoId: string): Promise<AdDocument | null>;
}

const adSchema = new Schema<AdDocument, AdModelType>(
  {
    ad_id: { type: String, unique: true },
    ad_name: String,
    advertiser_id: String,
    campaign_id: String,
    adgroup_id: String,
    video_id: String,
    post_id: String, // Added for Facebook posts
    audience_set_id: String, // Added for Facebook posts
    channel: { type: String, required: true, enum: ["facebook", "tiktok", "linkedin"] }, // Added channel field; extended to LinkedIn
    schedule_end_time: { type: String },
    campaign_group: String, // Optional LinkedIn campaign group
    companyId: { type: String, required: true },
  },
  { timestamps: true },
);

adSchema.static("createAd", async function (data: TikTokAdCreative): Promise<AdDocument> {
  const ad = new this(data);
  await ad.save();
  logger.info("Ad created", {
    "meltwater.ad.id": ad.ad_id,
    "internal.engage.ads.collection": "ads",
    "internal.engage.ads.operation": "create",
  });
  return ad;
});

adSchema.static("findByVideoId", async function (videoId: string): Promise<AdDocument | null> {
  const ad = await this.findOne({ video_id: videoId });
  if (ad) {
    logger.info("Ad found by video ID", {
      "meltwater.ad.id": ad.ad_id,
      "internal.engage.ads.video_id": videoId,
      "internal.engage.ads.collection": "ads",
      "internal.engage.ads.operation": "find",
    });
  }
  return ad;
});

adSchema.index({ companyId: 1 });

export const AdModel = model<AdDocument, AdModelType>("Ad", adSchema);
