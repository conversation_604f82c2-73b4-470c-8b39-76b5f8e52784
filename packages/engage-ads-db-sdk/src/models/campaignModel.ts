import { Schema, model } from "mongoose";
import type { Document, Model, Types } from "mongoose";
import type { TikTokCampaign, CampaignCreateInput } from "../types";
import { LambdaLogger } from "@meltwater/lambda-monitoring";

const logger = LambdaLogger.getInstance();

export interface CampaignDocument extends TikTokCampaign, Document {
  _id: Types.ObjectId;
}

interface CampaignModelType extends Model<CampaignDocument> {
  createCampaign(data: CampaignCreateInput): Promise<CampaignDocument>;
  // Add other static methods if needed
}

const campaignSchema = new Schema<CampaignDocument, CampaignModelType>(
  {
    secondary_status: String,
    objective_type: String,
    operation_status: String,
    rta_id: { type: String, default: null },
    is_new_structure: Boolean,
    is_advanced_dedicated_campaign: Boolean,
    modify_time: String,
    budget: Number,
    is_search_campaign: Boolean,
    budget_mode: String,
    campaign_id: { type: String, unique: true },
    roas_bid: Number,
    rta_product_selection_enabled: Boolean,
    deep_bid_type: { type: String, default: null },
    is_smart_performance_campaign: Boolean,
    create_time: String,
    campaign_type: String,
    advertiser_id: String,
    objective: String,
    campaign_name: String,
  },
  { timestamps: true },
);

campaignSchema.static("createCampaign", async function (data: CampaignCreateInput): Promise<CampaignDocument> {
  const campaign = new this(data);
  await campaign.save();
  logger.info("Campaign created", {
    "meltwater.campaign.id": campaign.campaign_id,
    "internal.engage.ads.collection": "campaigns",
    "internal.engage.ads.operation": "create",
  });
  return campaign;
});

export const CampaignModel = model<CampaignDocument, CampaignModelType>("Campaign", campaignSchema);
