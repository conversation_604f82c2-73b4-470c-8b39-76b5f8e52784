import { Schema, model } from "mongoose";
import type { Document, Model, Types } from "mongoose";
import type { TikTokAdGroup, AdGroupCreateInput } from "../types";
import { LambdaLogger } from "@meltwater/lambda-monitoring";

const logger = LambdaLogger.getInstance();

export interface AdGroupDocument extends TikTokAdGroup, Document {
  _id: Types.ObjectId;
}

interface AdGroupModelType extends Model<AdGroupDocument> {
  createAdGroup(data: AdGroupCreateInput): Promise<AdGroupDocument>;
  // Add other static methods if needed
}

const adGroupSchema = new Schema<AdGroupDocument, AdGroupModelType>(
  {
    adgroup_id: { type: String, unique: true },
    adgroup_name: String,
    advertiser_id: String,
    campaign_id: String,
    billing_event: String,
    bid_price: Number,
    budget: Number,
    budget_mode: String,
    schedule_start_time: String,
    schedule_end_time: String,
    optimization_goal: String,
    placements: [String],
    location_ids: [String],
    pacing: String,
  },
  { timestamps: true },
);

adGroupSchema.static("createAdGroup", async function (data: AdGroupCreateInput): Promise<AdGroupDocument> {
  const adGroup = new this(data);
  await adGroup.save();
  logger.info("AdGroup created", {
    "meltwater.adgroup.id": adGroup.adgroup_id,
    "meltwater.campaign.id": adGroup.campaign_id,
    "internal.engage.ads.collection": "adgroups",
    "internal.engage.ads.operation": "create",
  });
  return adGroup;
});

export const AdGroupModel = model<AdGroupDocument, AdGroupModelType>("AdGroup", adGroupSchema);
