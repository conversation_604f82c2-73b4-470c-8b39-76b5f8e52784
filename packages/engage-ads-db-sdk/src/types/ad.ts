export interface TikTokAdData {
  code: number;
  message: string;
  request_id: string;
  data: TikTokAdCreationResponse;
}

export interface TikTokAdCreationResponse {
  ad_ids: string[];
  need_audit: boolean;
  creatives: TikTokAdCreative[];
}

export interface TikTokAdCreative {
  ad_id: string;
  ad_name: string;
  advertiser_id: string;
  campaign_id: string;
  adgroup_id: string;
  video_id: string;
  schedule_end_time?: string;
  companyId?: string;
}

export interface LinkedInAdCreative {
  ad_id: string;
  ad_name: string;
  advertiser_id: string;
  campaign_id: string;
  adgroup_id: string;
  video_id: string;
  schedule_end_time?: string;
  campaign_group?: string;
  companyId?: string;
}

export interface FacebookAdCreative {
  ad_id: string;
  ad_name: string;
  advertiser_id: string;
  campaign_id: string;
  adgroup_id: string;
  video_id: string;
  channel: "facebook";
  post_id: string;
  audience_set_id: string;
  schedule_end_time?: string;
  companyId?: string;
}
