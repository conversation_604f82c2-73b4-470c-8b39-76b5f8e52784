export interface TikTokCampaignData {
  code: number;
  message: string;
  request_id: string;
  data: TikTokCampaign;
}

export interface TikTokCampaign {
  secondary_status: string;
  objective_type: string;
  operation_status: string;
  rta_id: string | null;
  is_new_structure: boolean;
  is_advanced_dedicated_campaign: boolean;
  modify_time: string;
  budget: number;
  is_search_campaign: boolean;
  budget_mode: string;
  campaign_id: string;
  roas_bid: number;
  rta_product_selection_enabled: boolean;
  deep_bid_type: string | null;
  is_smart_performance_campaign: boolean;
  create_time: string;
  campaign_type: string;
  advertiser_id: string;
  objective: string;
  campaign_name: string;
}

export interface CampaignCreateInput {
  campaign_id: string;
  campaign_name: string;
  advertiser_id: string;
}
