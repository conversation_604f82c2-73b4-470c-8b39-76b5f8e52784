export interface Author {
  authorInfo: {
    rawName: string;
    externalId: string;
  };
}

export interface DocumentBody {
  publishDate: {
    date: number;
  };
  content: {
    text: string;
    textLength: number;
  };
  contentTags: string[];
  emojis: string[];
}

export interface Provider {
  type: string;
}

export interface NamedEntity {
  canonicalName?: string;
  name: string;
  type: string;
  sentiment: {
    discrete: string;
  };
  knowledgeGraphId?: string;
}

export type Topic = {
  [K in `level${number}`]?: string;
};

export interface DocumentMetaData {
  fetchingTime: number;
  url: string;
  source: {
    id: string;
    location: {
      countryCode: string;
    };
    informationType: string;
    socialOriginType: string;
  };
  provider: Provider;
  mainAuthor: {
    externalId: string;
  };
  authors: Author[];
  mediaType: string;
  discussionType: string;
  applicationTags: string[];
  userTags: string[];
  analyzedUrl: string;
  randomSampling: number;
  receivedTime: number;
  indexingTime: number;
  domains: string[];
}

export interface Attachment {
  link: string;
  mimeType: string;
}

export interface KeyPhrase {
  phrase: string;
  sentiment: {
    discrete: string;
  };
}

export interface DocumentEnrichments {
  sentiment: {
    discrete: string;
  };
  languageCode: string;
  charikarLSH: string;
  nsfw: boolean;
  namedEntities: NamedEntity[];
  keyPhrases: KeyPhrase[];
  topLevelEntitySentiment: {
    n: string[];
    p: string[];
    u: string[];
    v: string[];
  };
  emotions: string[];
  topics: Topic[];
  conceptsTop: string[];
  categories: string[];
}

export interface WarpzoneContent {
  id: string;
  body: DocumentBody;
  metaData: DocumentMetaData;
  enrichments: DocumentEnrichments;
  attachments: Attachment[];
  externalId: string;
  systemData: {
    policies: {
      storage: {
        privateTo: string[];
      };
    };
  };
  documentId: string;
}

export interface WarpzonePayload {
  from: string;
  applicationUserId: string;
  companyId: string;
  channel: string;
  document: WarpzoneContent;
  credentialId: number;
}

export interface WarpzoneEvent {
  eventId: string;
  eventType: string;
  source: string;
  payloadType: string;
  payloadVersion: number;
  publisher: string;
  payload: WarpzonePayload;
  payloadEncoding: string[];
  version: number;
  publishedAt: number;
}
