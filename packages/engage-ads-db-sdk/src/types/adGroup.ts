export interface TikTokAdGroupData {
  code: number;
  message: string;
  request_id: string;
  data: TikTokAdGroup;
}

export interface TikTokAdGroup {
  adgroup_id: string;
  adgroup_name: string;
  advertiser_id: string;
  campaign_id: string;
  billing_event: string;
  bid_price: number;
  budget: number;
  budget_mode: string;
  schedule_start_time: string;
  schedule_end_time: string;
  optimization_goal: string;
  placements: string[];
  location_ids: string[];
  pacing: string;
}

export interface AdGroupCreateInput {
  adgroup_id: string;
  adgroup_name: string;
  advertiser_id: string;
  campaign_id: string;
}
