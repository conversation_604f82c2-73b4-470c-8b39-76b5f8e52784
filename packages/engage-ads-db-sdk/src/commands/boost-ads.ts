import { CampaignModel } from "../models/campaignModel";
import { AdGroupModel } from "../models/adGroupModel";
import { AdModel } from "../models/adModel";
import type { CampaignCreateInput, TikTokCampaign } from "../types/campaign";
import type { AdGroupCreateInput, TikTokAdGroup } from "../types/adGroup";
import type { TikTokAdCreative, FacebookAdCreative } from "../types/ad";
import { LambdaLogger } from "@meltwater/lambda-monitoring";

const logger = LambdaLogger.getInstance();

export class BoostAds {
  static async boostTiktokads(
    campaignData: CampaignCreateInput,
    adGroupData: AdGroupCreateInput,
    adData: TikTokAdCreative,
    companyId: string,
  ): Promise<{
    campaign: TikTokCampaign;
    adGroup: TikTokAdGroup;
    ad: TikTokAdCreative;
  }> {
    try {
      let campaignDoc = await CampaignModel.findOne({ campaign_id: campaignData.campaign_id }).exec();

      if (campaignDoc) {
        logger.info("Campaign exists in database", {
          "meltwater.campaign.id": campaignDoc.campaign_id,
          "meltwater.company.id": companyId,
          "internal.engage.ads.status": "skipped",
        });
      } else {
        logger.info("Creating new campaign", {
          "internal.engage.ads.campaign": JSON.stringify(campaignData),
          "meltwater.company.id": companyId,
        });
        await CampaignModel.createCampaign(campaignData);
        campaignDoc = await CampaignModel.findOne({ campaign_id: campaignData.campaign_id }).exec();
      }

      // If still null, we cannot continue
      if (!campaignDoc) {
        throw new Error(`Could not find or create Campaign for ID: ${campaignData.campaign_id}`);
      }

      let adGroupDoc = await AdGroupModel.findOne({ adgroup_id: adGroupData.adgroup_id }).exec();

      if (adGroupDoc) {
        logger.info("AdGroup exists in database", {
          "meltwater.adgroup.id": adGroupDoc.adgroup_id,
          "meltwater.campaign.id": campaignDoc.campaign_id,
          "meltwater.company.id": companyId,
          "internal.engage.ads.status": "skipped",
        });
      } else {
        logger.info("Creating new ad group", {
          "internal.engage.ads.adgroup": JSON.stringify(adGroupData),
          "meltwater.campaign.id": campaignDoc.campaign_id,
          "meltwater.company.id": companyId,
        });
        await AdGroupModel.createAdGroup(adGroupData);
        adGroupDoc = await AdGroupModel.findOne({ adgroup_id: adGroupData.adgroup_id }).exec();
      }

      if (!adGroupDoc) {
        throw new Error(`Could not find or create AdGroup for ID: ${adGroupData.adgroup_id}`);
      }

      const adDataWithCompanyId = { ...adData, companyId, channel: "tiktok" as const };

      let adDoc = await AdModel.findOne({ ad_id: adData.ad_id }).exec();

      if (adDoc) {
        logger.info("Ad exists in database", {
          "meltwater.ad.id": adDoc.ad_id,
          "meltwater.adgroup.id": adGroupDoc.adgroup_id,
          "meltwater.campaign.id": campaignDoc.campaign_id,
          "meltwater.company.id": companyId,
          "internal.engage.ads.status": "skipped",
        });
      } else {
        logger.info("Creating new ad", {
          "internal.engage.ads.ad": JSON.stringify(adData),
          "meltwater.adgroup.id": adGroupDoc.adgroup_id,
          "meltwater.campaign.id": campaignDoc.campaign_id,
          "meltwater.company.id": companyId,
        });
        adDoc = await AdModel.createAd(adDataWithCompanyId);
      }

      if (!adDoc) {
        throw new Error(`Could not find or create Ad for ID: ${adData.ad_id}`);
      }

      logger.info("TikTok ads boost completed", {
        "meltwater.campaign.id": campaignDoc.campaign_id,
        "meltwater.adgroup.id": adGroupDoc.adgroup_id,
        "meltwater.ad.id": adDoc.ad_id,
        "meltwater.company.id": companyId,
      });

      return {
        campaign: campaignDoc.toObject(),
        adGroup: adGroupDoc.toObject(),
        ad: adDoc.toObject(),
      };
    } catch (error) {
      logger.error("Failed to boost TikTok ads", {
        "internal.engage.ads.error": error,
        "meltwater.company.id": companyId,
      });
      throw error;
    }
  }

  static async boostLinkedInAds(
    campaignData: CampaignCreateInput,
    adData: (TikTokAdCreative & { channel: "linkedin" }) & {
      post_id?: string;
      schedule_end_time?: string;
      campaign_group?: string;
    },
    companyId: string,
  ): Promise<{
    campaign: TikTokCampaign;
    ad: TikTokAdCreative;
  }> {
    try {
      let campaignDoc = await CampaignModel.findOne({ campaign_id: campaignData.campaign_id }).exec();

      if (!campaignDoc) {
        logger.info("Creating LinkedIn campaign record", {
          campaignData: JSON.stringify(campaignData),
          companyId,
        });
        await CampaignModel.createCampaign(campaignData);
        campaignDoc = await CampaignModel.findOne({ campaign_id: campaignData.campaign_id }).exec();
      }

      if (!campaignDoc) throw new Error("Unable to persist LinkedIn campaign");

      let adDoc = await AdModel.findOne({ ad_id: adData.ad_id }).exec();
      if (!adDoc) {
        logger.info("Creating LinkedIn ad record", { adData: JSON.stringify(adData), companyId });
        adDoc = await AdModel.createAd({ ...adData, companyId });
      }

      if (!adDoc) throw new Error("Unable to persist LinkedIn ad record");

      return { campaign: campaignDoc.toObject(), ad: adDoc.toObject() };
    } catch (error) {
      logger.error("Failed to boost LinkedIn ads", {
        error,
        companyId,
      });
      throw error;
    }
  }

  static async boostFacebookAds(adData: FacebookAdCreative, companyId: string): Promise<{ ad: FacebookAdCreative }> {
    try {
      let adDoc = await AdModel.findOne({ ad_id: adData.ad_id, companyId }).exec();

      if (adDoc) {
        logger.info("Facebook boost record already exists in database", {
          "meltwater.ad.id": adData.ad_id,
          "internal.engage.ads.companyId": companyId,
          "internal.engage.ads.status": "skipped_creation",
        });
      } else {
        logger.info("Creating new Facebook boost record", {
          "meltwater.ad.id": adData.ad_id,
          "internal.engage.ads.post_id": adData.post_id,
          "internal.engage.ads.audience_set_id": adData.audience_set_id,
          "internal.engage.ads.companyId": companyId,
        });

        adDoc = await AdModel.createAd({ ...adData, companyId });
      }

      if (!adDoc) {
        throw new Error(`Could not find or create Facebook Ad for ID: ${adData.ad_id}`);
      }

      logger.info("Facebook boost completed", {
        "meltwater.ad.id": adDoc.ad_id,
        "meltwater.company.id": companyId,
        "internal.engage.ads.collection": "ads",
        "internal.engage.ads.operation": "create",
      });

      return { ad: adDoc.toObject() };
    } catch (error) {
      logger.error("Failed to boost Facebook ads", {
        "internal.engage.ads.error": error,
        "meltwater.company.id": companyId,
      });
      throw error;
    }
  }

  static async checkBoostStatus(
    ids: string[],
    platform: "tiktok" | "facebook" | "linkedin" = "tiktok",
  ): Promise<Map<string, { isBoosted: boolean; scheduleEndTime?: string }>> {
    const boostStatusMap = new Map<string, { isBoosted: boolean; scheduleEndTime?: string }>();

    if (!ids || ids.length === 0) {
      logger.info("Boost status check skipped: no IDs provided", { platform });
      return boostStatusMap;
    }

    ids.forEach((id) => boostStatusMap.set(id, { isBoosted: false }));

    try {
      let queryField: string;
      let selectFields: string;

      if (platform === "facebook") {
        queryField = "post_id";
        selectFields = "post_id schedule_end_time companyId";
        logger.info("Checking boost status for Facebook posts", { count: ids.length });
      } else if (platform === "linkedin") {
        queryField = "post_id";
        selectFields = "post_id schedule_end_time companyId";
        logger.info("Checking boost status for LinkedIn posts", { count: ids.length });
      } else {
        queryField = "video_id";
        selectFields = "video_id schedule_end_time companyId";
        logger.info("Checking boost status for TikTok videos", { count: ids.length });
      }

      const ads = await AdModel.find({ [queryField]: { $in: ids } })
        .select(selectFields)
        .lean()
        .exec();

      ads.forEach((ad) => {
        const key = platform === "facebook" ? ad.post_id : platform === "linkedin" ? ad.post_id : ad.video_id;
        if (key) {
          boostStatusMap.set(key, {
            isBoosted: true,
            scheduleEndTime: ad.schedule_end_time,
          });
        } else {
          logger.warn("Found ad record during boost check missing the query field", { platform, adId: ad._id });
        }
      });

      logger.info(`Boost status check completed for ${platform}`, { foundCount: ads.length, totalChecked: ids.length });
      return boostStatusMap;
    } catch (error) {
      logger.error(`Failed to check boost status for ${platform}`, {
        "internal.engage.ads.error": error,
        "internal.engage.ads.ids": JSON.stringify(ids),
        platform,
      });
      throw error;
    }
  }
}
