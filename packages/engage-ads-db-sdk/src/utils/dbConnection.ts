import mongoose from "mongoose";
import dotenv from "dotenv";
import { LambdaLogger } from "@meltwater/lambda-monitoring";

const logger = LambdaLogger.getInstance();

dotenv.config();

const MONGODB_URI = process.env.MONGODB_URI || "your-mongodb-uri";
let isConnected = false;

export const connectToDatabase = async (): Promise<void> => {
  if (isConnected) {
    return;
  }

  try {
    await mongoose.connect(MONGODB_URI, {});
    isConnected = true;
    logger.info("Connected to MongoDB");
  } catch (error) {
    console.error("MongoDB connection error:", error);
    throw error;
  }
};
