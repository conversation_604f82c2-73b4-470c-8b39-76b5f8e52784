{"name": "@meltwater/engage-ads-db-sdk", "version": "1.22.2", "description": "SDK for Engage Ads database operations", "author": "Team Area51", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "test": "jest", "clean": "rimraf dist tsconfig.tsbuildinfo", "seed": "ts-node src/scripts/seedDatabase.ts"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/node": "^20.14.2", "jest": "^29.7.0", "rimraf": "^5.0.1", "ts-jest": "^29.1.4", "typescript": "^5.3.3"}, "dependencies": {"@meltwater/lambda-monitoring": "^0.2.5", "mongoose": "^7.5.1"}, "license": "ISC", "publishConfig": {"access": "restricted", "registry": "https://registry.npmjs.org/"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "modulePathIgnorePatterns": ["<rootDir>/dist/"], "testPathIgnorePatterns": ["<rootDir>/dist/"], "moduleFileExtensions": ["ts", "js"], "transform": {"^.+\\.ts$": "ts-jest"}, "testMatch": ["**/__tests__/**/*.spec.ts"]}}