# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [1.0.6-ENGAGE.1](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-db-sdk@1.0.6-ENGAGE.0...@meltwater/engage-ads-db-sdk@1.0.6-ENGAGE.1) (2024-11-04)

### Features

- adding support for returning scheduleEndTime for boosted ad ([daa1021](https://github.com/meltwater/engage-ads-backend/commit/daa102117175c8f483ed45c184e61866b69c64f3))

## [1.0.6-ENGAGE.0](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-db-sdk@1.0.3...@meltwater/engage-ads-db-sdk@1.0.6-ENGAGE.0) (2024-11-04)

### Bug Fixes

- upgrading version ([713d9a4](https://github.com/meltwater/engage-ads-backend/commit/713d9a42f6fe5b48105ec2849c299d73eba786f6))

### Features

- adding support for checking ad boost status ([a56d35d](https://github.com/meltwater/engage-ads-backend/commit/a56d35df66f50539072508278e03b823e2abd82c))

## 1.0.3 (2024-10-16)

**Note:** Version bump only for package @meltwater/engage-ads-db-sdk

## [1.0.3-ENGAGE.6](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-db-sdk@1.0.3-ENGAGE.5...@meltwater/engage-ads-db-sdk@1.0.3-ENGAGE.6) (2024-10-16)

### Bug Fixes

- updating jest configurationf for database package ([16c2aea](https://github.com/meltwater/engage-ads-backend/commit/16c2aea15e35d297afd1b03052c3422f22cf6108))

## [1.0.3-ENGAGE.5](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-db-sdk@1.0.2-ENGAGE.4...@meltwater/engage-ads-db-sdk@1.0.3-ENGAGE.5) (2024-10-16)

### Bug Fixes

- version upgrade ([3b653b9](https://github.com/meltwater/engage-ads-backend/commit/3b653b95ad73ed061620556ba235beef8099be9b))
- updating tests with mock and replacing mongodb server ([b3546c2](https://github.com/meltwater/engage-ads-backend/commit/b3546c280f0bf0cce45f7913a2849856e9492adc))

## [1.0.2-ENGAGE.4](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-db-sdk@1.0.2-ENGAGE.3...@meltwater/engage-ads-db-sdk@1.0.2-ENGAGE.4) (2024-10-15)

### Features

- adding static methods for mongoose models ([7b5cf74](https://github.com/meltwater/engage-ads-backend/commit/7b5cf743fffd842ae5bfbc45e3385aec514c72cb))

## [1.0.2-ENGAGE.3](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-db-sdk@1.0.2-ENGAGE.2...@meltwater/engage-ads-db-sdk@1.0.2-ENGAGE.3) (2024-10-10)

### Features

- integrating database package with mutation ([77de0db](https://github.com/meltwater/engage-ads-backend/commit/77de0db69d04ae905e2ba1627b89552d6e63f0e3))

## [1.0.2-ENGAGE.2](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-db-sdk@1.0.1-ENGAGE.1...@meltwater/engage-ads-db-sdk@1.0.2-ENGAGE.2) (2024-10-09)

### Bug Fixes

- addressing type checking and linting concerns ([360447d](https://github.com/meltwater/engage-ads-backend/commit/360447d7363668a4b43fdae078a9f2915ac8986e))
- version update ([47b7d83](https://github.com/meltwater/engage-ads-backend/commit/47b7d83d7665b004faef7d2c953860cdb7ed3f33))

### Features

- replacing console logs with logger instance ([0148030](https://github.com/meltwater/engage-ads-backend/commit/014803040dfc5a430f0711433874d919a2ee8c0d))
- updating jest configuration ([eb69f90](https://github.com/meltwater/engage-ads-backend/commit/eb69f90e2d0d5d8bbbb58427a9541813671601bf))
- updating tests to mock mongoose methods ([9fb3edc](https://github.com/meltwater/engage-ads-backend/commit/9fb3edc10cf416fabe3f677a481b0355e42c2199))

## [1.0.1-ENGAGE.1](https://github.com/meltwater/engage-ads-backend/compare/@meltwater/engage-ads-db-sdk@1.0.1-ENGAGE.0...@meltwater/engage-ads-db-sdk@1.0.1-ENGAGE.1) (2024-09-30)

### Bug Fixes

- addressing linting concerns ([3c42e7a](https://github.com/meltwater/engage-ads-backend/commit/3c42e7a8f2d30614fc3fed0b6bc5d661b7ecaf3d))

## 1.0.1-ENGAGE.0 (2024-09-30)

### Features

- initial database package infrastructure ([4ea86ca](https://github.com/meltwater/engage-ads-backend/commit/4ea86cabda131a97651460a66ceff8c5fad77230))
