import { AdGroupModel } from "../src/models";
import type { AdGroupCreateInput } from "../src/types/adGroup";

jest.mock("../src/models", () => require("../__mocks__/models"));
jest.mock("../src/utils/dbConnection", () => ({
  connectToDatabase: jest.fn().mockResolvedValue(undefined),
}));

const adGroupData: AdGroupCreateInput = {
  adgroup_id: "test_adgroup_012",
  adgroup_name: "Test Ad Group",
  advertiser_id: "test_advertiser_789",
  campaign_id: "test_campaign_456",
};

beforeEach(() => {
  jest.clearAllMocks();
});

describe("AdGroupModel Static Methods", () => {
  it("should create an ad group using the static createAdGroup method", async () => {
    // Arrange
    (AdGroupModel.createAdGroup as jest.Mock).mockResolvedValue(adGroupData);

    // Act
    const createdAdGroup = await AdGroupModel.createAdGroup(adGroupData);

    // Assert
    expect(AdGroupModel.createAdGroup).toHaveBeenCalledWith(adGroupData);
    expect(createdAdGroup.adgroup_id).toBe(adGroupData.adgroup_id);
    expect(createdAdGroup.adgroup_name).toBe(adGroupData.adgroup_name);
  });

  it("should retrieve an ad group using findOne", async () => {
    // Arrange
    (AdGroupModel.findOne as jest.Mock).mockReturnValue({
      lean: jest.fn().mockReturnThis(),
      exec: jest.fn().mockResolvedValue(adGroupData),
    });

    // Act
    const retrievedAdGroup = await AdGroupModel.findOne({ adgroup_id: adGroupData.adgroup_id }).lean().exec();

    // Assert
    expect(AdGroupModel.findOne).toHaveBeenCalledWith({ adgroup_id: adGroupData.adgroup_id });
    expect(retrievedAdGroup).not.toBeNull();
    expect(retrievedAdGroup!.adgroup_name).toBe(adGroupData.adgroup_name);
  });

  it("should delete an ad group using deleteOne", async () => {
    // Arrange
    (AdGroupModel.deleteOne as jest.Mock).mockResolvedValue({ deletedCount: 1 });

    // Act
    const deleteResult = await AdGroupModel.deleteOne({ adgroup_id: adGroupData.adgroup_id });

    // Assert
    expect(AdGroupModel.deleteOne).toHaveBeenCalledWith({ adgroup_id: adGroupData.adgroup_id });
    expect(deleteResult.deletedCount).toBe(1);
  });
});
