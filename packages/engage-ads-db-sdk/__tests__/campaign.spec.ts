import { CampaignModel } from "../src/models";

jest.mock("../src/models", () => require("../__mocks__/models"));
jest.mock("../src/utils/dbConnection", () => ({
  connectToDatabase: jest.fn().mockResolvedValue(undefined),
}));

const campaignData = {
  secondary_status: "CAMPAIGN_STATUS_ENABLE",
  objective_type: "VIDEO_VIEWS",
  operation_status: "ENABLE",
  rta_id: null,
  is_new_structure: true,
  is_advanced_dedicated_campaign: false,
  modify_time: new Date().toISOString(),
  budget: 50,
  is_search_campaign: false,
  budget_mode: "BUDGET_MODE_TOTAL",
  campaign_id: "test_campaign_456",
  roas_bid: 0,
  rta_product_selection_enabled: false,
  deep_bid_type: null,
  is_smart_performance_campaign: false,
  create_time: new Date().toISOString(),
  campaign_type: "REGULAR_CAMPAIGN",
  advertiser_id: "test_advertiser_789",
  objective: "LANDING_PAGE",
  campaign_name: "test-120",
};

beforeEach(() => {
  jest.clearAllMocks();
});

describe("CampaignModel Static Methods", () => {
  it("should create a campaign using the static createCampaign method", async () => {
    // Arrange
    (CampaignModel.createCampaign as jest.Mock).mockResolvedValue(campaignData);

    // Act
    const createdCampaign = await CampaignModel.createCampaign(campaignData);

    // Assert
    expect(CampaignModel.createCampaign).toHaveBeenCalledWith(campaignData);
    expect(createdCampaign.campaign_id).toBe(campaignData.campaign_id);
    expect(createdCampaign.campaign_name).toBe(campaignData.campaign_name);
  });

  it("should retrieve a campaign using findOne", async () => {
    // Arrange
    (CampaignModel.findOne as jest.Mock).mockReturnValue({
      lean: jest.fn().mockReturnThis(),
      exec: jest.fn().mockResolvedValue(campaignData),
    });

    // Act
    const retrievedCampaign = await CampaignModel.findOne({ campaign_id: campaignData.campaign_id }).lean().exec();

    // Assert
    expect(CampaignModel.findOne).toHaveBeenCalledWith({ campaign_id: campaignData.campaign_id });
    expect(retrievedCampaign).not.toBeNull();
    expect(retrievedCampaign!.campaign_name).toBe(campaignData.campaign_name);
  });

  it("should delete a campaign using deleteOne", async () => {
    // Arrange
    (CampaignModel.deleteOne as jest.Mock).mockResolvedValue({ deletedCount: 1 });

    // Act
    const deleteResult = await CampaignModel.deleteOne({ campaign_id: campaignData.campaign_id });

    // Assert
    expect(CampaignModel.deleteOne).toHaveBeenCalledWith({ campaign_id: campaignData.campaign_id });
    expect(deleteResult.deletedCount).toBe(1);
  });
});
