import { CampaignModel, AdGroupModel, AdModel } from "../src/models";
import type { TikTokAdCreative } from "../src/types/ad";

jest.mock("../src/models", () => require("../__mocks__/models"));
jest.mock("../src/utils/dbConnection", () => ({
  connectToDatabase: jest.fn().mockResolvedValue(undefined),
}));

const campaignData = {
  secondary_status: "active",
  objective_type: "TRAFFIC",
  operation_status: "enabled",
  rta_id: null,
  is_new_structure: true,
  is_advanced_dedicated_campaign: false,
  modify_time: new Date().toISOString(),
  budget: 1000,
  is_search_campaign: false,
  budget_mode: "daily",
  campaign_id: "test_campaign_456",
  roas_bid: 5,
  rta_product_selection_enabled: true,
  deep_bid_type: null,
  is_smart_performance_campaign: false,
  create_time: new Date().toISOString(),
  campaign_type: "DISPLAY",
  advertiser_id: "test_advertiser_789",
  objective: "Increase Brand Awareness",
  campaign_name: "Test Campaign for Ads",
};

const adGroupData = {
  adgroup_id: "test_adgroup_012",
  adgroup_name: "Test Ad Group for Ad",
  advertiser_id: "test_advertiser_789",
  campaign_id: "test_campaign_456",
  billing_event: "IMPRESSIONS",
  bid_price: 3.0,
  budget: 750,
  budget_mode: "daily",
  schedule_start_time: new Date().toISOString(),
  schedule_end_time: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(),
  optimization_goal: "ENGAGEMENT",
  placements: ["Google", "LinkedIn"],
  location_ids: ["loc_3", "loc_4"],
  pacing: "accelerated",
};

const adData: TikTokAdCreative = {
  ad_id: "test_ad_101112",
  ad_name: "Test Ad",
  advertiser_id: "test_advertiser_789",
  campaign_id: "test_campaign_456",
  adgroup_id: "test_adgroup_012",
  video_id: "7367369947888127278",
};

beforeEach(() => {
  jest.clearAllMocks();
});

describe("AdModel Static Methods", () => {
  it("should create an ad using the static createAd method", async () => {
    // Arrange
    (CampaignModel.createCampaign as jest.Mock).mockResolvedValue(campaignData);
    (AdGroupModel.createAdGroup as jest.Mock).mockResolvedValue(adGroupData);
    (AdModel.createAd as jest.Mock).mockResolvedValue(adData);

    // Act
    await CampaignModel.createCampaign(campaignData);
    await AdGroupModel.createAdGroup(adGroupData);
    const createdAd = await AdModel.createAd(adData);

    // Assert
    expect(CampaignModel.createCampaign).toHaveBeenCalledWith(campaignData);
    expect(AdGroupModel.createAdGroup).toHaveBeenCalledWith(adGroupData);
    expect(AdModel.createAd).toHaveBeenCalledWith(adData);
    expect(createdAd.ad_id).toBe(adData.ad_id);
    expect(createdAd.ad_name).toBe(adData.ad_name);
  });

  it("should retrieve an ad using findOne", async () => {
    // Arrange
    (AdModel.findOne as jest.Mock).mockReturnValue({
      lean: jest.fn().mockReturnThis(),
      exec: jest.fn().mockResolvedValue(adData),
    });

    // Act
    const retrievedAd = await AdModel.findOne({ ad_id: adData.ad_id }).lean().exec();

    // Assert
    expect(AdModel.findOne).toHaveBeenCalledWith({ ad_id: adData.ad_id });
    expect(retrievedAd).not.toBeNull();
    expect(retrievedAd!.ad_name).toBe(adData.ad_name);
  });

  it("should delete an ad using deleteOne", async () => {
    // Arrange
    (AdModel.deleteOne as jest.Mock).mockResolvedValue({ deletedCount: 1 });

    // Act
    const deleteResult = await AdModel.deleteOne({ ad_id: adData.ad_id });

    // Assert
    expect(AdModel.deleteOne).toHaveBeenCalledWith({ ad_id: adData.ad_id });
    expect(deleteResult.deletedCount).toBe(1);
  });
});
