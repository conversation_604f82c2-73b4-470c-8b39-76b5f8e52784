{"name": "meltwater-common", "version": "0.0.1", "dependencies": {"@meltwater/cqrs": "^0.0.1", "@meltwater/engage-ads-graphql-sdk": "^1.0.0", "@meltwater/grimoire-publish-services": "^4.3.0", "@meltwater/lambda-monitoring": "^0.2.0", "jest": "^29.7.0", "mongoose": "^8.6.3"}, "devDependencies": {"@coralogix/esbuild-plugin-node": "^0.0.3", "@meltwater/backend-generator-cli": "^1.0.4", "@meltwater/eslint-config": "^1.2.4", "@meltwater/prettier-config": "^1.0.5", "@types/node": "^20.12.10", "@typescript-eslint/eslint-plugin": "^6.16.0", "@typescript-eslint/parser": "^6.16.0", "esbuild": "^0.21.2", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "husky": "^8.0.0", "lerna": "^8.2.2", "prettier": "^3.2.5", "typescript": "^5.4.5"}, "private": true, "scripts": {"build": "lerna run build", "build:package": "lerna run build:package", "build:package:sinceTag": "lerna run build:package --stream --since $TAG", "clean": "lerna run clean && lerna exec -- rm -rf dist tsconfig.tsbuildinfo", "cleanCache:sinceTag": "lerna run registry:clear-cache --stream --concurrency 1 --since $TAG", "deploy": "lerna run deploy --stream --concurrency 1 --since `git describe --tags --abbrev=0`", "deploy:sinceTag": "lerna run deploy:lambda --stream --since $TAG", "format": "prettier --write .", "format-check": "prettier . --check", "generate:lambda": "backend-generator-cli lambda -o lambda", "install:lerna": "lerna exec -- npm i", "lint": "eslint .", "lint:fix": "eslint . --fix", "prepare": "husky install", "publish:ecr": "lerna run publish:ecr --stream --concurrency 1 --since `git describe --tags --abbrev=0`", "publish:ecr:sinceTag": "lerna run publish:ecr --stream --since $TAG", "publish:packages": "lerna publish from-package --yes", "publish:s3": "lerna run publish:s3 --stream", "publish:s3:sinceTag": "lerna run publish:s3 --stream --since $TAG", "registry:clear-cache": "lerna run registry:clear-cache --stream --concurrency 1 --since TAG_PLACEHOLDER", "test": "lerna run test", "version:prerelease": "lerna version prerelease --yes", "version:release": "lerna version --conventional-commits --conventional-graduate --yes", "version:patch": "lerna version patch --yes"}, "volta": {"node": "20.12.2"}, "workspaces": ["packages/*", "lambda/*"]}