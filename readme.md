# Meltwater Commons

This is a Github Template monorepo for all the common packages used by the Meltwater projects. It also contains the Github Actions used by the projects. It is also responsible for deploying Lambdas and Layer and is built to handle only 1 AWS account.

## Creating a new project from this template

Read this -> https://docs.github.com/en/repositories/creating-and-managing-repositories/creating-a-repository-from-a-template

![Creating a New Repo Image](new-repo.png)

### Steps

1. Click on the Select Repository Template and select meltwater-commons.
2. Fill in the repository name and description.
3. Click on the "Create repository from template" button.
4. Clone the repository to your local machine.
5. Run `npm install` to install all the dependencies.
6. delete all the example folders and files or re-purpose them for your project.
7. Add your own packages and lambdas.
8. Delete the workflow Github actions and then change the ci-cd-example.yml to ci-cd.yml appropriately.

## Lambdas

- [example](lambda/example)

## Adding a New Lambda

Please follow this [readme](lambda/readme.md) instructions

This project sets up lambda using Docker and ECR and uses Typescript and ESBuild to build the lambda.

## Layers

- [example](layers/example)

Follow this guide to add a new layer: https://docs.aws.amazon.com/lambda/latest/dg/packaging-layers.html

## Packages

- [cqrs](packages/cqrs)
- [monitoring](packages/monitoring)

This is where all the npm packges exist and are shared between the projects. The goal of these packages is to be used by other projects. they are small and ususally have a singular purpose. easily testable and integrated with both frontend typescript projects or backend typescript projects.

## Github Actions

- [CI-CD](.github/workflows/ci-cd-example.yml)

for example:

```yaml
name: CI/CD Workflow
on: [push, pull_request]
jobs:
  build:
    uses: meltwater/meltwater-commons/.github/workflows/reusable-ci-cd.yml@main
    with:
      node-version: 20
      beta-release-name: "ENGAGE-"
      enable-ecr: false
      enable-s3: false
      enable-lambda: false
    secrets:
      npm-token: ${{ secrets.NPM_TOKEN }}
      gh-token: ${{ secrets.GH_TOKEN }}
      aws-region: ${{ secrets.AWS_REGION }}
      ecr-repository: ${{ secrets.ECR_REPOSITORY }}
      s3-bucket-name: ${{ secrets.S3_BUCKET_NAME }}
      assume-role-arn: ${{ secrets.ASSUME_ROLE_ARN }}
```

This is the Github Action that is used to deploy the lambdas, layers and Packages. It is triggered on push to the main branch and on a feature branch.

## Local Development

To run the lambdas locally, go to the individual lambda folder and check package.json for the script to run. they ususally will contain a local script to deubg the code.

for example:

```bash
cd lambda/example
npm run local:lambda
```

## Testing

Since this is a monorepo with Lerna, you can run tests on all the packages at once using Jest. To run tests, run the following command.

```bash
npm run test
```

## Limitations

- Does not work with multiple AWS accounts
- Does not work for Lambda Edge functions
- Layers should only install npm packages as dependencies and not add additional custom code.

## Testing engage-ads-graphql lambda locally

To test the engage-ads-graphql lambda locally:

1. Navigate to the lambda directory:
   ```bash
   cd lambda/engage-ads-graphql/
   ```
2. Locate the entry point file src/test/lambda-tester.ts. This file manages the process of testing the lambda locally by sending queries or mutations to the lambda handler.

3. In the src/test/queries directory, you may find JSON files containing test queries.

4. In the src/test/mutations directory, you may find JSON files containing test mutations.

5. Check the package.json file for scripts to test the lambda with specific queries or mutations. For example:

   ```bash
   "test:lambda:tiktokInsights": "ts-node-dev --respawn --transpile-only --no-notify src/test/lambda-tester.ts tiktok-insights.json"
   ```

   If you add new queries or mutations, make sure to add corresponding entries to the scripts section in package.json. For example, if you add a new query file new-query.json, you would add a new script like this:

   ```bash
   "test:lambda:newQuery": "ts-node-dev --respawn --transpile-only --no-notify src/test/lambda-tester.ts new-query.json"
   ```

6. To run a specific test, use the corresponding npm script. For example:

   ```bash
   npm run test:lambda:tiktokInsights
   ```

### Example Query (tiktok-insights.json)

Here's an example of a query file (tiktok-insights.json) that you might use for testing:

```json
{
  "operationName": null,
  "variables": {
    "filter": {
      "startDate": "2022-01-01",
      "endDate": "2022-10-25",
      "size": 25,
      "order": "desc",
      "aggregationType": "sum",
      "startFrom": 0,
      "channels": ["tiktok"],
      "match": {
        "captionKeyword": "test UP with SPLIT OFF"
      }
    }
  },
  "query": "query ($filter: EngageAdsChannelInsightsFilter!) { engageAdsChannelInsights(filter: $filter) { insights { postId channel companyId profileId createTime media { type url } caption likes comments metrics { averageWatchTime videoLength engagements engagementRate reach videoViews fullVideoWatchRate totalTimeWatched shares impressionSources { percentage impressionSource } } } totalCount } }"
}
```
