#!/bin/bash


if [ "$#" -ne 2 ]; then
    echo "Usage: $0 <lambda-name> <version>"
    exit 1
fi

ECR_NAME=$1
VERSION=$2

echo "Publishing Lambda to ECR: $ECR_NAME Version $VERSION to ECR"

# aws ecr get-login-password --region eu-west-1 | docker login --username AWS --password-stdin $ECR_REPOSITORY

docker build --platform linux/arm64 -t $ECR_NAME .

docker tag $ECR_NAME:latest $ECR_REPOSITORY/$ECR_NAME:latest
docker tag $ECR_NAME:latest $ECR_REPOSITORY/$ECR_NAME:$VERSION

docker push --all-tags $ECR_REPOSITORY/$ECR_NAME
