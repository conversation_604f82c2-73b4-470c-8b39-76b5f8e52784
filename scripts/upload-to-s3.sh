#!/bin/bash
set -e

if [ "$#" -ne 3 ]; then
    echo "Usage: $0 SOURCE_PATH and PACKAGE_NAME and version must be set"
    exit 1
fi

SOURCE_PATH=$1
PACKAGE_NAME=$2
VERSION=$3

if [[ -z "$S3_BUCKET_NAME" ]]; then
  echo "Error: S3_BUCKET_NAME must be set as environment variable"
  exit 1
fi

if [[ ! -f "$SOURCE_PATH/app.zip" ]]; then
  echo "Error: $SOURCE_PATH/app.zip does not exist"
  exit 1
fi

aws s3 cp --no-progress "$SOURCE_PATH/app.zip" "s3://$S3_BUCKET_NAME/$PACKAGE_NAME/latest/app.zip"
aws s3 cp --no-progress "$SOURCE_PATH/app.zip" "s3://$S3_BUCKET_NAME/$PACKAGE_NAME/$VERSION/app.zip"
