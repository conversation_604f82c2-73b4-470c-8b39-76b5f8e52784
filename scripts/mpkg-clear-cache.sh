#!/bin/bash

PACKAGE_NAME=$1
VERSION=$2

echo "Clearing cache for package $PACKAGE_NAME version $VERSION"

STATUS_CODE=$(curl --location \
    --request POST 'https://v1.mpkg.meltwater.io/npm/manualHook' \
    --header 'Content-Type:application/json' \
    --data-raw "{\"packageName\":\"$PACKAGE_NAME\", \"version\":\"$VERSION\"}" \
    -w "%{http_code}" \
    -o /dev/null \
    -s)

echo "Response Status Code: $STATUS_CODE"
